import Cookies from 'js-cookie'

const TokenKey = 'token'

export function getToken() {
	return Cookies.get(TokenKey)
}

export function setToken(token) {
	return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
	return Cookies.remove(TokenKey)
}


const idKey = 'channel_merchant_id'

export function getId() {
	return Cookies.get(idKey)
}

export function setId(token) {
	return Cookies.set(idKey, token)
}

export function removeId() {
	return Cookies.remove(idKey)
}