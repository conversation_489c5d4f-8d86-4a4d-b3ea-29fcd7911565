import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
import store from '../store';
import router from '../router';
import _ from 'lodash';

// 2秒内的错误提示都显示一个，5 秒必须提示一次
const debouncedMsg = _.debounce(
  (message) => {
    Message({
      message: message,
      type: 'error',
      duration: 3 * 1000,
    });
  },
  2000,
  { 'leading': true, 'maxWait': 5000 },
);

// 创建axios实例
const service = axios.create({
  baseURL: process.env.BASE_API, // api 的 base_url
  timeout: 20000, // 请求超时时间
  withCredentials: true ,
});

// request拦截器
service.interceptors.request.use(
  (config) => {
    config.headers['Kk-prt-from'] = 'fe2w';
    if (localStorage.getItem('token')) {
      config.headers['Authorization'] = localStorage.getItem('token'); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    let oprtFrom=localStorage.getItem('oprtFrom')
    if(oprtFrom){
      config.headers['Kk-oprt-from'] = oprtFrom
    }
    let kkuuid=localStorage.getItem('kkuuid')
    if(kkuuid){
      config.headers['Kk-Did-Uid'] = kkuuid
    }
    return config;
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error);
  },
);

// response 拦截器
service.interceptors.response.use(
  (response) => {
    /**
     * code为非0是抛错 可结合自己业务进行修改
     */
    const res = response.data;
    if (
      response.config &&
      response.config.params &&
      response.config.params.ignore == '1'
    ) {
      res.code = 200;
    }
    if (res.code != 200) {
      // 401:非法的token;
      if (res.code == '401') {
        MessageBox.confirm(
          '你已被登出，可以取消继续留在该页面，或者重新登录',
          '确定登出',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )
          .then(() => {
            store.dispatch('FedLogOut').then(() => {
              MessageBox.close();
              router.push({
                path: '/login',
                query: {
                  redirect: location.href,
                },
              });
            });
          })
          .catch(() => {
            localStorage.removeItem('token');
            localStorage.removeItem('yximtoken');
            MessageBox.close();
          });
      } else {
        try {
          if (response.config.url.indexOf('/member/product/smsCode') == -1) {
            debouncedMsg(res.message);
          }
        } catch (e) {
          console.log(e);
        }
      }
      // else if (res.code == '888') {
      //   const { nim, store } = window.__xkit_store__;
      //   const imcode = res.data;
      //   let sessionId = `p2p-${imcode}`;
      //   let sence = `p2p`;
      //   if (store.sessionStore.sessions.get(sessionId)) {
      //     store.uiStore.selectSession(sessionId);
      //   } else {
      //     store.sessionStore.insertSessionActive(sence, imcode);
      //   }
      //   this.$store.dispatch('ToggleIM', true);
      // }
      return res;
    } else {
      return res;
    }
  },
  (error) => {
    const { response = {} } = error;
    const { data = {} } = response;
    const status = data.status || data.code || '';
    let msg = JSON.stringify(data);
    if (status.toString().indexOf('5') == 0) {
      msg = '系统维护中，请稍后再试';
    }
    if(msg!='{}'){
      debouncedMsg(msg);
    }else{
      localStorage.removeItem('token');
      localStorage.removeItem('yximtoken');
    }
    // Message({
    //   message: msg,
    //   type: 'error',
    //   duration: 5 * 1000,
    // });
    return {};
  },
);

export default service;
