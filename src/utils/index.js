import BigNumber from 'bignumber.js';
import moment from 'moment';
import storeUtils from '@/store/index.js';
import { getMemberHisKFList, m2kfTalk } from '@/api/kf';
/**
 * 文件转base64
 * @param  file 文件流
 * @param  callback 回调函数
 */
export function fileByBase64(file, callback) {
  let reader = new FileReader();
  // 传入一个参数对象即可得到基于该参数对象的文本内容
  reader.readAsDataURL(file);
  reader.onload = function (e) {
    // target.result 该属性表示目标对象的DataURL
    callback(e.target.result);
  };
}

/**
 *
 * @param urlData  base64
 * @param fileName 文件名称
 * @returns {File}
 */
export function base64ToFile(urlData, fileName) {
  let arr = urlData.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bytes = atob(arr[1]); // 解码base64
  let n = bytes.length;
  let ia = new Uint8Array(n);
  while (n--) {
    ia[n] = bytes.charCodeAt(n);
  }
  return new File([ia], fileName, { type: mime });
}

/**
 * 添加水印
 * @param {blob} file
 * @param {string} el
 * @returns {Promise}
 */
export async function addWaterMarker(file, el = '#markImg') {
  return new Promise(async (resolve, reject) => {
    try {
      // 先压缩和旋转图片
      // file = await compressor(file)
      // 将文件blob转换成图片
      let img = await blobToImg(file);

      // 创建canvas画布
      let canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      let ctx = canvas.getContext('2d');

      // 填充上传的图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // 生成水印图片
      const markEle = document.querySelector(el);
      const markWidth = markEle.clientWidth;
      // const markWidth = canvas.width
      const scale = (canvas.width * 0.25) / markWidth;
      // 先缩放水印再转成图片
      // markEle.style.transform = `scale(${scale})`
      const markImg = await htmlToCanvas(markEle);

      // 填充水印
      ctx.drawImage(
        markImg,
        canvas.width - markImg.width - 15 * scale,
        canvas.height - markImg.height - 15 * scale,
        markImg.width,
        markImg.height
      );

      // 将canvas转换成blob
      canvas.toBlob((blob) => resolve(blob));
    } catch (error) {
      reject(error);
    }
  });
}

function blobToImg(blob) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    reader.addEventListener('load', () => {
      let img = new Image();
      img.src = reader.result;
      img.addEventListener('load', () => resolve(img));
    });
    reader.readAsDataURL(blob);
  });
}

function htmlToCanvas(el, backgroundColor = 'rgba(0,0,0,.1)') {
  return new Promise(async (resolve, reject) => {
    try {
      const markImg = await htmlToCanvas(el, {
        scale: 2, //此处不使用默认值window.devicePixelRatio，需跟移动端保持一致
        allowTaint: false, //允许污染
        useCORS: true,
        backgroundColor, //'transparent'  //背景色
      });
      resolve(markImg);
    } catch (error) {
      reject(error);
    }
  });
}

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
}

const formatTime = (time, str = 'YYYY-MM-DD HH:mm') => {
  if (!time) {
    return time;
  }
  time = new Date(time);
  return moment(time).format(str);
};

// 格式化时间
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf('?') + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {Sting} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

export function param(json) {
  if (!json) return '';
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return '';
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);
    })
  ).join('&');
}

export function param2Obj(url) {
  const search = url.split('?')[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"') +
      '"}'
  );
}

export function html2Text(val) {
  const div = document.createElement('div');
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

export function objectMerge(target, source) {
  /* Merges two  objects,
     giving the last one precedence */

  if (typeof target !== 'object') {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += '' + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

export const pickerOptions = [
  {
    text: '今天',
    onClick(picker) {
      const end = new Date();
      const start = new Date(new Date().toDateString());
      end.setTime(start.getTime());
      picker.$emit('pick', [start, end]);
    },
  },
  {
    text: '最近一周',
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(end.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', [start, end]);
    },
  },
  {
    text: '最近一个月',
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit('pick', [start, end]);
    },
  },
  {
    text: '最近三个月',
    onClick(picker) {
      const end = new Date(new Date().toDateString());
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit('pick', [start, end]);
    },
  },
];

export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'shallowClone');
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

export function createUniqueString() {
  const timestamp = +new Date() + '';
  const randomNum = parseInt((1 + Math.random()) * 65536) + '';
  return (+(randomNum + timestamp)).toString(32);
}

// WAIT_PAY(Arrays.asList(0), "代付款"),
// BOOKED(Arrays.asList(1, 2, 3), "已预订"),
// WAIT_REMIT(Arrays.asList(7, 8, 9, 10), "待汇款"),
// COMPLETED(Arrays.asList(11, 12), "已完成"),
// CANCELED(Arrays.asList(4), "已取消"),
// REFUND(Arrays.asList(6), "已退款");

//  orderStatus
const STATUSMAP = {
  'WAIT_PAY': '待付款',
  'BOOKED': '已预订',
  'WAIT_REMIT': '待汇款',
  'COMPLETED': '已完成',
  'CANCELED': '已取消',
  'REFUND': '已退款',
};
const getStatus = (status) => {
  // 订单状态：0->待付款；1->已预定；2->已发货；3->已完成；4->已关闭；5->无效订单 6->已退款'
  return STATUSMAP[status];
};

const getUrlParams = (url) => {
  const regex = /[?&]([^=#]+)=([^&#]*)/g;
  let obj = {};
  let match;
  while ((match = regex.exec(url))) {
    obj[match[1]] = match[2];
  }
  return obj;
};
const isNumber = (value) => {
  return /^\d+$/.test(value);
};

const onAtMembersExtHandler = function (inputValue, selectedAtMembers) {
  var ext;
  if (selectedAtMembers.length) {
    selectedAtMembers.forEach(function (member) {
      var _a;
      var substr = '@'.concat(member.appellation, ' ');
      var positions = [];
      var pos = inputValue.indexOf(substr);
      while (pos !== -1) {
        positions.push(pos);
        pos = inputValue.indexOf(substr, pos + 1);
      }
      if (positions.length) {
        if (!ext) {
          ext = {
            yxAitMsg:
              ((_a = {}),
              (_a[member.account] = {
                text: substr,
                segments: [],
              }),
              _a),
          };
        } else {
          ext.yxAitMsg[member.account] = {
            text: substr,
            segments: [],
          };
        }
        positions.forEach(function (position) {
          var start = position;
          ext.yxAitMsg[member.account].segments.push({
            start: start,
            end: start + substr.length - 1,
            broken: false,
          });
        });
      }
    });
  }
  return ext;
};
const comparedTo = function (a, b) {
  a = new BigNumber(a);
  b = new BigNumber(b);
  return a.comparedTo(b);
};
const tedianFilter = (text, item) => {
  if (!text) {
    return text;
  }
  // let l = text.length;
  // if (text[l - 1] === '】') {
  //   let lastIndex = text.lastIndexOf('【');
  //   if (lastIndex !== -1) {
  //     // 如果找到了【，则截取到这个【之前的所有字符
  //     text = text.slice(0, lastIndex);
  //   }
  // }
  return text
    .replace(/\[核\]/g, '')
    .replace(/\[绝\]/g, '')
    .replace(/\[钱\]/g, '')
    .replace(/\[<span>核<\/span>\]/g, '')
    .replace(/\[<span>绝<\/span>\]/g, '')
    .replace(/\[<span>钱<\/span>\]/g, '');
};

const isMoreThan12HoursFromNow = (timeStr) => {
  const targetTime = new Date(timeStr);

  if (isNaN(targetTime.getTime())) {
    throw new Error('Invalid date string');
  }

  const now = new Date().getTime();

  const diff = now - targetTime.getTime();
  // todo 改成 12 小时
  const twelveHoursInMilliseconds = 1000 * 60 * 60 * 12;
  return diff > twelveHoursInMilliseconds;
};

const statusDate = [
  {
    name: '查看全部',
    id: undefined,
  },
  {
    name: '待付款',
    id: 'WAIT_PAY',
  },
  {
    name: '已预定',
    id: 'BOOKED',
  },
  {
    name: '已退款',
    id: 'REFUND',
  },
  {
    name: '已完成',
    id: 'COMPLETED',
  },
  {
    name: '已取消',
    id: 'CANCELED',
  },
];

const createProdcutAttach = (data) => {
  const productDetail = data.product;
  const productAttributeList = data.productAttributeList;

  const content = `<div class="spaceBetween msg-flexstart">
    <img src="${productDetail.pic}" class="msg-productImg" />
    <div>
      <div class="twoLine">${productDetail.subTitle.substring(0,30)}</div>
      <div class="msg-red">￥${productDetail.price || ''}</div>
    </div>
  </div>`;
  let type4List = [];
  productAttributeList.forEach((ele) => {
    if (ele.type == 4) {
      type4List.push(ele.name);
    }
  });
  if (productDetail.stock == 0 || productDetail.publishStatus == -2) {
    type4List = [];
  }
  const attach = {
    data: {
      type: 'product',
      productSn: productDetail.productSn,
      productId: productDetail.id,
      productCategoryId: productDetail.productCategoryId,
    },
    body: {
      title: productDetail.subTitle.substring(0,30),
      content,
      type4List,
    },
    type: 'kk_product_msg_fed',
  };
  return attach;
};
const getParamsFromUrl = (url) => {
  return Object.fromEntries(new URL(url).searchParams.entries());
};
const goImPage = (url) => {
  let params = getParamsFromUrl(url);
  if (!params.hasOwnProperty('cateId')) {
    return;
  }
  getMemberHisKFList({
    cateId: params.cateId,
    // productId: this.productId,
  }).then((res) => {
    if (res.code == 200) {
      const findKf = res.data;
      if (findKf) {
        const imcode = findKf;
        const sessionId = `p2p-${imcode}`;
        if (window.__xkit_store__) {
          const { nim, store } = window.__xkit_store__;
          m2kfTalk({
            cateId: params.cateId,
            kfIM: imcode,
          });
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive('p2p', imcode);
          }
          //是否打开消息卡片
          // this.$store.dispatch('ToggleProductCardId', this.productId);
          storeUtils.dispatch('ToggleIM', true);
        }
      } else {
        storeUtils.dispatch('ToggleIM', true);
      }
    }
  });
};

//判断是否存在缓存 如果存在走缓存 不存在就重新请求并设置10分钟缓存
// const cachedFunFlag=(dataKey, timeKey, data, expireMinutes = 10, callback)=>{
//   const getDataKey = localStorage.getItem(dataKey);
//   const getTimeKey = localStorage.getItem(timeKey);
//   const currentTime = new Date().getTime();
//   if(data!=undefined){
//     localStorage.setItem(dataKey, JSON.stringify(data));
//     localStorage.setItem(timeKey, (currentTime + 10 * 60 * 1000).toString());
//     return
//   }
 
    
//   if (getDataKey && getTimeKey && currentTime < parseInt(getTimeKey)) {
//     const parsedData = JSON.parse(getDataKey);
//     // if (typeof callback === 'function') {
//     //   callback(parsedData); 
//     // }
//     // return true;
//     return parsedData
//   }
//   return false; 
// }
const cachedFunFlag = (cacheKey, data, expireMinutes = 10, callback) => {
  const currentTime = Date.now();
  // 写入模式（data有值）
  if (data !== undefined) {
    const cacheData = {
      data: data,
      expireTime: currentTime + expireMinutes * 60 * 1000
    };
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    return;
  }
  const cachedStr = localStorage.getItem(cacheKey);
  if (!cachedStr) return false;
  try {
    const cachedObj = JSON.parse(cachedStr);
    if (currentTime > cachedObj.expireTime) {
      localStorage.removeItem(cacheKey);
      return false;
    }
    return cachedObj;
  } catch (e) {
    return false;
  }
};
export default {
  cachedFunFlag,
  goImPage,
  createProdcutAttach,
  isMoreThan12HoursFromNow,
  statusDate,
  tedianFilter,
  comparedTo,
  onAtMembersExtHandler,
  isNumber,
  getUrlParams,
  getStatus,
  formatTime,
  add(a, b, digits = 2) {
    a = a || 0;
    b = b || 0;
    a = new BigNumber(a);
    b = new BigNumber(b);
    let result = a.plus(b);
    if (result.isInteger()) {
      return result.toNumber();
    }
    return result.toFixed(digits);
  },
  times(a, b, digits = 2) {
    a = new BigNumber(a);
    b = new BigNumber(b);
    let result = a.times(b);
    if (result.isInteger()) {
      return result.toNumber();
    }
    return result.toFixed(digits);
  },
  minus(a, b, digits = 2) {
    a = a || 0;
    b = b || 0;
    a = new BigNumber(a);
    b = new BigNumber(b);
    let result = a.minus(b);
    if (result.isInteger()) {
      return result.toNumber();
    }
    return result.toFixed(digits);
  },
};
