// import { checkToken } from '@/config/api/index.js'

export default function isLogin() {
  var token = localStorage.getItem('token');
  // var token = getToken();
  if (token) {
    return true;
    // checkToken().then((res) => {
    // 	if(res.ret == 0){
    // 		return true;
    // 	}else{
    // 		uni.clearStorageSync('token');
    // 		uni.clearStorageSync('bind_phone');
    // 		return false;
    // 	}
    // })
  } else {
    // removeToken();
    localStorage.removeItem('token');
    localStorage.removeItem('yximtoken');
    return false;
  }
}
