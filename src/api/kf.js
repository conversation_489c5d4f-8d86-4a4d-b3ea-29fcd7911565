import request from '@/utils/request';

export function getKfList(data) {
  return request({
    url: '/mall-portal/home/<USER>/list',
    method: 'post',
    data,
  });
}

export function getBigMemberList(params) {
  return request({
    url: '/mall-portal/home/<USER>/bigMember',
    method: 'get',
    params,
  });
}

export function kfCheck(params) {
  return request({
    url: '/mall-portal/home/<USER>/checkKF',
    method: 'get',
    params,
  });
}

export function checkBU(params) {
  return request({
    url: '/mall-portal/home/<USER>/checkBU',
    method: 'get',
    params,
  });
}

export function checkSK(params) {
  return request({
    url: '/mall-portal/home/<USER>/checkSK',
    method: 'get',
    params,
  });
}

export function getTeamBigMember(params) {
  return request({
    url: '/mall-portal/kkim/team/recovery',
    method: 'post',
    params,
  });
}

export function getMemberHisKFList(params) {
  return request({
    url: '/mall-portal/kkim/m2kf/getMemberHisKFList',
    method: 'get',
    params,
  });
}

export function m2kfTalk(params) {
  return request({
    url: '/mall-portal/kkim/m2kf/talk',
    method: 'get',
    params,
  });
}

export function m2kfSendProduct(params) {
  return request({
    url: '/mall-portal/kkim/m2kf/sendProduct',
    method: 'get',
    params,
  });
}

export function m2kfSendOrder(params) {
  return request({
    url: '/mall-portal/kkim/m2kf/sendOrder',
    method: 'get',
    params,
  });
}

export function flowStepDetail(params) {
  return request({
    url: '/mall-portal/order/flow/flowStepDetail',
    method: 'get',
    params,
  });
}

export function getFlowState(params) {
  return request({
    url: '/mall-portal/order/flow/detail',
    method: 'get',
    params,
  });
}

export function getCategoryAdverList(params) {
  return request({
    url: '/mall-portal/home/<USER>',
    method: 'get',
    params,
  });
}
export function getCategoryAdverList2(params) {
  return request({
    url: '/mall-portal/home/<USER>',
    method: 'get',
    params,
  });
}

export function orderTeam(params) {
  return request({
    url: '/mall-portal/member/product/orderTeam',
    method: 'get',
    params,
  });
}

export function reportAdd(data) {
  return request({
    url: '/mall-portal/member/report/add',
    method: 'post',
    data,
  });
}

export function changeKfList(data) {
  return request({
    url: `/mall-portal/member/changeKfList`,
    method: 'post',
    data,
  });
}

export function detailBySn(params) {
  return request({
    url: '/mall-portal/product/detailBySn',
    method: 'get',
    params,
  });
}

export function getProductCategory(id) {
  return request({
    url: '/mall-portal/home/<USER>/' + id,
    method: 'get',
  });
}
export function negotiaSellerNegoSet(params) {
  return request({
    url: '/mall-portal/negotia/sellerNegoSet',
    method: 'get',
    params,
  });
}
export function getDetail(id) {
  return request({
    url: '/mall-portal/product/detail/' + id,
    method: 'get',
  });
}

export function teamAtKfer(id) {
  return request({
    url: '/mall-portal/member/imtool/teamAtKfer?teamId=' + id,
    method: 'get',
  });
}


