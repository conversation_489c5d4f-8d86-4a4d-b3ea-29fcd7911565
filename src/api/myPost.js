import request from '@/utils/request';

export function getProductList(params) {
  return request({
    url: '/mall-portal/member/product/list',
    method: 'get',
    params,
  });
}

export function publishStatus(params) {
  return request({
    url: '/mall-portal/member/product/update/publishStatus',
    method: 'post',
    params,
  });
}

export function updatePrice(id, params) {
  return request({
    url: `/mall-portal/member/product/updatePrice/${id}`,
    method: 'post',
    params,
  });
}

export function productCaliang(id) {
  return request({
    url: `/mall-portal/member/product/caliang/${id}`,
    method: 'get',
  });
}


export function deleteProduct(id) {
  return request({
    url: `/mall-portal/member/product/delete/${id}`,
    method: 'get',
  });
}

// export function unVerify(id) {
//   return request({
//     url: `/mall-portal/product/unVerify/${id}`,
//     method: 'get',
//   });
// }

export function publishUp(params) {
  return request({
    url: `/mall-portal/member/product/update/publishUp`,
    method: 'get',
    params,
  });
}

export function publishDown(params) {
  return request({
    url: `/mall-portal/member/product/update/publishDown`,
    method: 'get',
    params,
  });
}

export function sameSoldProduct(params) {
  return request({
    url: `/mall-search/kkSearch/sameSoldProduct`,
    method: 'get',
    params,
  });
}

export function sameProductList(params) {
  return request({
    url: `/mall-search/kkSearch/sameProductList`,
    method: 'get',
    params,
  });
}
