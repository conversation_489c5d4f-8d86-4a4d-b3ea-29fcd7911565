import request from '@/utils/request';

export function getList(data) {
  return request({
    url: '/mall-portal/pick/list',
    method: 'post',
    data,
    headers: {
      'Content-type': 'application/json',
    },
  });
}

export function payMacro(data) {
  return request({
    url: '/mall-portal/pick/payMacro',
    method: 'post',
    data,
  });
}

export function payCheck(params) {
  return request({
    url: '/mall-portal/pick/payCheck',
    method: 'get',
    params,
  });
}

export function getMemberMacroOrderList(params) {
  return request({
    url: '/mall-portal/pick/getMemberMacroOrderList',
    method: 'get',
    params,
  });
}

export function orderDetail(params) {
  return request({
    url: '/mall-portal/pick/orderDetail',
    method: 'get',
    params,
  });
}

export function createCode(params) {
  return request({
    url: '/mall-portal/pick/createCode',
    method: 'get',
    params,
  });
}
