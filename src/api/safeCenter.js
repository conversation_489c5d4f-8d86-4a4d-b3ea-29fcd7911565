import request from '@/utils/request';

export function getCertDetail() {
  return request({
    url: '/mall-portal/member/cert/detail',
    method: 'get',
  });
}

export function certAdd(data) {
  return request({
    url: '/mall-portal/member/cert/add',
    method: 'post',
    data,
  });
}

export function initFaceVerify(params) {
  return request({
    url: '/mall-portal/member/cert/initFaceVerify',
    method: 'post',
    params,
    headers: {
      'Content-type': 'application/json',
    },
  });
}

export function describeFaceVerify() {
  return request({
    url: '/mall-portal/member/cert/describeFaceVerify',
    method: 'get',
  });
}
