import request from '@/utils/request';

//用户优惠券列表
export function getCouponList(params) {
  return request({
    url: '/mall-portal/member/coupon/listHistory',
    method: 'get',
    params,
  });
}
//用户当前商品可用优惠券
export function getCouponCanUseByProduct(id) {
  return request({
    url: '/mall-portal/member/coupon/listMemberCouponHisByProduct/' + id,
    method: 'get',
  });
}
//用户当前商品不可用优惠券
export function getCouponCantUseByProduct(id) {
  return request({
    url:
      '/mall-portal/member/coupon/listMemberOtherCouponHistoryByProduct/' + id,
    method: 'get',
  });
}
//用户当前商品相关优惠券
export function getCouponListByProduct(id) {
  return request({
    url: '/mall-portal/member/coupon/listByProduct/' + id,
    method: 'get',
  });
}
