import request from '@/utils/request';

export function getUserinfor<PERSON><PERSON>(params) {
  return request({
    url: '/mall-portal/sso/info',
    method: 'get',
    params,
  });
}

export function updateInfo(params) {
  return request({
    url: '/mall-portal/sso/updateInfo',
    method: 'post',
    params,
  });
}

export function updatePwdApi(params) {
  return request({
    url: '/mall-portal/sso/updatePassword2',
    method: 'post',
    params,
  });
}

export function memberStatics(data) {
  return request({
    url: '/mall-portal/member/statics',
    method: 'post',
    data,
  });
}

export function getHelpDetail(id) {
  return request({
    url: '/mall-portal/home/<USER>/detail/' + id,
    method: 'get',
  });
}

export function anchorListsApi() {
  return request({
    url: '/mall-portal/home/<USER>',
    method: 'get',
  });
}

export function getWxQrcode() {
  return request({
    url: '/mall-portal/member/get_wx_qrcode',
    method: 'get',
  });
}

export function unbindWeixin() {
  return request({
    url: '/mall-portal/member/unbindWeixin',
    method: 'get',
  });
}