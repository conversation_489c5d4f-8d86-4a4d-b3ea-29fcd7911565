import request from '@/utils/request';

export function searchProductList2(params, data) {
  return request({
    url: '/mall-search/kkSearch/search',
    method: 'post',
    data,
    params,
  });
}

export function getProductAttribute(id) {
  return request({
    url: '/mall-portal/home/<USER>/attrInfo3/' + id+'?tagType=2',
    method: 'get',
  });
}

export function getProductCategory(id) {
  return request({
    url: '/mall-portal/home/<USER>/' + id,
    method: 'get',
  });
}
export function getOrderSku(id) {
  return request({
    url: '/mall-portal/home/<USER>/getOrderSku/' + id,
    method: 'get',
  });
}
export function searchRelate(params) {
  return request({
    url: '/mall-search/kkSearch/search/relate',
    method: 'get',
    params,
  });
}
