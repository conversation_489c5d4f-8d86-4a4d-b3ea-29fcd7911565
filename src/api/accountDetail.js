import request from '@/utils/request';

export function getProductCollectionList(params) {
  return request({
    url: '/mall-portal/member/productCollection/list',
    method: 'get',
    params,
  });
}

export function productCollectionDetele(params) {
  return request({
    url: '/mall-portal/member/productCollection/delete',
    method: 'post',
    params,
  });
}

// export function getPreview(id) {
//   return new Promise((reslove, reject) => {
//     request.get(webUrl + '/member/product/preview/' + id).then((res) => {
//       reslove(res);
//     });
//   });
// }

// export function productCollectionAdd(data) {
//   return new Promise((reslove, reject) => {
//     request.post(webUrl + '/member/productCollection/add', data).then((res) => {
//       reslove(res);
//     });
//   });
// }

// export function readHistoryCreate(data) {
//   return new Promise((reslove, reject) => {
//     request.post(webUrl + '/member/readHistory/create', data).then((res) => {
//       reslove(res);
//     });
//   });
// }

// export function getReadHistoryList(params) {
//   return new Promise((reslove, reject) => {
//     request.get(webUrl + '/member/readHistory/list', params).then((res) => {
//       reslove(res);
//     });
//   });
// }

// export function readHistoryDelete(params, data = {}) {
//   return new Promise((reslove, reject) => {
//     request
//       .post(webUrl + '/member/readHistory/delete', data, { ext: params })
//       .then((res) => {
//         reslove(res);
//       });
//   });
// }
