import request from '@/utils/request';

// 手机+密码登录
export function getHomeContentAll(params, from) {
  const config = {
    url: '/mall-portal/home/<USER>',
    method: 'get',
    params,
  };

  if (from) {
    config.headers = {
      'kk-oprt-from': from // 动态设置请求header
    };
  }

  return request(config);
}

export function getSubjectProductList(params) {
  return request({
    url: '/mall-portal/home/<USER>',
    method: 'get',
    params,
  });
}

export function getGameList(params) {
  return request({
    url: '/mall-portal/home/<USER>',
    method: 'get',
    params,
  });
}

export function stsTokenApi() {
  return request({
    url: '/mall-portal/member/oss/policy',
    method: 'get',
  });
}
export function ossCallback(data) {
  return request({
    url: '/mall-portal/member/oss/callback',
    method: 'post',
    data
  });
}

export function getZbList(data) {
  return request({
    url: '/mall-portal/home/<USER>/list',
    method: 'post',
    data,
  });
}

export function getZbDetail(id) {
  return request({
    url: '/mall-portal/home/<USER>/detail/' + id,
    method: 'get',
  });
}

export function getGuess(params) {
  return request({
    url: '/mall-search/kkSearch/recommend',
    method: 'get',
    params,
  });
}

export function topsApi(id) {
  return request({
    url: '/mall-portal/home/<USER>' + id,
    method: 'get',
  });
}

export function newProduct(id) {
  return request({
    url: '/mall-search/kkSearch/newProduct?categoryId=' + id,
    method: 'get',
  });
}

export function detectProduct() {
  return request({
    url: '/mall-search/kkSearch/detectProduct',
    method: 'get',
  });
}

export function topProduct() {
  return request({
    url: '/mall-search/kkSearch/topProduct',
    method: 'get',
  });
}
