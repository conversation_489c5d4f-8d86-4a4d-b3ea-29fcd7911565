import request from '@/utils/request';

export function getDetail(id) {
  return request({
    url: '/mall-portal/product/detail/' + id,
    method: 'get',
  });
}

export function getDetailByCode(params) {
  return request({
    url: '/mall-portal/product/detail',
    method: 'get',
    params,
  });
}

export function getPreview(id) {
  return request({
    url: '/mall-portal/member/product/preview/' + id,
    method: 'get',
  });
}

export function productCollectionAdd(data) {
  return request({
    url: '/mall-portal/member/productCollection/add',
    method: 'post',
    data,
  });
}

export function productCollectionDetele(params) {
  return request({
    url: '/mall-portal/member/productCollection/delete',
    method: 'post',
    params,
  });
}

export function deletCollectApi(o) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/appapi/account/delete_collect', o).then((res) => {
      reslove(res);
    });
  });
}

export function readHistoryCreate(data, params) {
  return request({
    url: '/mall-portal/member/readHistory/create',
    method: 'post',
    data,
    params,
  });
}

export function getReadHistoryList(params) {
  return request({
    url: '/mall-portal/member/readHistory/list',
    method: 'get',
    params,
  });
}

export function readHistoryDelete(params) {
  return request({
    url: '/mall-portal/member/readHistory/delete',
    method: 'post',
    params,
    headers: {
      'Content-type': 'application/json',
    },
  });
}

export function readHistoryClear(params) {
  return request({
    url: '/mall-portal/member/readHistory/clear',
    method: 'post',
    params,
    headers: {
      'Content-type': 'application/json',
    },
  });
}

export function getSkinAndHero(id) {
  return request({
    url: `https://images2.kkzhw.com/mall/statics/wzry/${id}.json`,
    method: 'get',
    params: { ignore: 1 },
  });
}

export function topOfferPrice(params) {
  return request({
    url: '/mall-portal/negotia/topOfferPrice2',
    method: 'post',
    params,
    headers: {
      'Content-type': 'application/json',
    },
  });
}
export function freeNegoOffer(params) {
  return request({
    url: '/mall-portal/negotia/freeNegoOffer',
    method: 'get',
    params,
  });
}


