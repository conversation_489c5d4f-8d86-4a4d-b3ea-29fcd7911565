import request from '@/utils/request';

export function getBuyerList(params) {
  return request({
    url: '/mall-portal/negotia/BuyerList',
    method: 'get',
    params,
  });
}

export function negotiaCancel(params) {
  return request({
    url: '/mall-portal/negotia/cancel',
    method: 'post',
    params,
  });
}

export function generateKKConfirmOrderMyAssess(params) {
  return request({
    url: '/mall-portal/negotia/generateKKConfirmOrder',
    method: 'post',
    params,
  });
}

// export function generateKKOrderMyAssess(params) {
//   return request({
//     url: '/mall-portal/negotia/generateKKOrder',
//     method: 'get',
//     params,
//   });
// }

export function generateKKOrderMyAssess2(params) {
  return request({
    url: '/mall-portal/negotia/generateKKOrder2',
    method: 'get',
    params,
  });
}

export function offerPrice(params) {
  return request({
    url: '/mall-portal/negotia/buyerOfferPrice',
    method: 'post',
    params,
  });
}

export function getSellerList(params) {
  return request({
    url: '/mall-portal/negotia/SellerList',
    method: 'get',
    params,
  });
}

export function sellerDo(params) {
  return request({
    url: '/mall-portal/negotia/sellerDo',
    method: 'post',
    params,
  });
}

export function sellerOfferPrice(params) {
  return request({
    url: '/mall-portal/negotia/sellerOfferPrice',
    method: 'post',
    params,
  });
}

export function negotiaDelete(id) {
  return request({
    url: `/mall-portal/negotia/delete/${id}`,
    method: 'post',
  });
}

export function getNegotiaDetail(id) {
  return request({
    url: `/mall-portal/negotia/detailByTeam?tid=${id}`,
    method: 'get',
  });
}
