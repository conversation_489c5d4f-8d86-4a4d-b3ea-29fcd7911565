import request from '@/utils/request';

export function generateKKConfirmOrder(data) {
  return request({
    url: '/mall-portal/order/generateKKConfirmOrder',
    method: 'post',
    data,
  });
}

export function generateKfKKConfirmOrder(data) {
  return request({
    url: '/mall-portal/order/generateKfKKConfirmOrder',
    method: 'post',
    data,
  });
}


// export function generateKKOrder(data) {
//   return request({
//     url: '/mall-portal/order/generateKKOrder',
//     method: 'post',
//     data,
//   });
// }

export function generateKKOrder2(data) {
  return request({
    url: '/mall-portal/order/generateKKOrder2',
    method: 'post',
    data,
  });
}

export function productConsultationRecords(params) {
  return request({
    url: '/mall-portal/product/consultationRecords',
    method: 'get',
    params,
  });
}


export function myOrderList(params) {
  return request({
    url: '/mall-portal/order/list',
    method: 'get',
    params,
  });
}

export function mySellerList(params) {
  return request({
    url: '/mall-portal/order/sellerList',
    method: 'get',
    params,
  });
}

export function getOrderDetail(id) {
  return request({
    url: '/mall-portal/order/detail/' + id,
    method: 'get',
  });
}

export function cancelUserOrder(data, params) {
  return request({
    url: '/mall-portal/order/cancelUserOrder',
    method: 'post',
    data,
    params,
  });
}

// export function paySuccess(params) {
//   return request({
//     url: '/mall-portal/order/paySuccess',
//     method: 'post',
//     params,
//   });
// }

export function payCheck(params) {
  return request({
    url: '/mall-portal/order/payCheck',
    method: 'get',
    params,
  });
}
export function payCheck2(params) {
  return request({
    url: '/mall-portal/order/payCheck3',
    method: 'get',
    params,
  });
}

export function payCheck3(params) {
  return request({
    url: '/mall-portal/order/payCheck3',
    method: 'get',
    params,
  });
}

export function getOrderTeam(params) {
  return request({
    url: '/mall-portal/order/orderTeam',
    method: 'get',
    params,
  });
}

export function pay(params) {
  return request({
    url: '/mall-portal/kkpay/pay',
    method: 'get',
    params,
  });
}

export function pay2(params) {
  return request({
    url: '/mall-portal/kkpay/pay2',
    method: 'get',
    params,
  });
}

export function pay3(params) {
  return request({
    url: '/mall-portal/kkpay/pay3',
    method: 'get',
    params,
  });
}

export function deleteOrder(orderId) {
  return request({
    url: `/mall-portal/order/deleteOrder/${orderId}`,
    method: 'post',
  });
}
export function payConfig(orderId) {
  return request({
    url: `/mall-portal/member/payConfig`,
    method: 'get',
  });
}
