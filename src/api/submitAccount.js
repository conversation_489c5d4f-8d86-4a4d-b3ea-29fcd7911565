import request from '@/utils/request';

export function getProductAttribute(skuId) {
  return request({
    url: '/mall-portal/home/<USER>/listall/' + skuId,
    method: 'get',
  });
}

export function productCreate(data) {
  return request({
    url: '/mall-portal/member/product/create',
    method: 'post',
    data,
  });
}

export function productUpdate(id, data) {
  return request({
    url: '/mall-portal/member/product/update/' + id,
    method: 'post',
    data,
  });
}

export function productUpdate2(id, data) {
  return request({
    url: '/mall-portal/member/product/updateQuestion/' + id,
    method: 'post',
    data,
  });
}

export function getUpdateInfo(id) {
  return request({
    url: '/mall-portal/member/product/updateInfo/' + id,
    method: 'get',
  });
}

export function sendSm(data) {
  return request({
    url: '/mall-portal/member/product/smsCode',
    method: 'post',
    data,
  });
}

export function startLuhao(data) {
  return request({
    url: '/mall-portal/member/product/startLuhao',
    method: 'post',
    data,
  });
}

export function startLuhao2(data) {
  return request({
    url: '/mall-portal/record/task/start',
    method: 'post',
    data,
  });
}

export function recordTaskDetail(id) {
  return request({
    url: `/mall-portal/record/task/${id}`,
    method: 'get'
  });
}
export function taskUpdate(data) {
  return request({
    url: `/mall-portal/record/task/update`,
    method: 'post',
    data
  });
}

export function recordTaskSmsCode(data) {
  return request({
    url: `/mall-portal/record/task/smsCode`,
    method: 'post',
    data
  });
}