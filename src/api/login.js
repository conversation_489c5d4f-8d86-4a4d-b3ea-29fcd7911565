import request from '@/utils/request';

export function registerApi(params) {
  return request({
    url: '/mall-portal/sso/register',
    method: 'post',
    params,
  });
}

export function sendPhoneCode(params) {
  return request({
    url: '/mall-portal/sso/getAuthCode',
    method: 'get',
    params,
  });
}

export function loginCodenumApi(params) {
  return request({
    url: '/mall-portal/sso/smsLogin',
    method: 'post',
    params,
  });
}

export function loginPwdnumApi(params) {
  return request({
    url: '/mall-portal/sso/login',
    method: 'post',
    params,
  });
}

export function getUserinforApi() {
  return request({
    url: '/mall-portal/sso/info',
    method: 'get',
  });
}

export function deletAccountApi() {
  return request({
    url: '/mall-portal/sso/delete',
    method: 'get',
  });
}

export function logout() {
  return request({
    url: '/mall-portal/sso/logout',
    method: 'post',
  });
}
