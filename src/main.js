import Vue from 'vue';
import VueLazyload from 'vue-lazyload';
import 'normalize.css/normalize.css'; // A modern alternative to CSS resets

import ElementUI from 'element-ui';
import zhLocale from 'element-ui/lib/locale/lang/zh-CN';
import 'element-ui/lib/theme-chalk/index.css';
// import locale from 'element-ui/lib/locale/lang/en'; // lang i18n
const loadimage = require('../static/loading.png');
const errorimage = require('../static/loading.png');
import '@/styles/index.scss'; // global css

import App from './App';
import store from './store';
import router from './router';

import '@/icons'; // icon
import i18n from './lang'; // Internationalization
//import '@/errorLog' // error log
// import '@/permission' // permission control

import vucIdentify from 'vuc-identify';
import moment from 'moment';
import IconFont from '@/components/IconFont';
import scroll from 'vue-seamless-scroll';

ElementUI.Dialog.props.closeOnClickModal.default = false;

Vue.component('IconFont', IconFont);
Vue.filter('formatTime', (time) => {
  if (!time) {
    return time;
  }
  return moment(time).format('YYYY-MM-DD');
});

Vue.filter('formatTimetoSS', (time) => {
  if (!time) {
    return time;
  }

  return moment(time).format('YYYY-MM-DD HH:mm:ss');
});

Vue.filter('tedianFilter', (text) => {
  if (!text) {
    return text;
  }
  return text
    .replace(/\[核\]/g, '')
    .replace(/\[绝\]/g, '')
    .replace(/\[钱\]/g, '');
});
Vue.use(vucIdentify);
Vue.use(scroll);
Vue.use(VueLazyload, {
  preLoad: 1,
  error: errorimage,
  loading: loadimage,
  attempt: 1,
});

/**
 * This project originally used easy-mock to simulate data,
 * but its official service is very unstable,
 * and you can build your own service if you need it.
 * So here I use Mock.js for local emulation,
 * it will intercept your request, so you won't see the request in the network.
 * If you remove `../mock` it will automatically request easy-mock data.
 */
// import '../mock' // simulation data

// import 'quill/dist/quill.core.css'
// import 'quill/dist/quill.snow.css'
// import 'quill/dist/quill.bubble.css'
// import VueQuillEditor from 'vue-quill-editor'

// Vue.use(VueQuillEditor);

/**
 * 懒加载
 */
import VueLazyLoad from 'vue-lazyload';
Vue.use(VueLazyLoad, {
  error: './static/error.png',
  loading: './static/loading.png',
});

/** 省市区 **/
import VueAreaLinkage from 'vue-area-linkage';

Vue.use(VueAreaLinkage);

import Meta from 'vue-meta';
Vue.use(Meta);

Vue.use(ElementUI, {
  zhLocale,
  // i18n: (key, value) => i18n.t(key, value),
});

import './assets/icon/iconfont.css';

// import VueAwesomeSwiper from 'vue-awesome-swiper'
// import './styles/swiper.css'
// Vue.use(VueAwesomeSwiper)
// import 'swiper/css';

Vue.config.productionTip = false;

// import VueRouterSitemap from 'vue-router-sitemap';
//  export const sitemapMiddleware = () => {
//     return (req, res) => {
//         res.set('Content-Type', 'application/xml');
//         const staticSitemap = path.resolve('dist/static', 'sitemap.xml');
//         const filterConfig = {
//             isValid: false,
//             rules: [
//                 /\/example-page/,
//                 /\*/,
//             ],
//         };
//         new VueRouterSitemap(router).filterPaths(filterConfig).build('http://example.com').save(staticSitemap);
//         return res.sendFile(staticSitemap);
//     };
// };
// console.log(sitemapMiddleware())

router.beforeEach((to, from, next) => {
  /*如果本地 存在 token 则 不允许直接跳转到 登录页面*/
  if (to.fullPath == '/login') {
    if (localStorage.getItem('token')) {
      next({
        path: from.fullPath,
      });
    } else {
      next();
    }
  } else {
    if (to.meta.login) {
      if (localStorage.getItem('token')) {
        next();
      } else {
        next({
          path: '/login',
          query: {
            redirect: to.fullPath,
          },
        });
      }
    } else {
      next();
    }
  }
});

//路由跳转后，页面回到顶部
router.afterEach(() => {
  document.body.scrollTop = 0;
  document.documentElement.scrollTop = 0;
});

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: (h) => h(App),
});
