import Cookies from 'js-cookie';
const app = {
  state: {
    sidebar: {
      opened: !+Cookies.get('sidebarStatus'),
      withoutAnimation: false,
    },
    device: 'desktop',
    imstate: false,
    // imok: false,
    showProductCardId: '',
    showOrderCardId: '',
    imunreadcount: 0,
  },
  mutations: {
    SET_IMUNREADCOUNT: (state, imunreadcount) => {
      state.imunreadcount = imunreadcount;
    },
    TOGGLE_ORDERCARD: (state, showOrderCardId) => {
      state.showOrderCardId = showOrderCardId;
    },
    TOGGLE_PRODUCTCARD: (state, showProductCardId) => {
      state.showProductCardId = showProductCardId;
    },
    TOGGLE_IM: (state, imstate) => {
      state.imstate = imstate;
    },
    TOGGLE_SIDEBAR: (state) => {
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1);
      } else {
        Cookies.set('sidebarStatus', 0);
      }
      state.sidebar.opened = !state.sidebar.opened;
      state.sidebar.withoutAnimation = false;
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 1);
      state.sidebar.opened = false;
      state.sidebar.withoutAnimation = withoutAnimation;
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device;
    },
  },
  actions: {
    ToggleSideBar: ({ commit }) => {
      commit('TOGGLE_SIDEBAR');
    },
    CloseSideBar({ commit }, { withoutAnimation }) {
      commit('CLOSE_SIDEBAR', withoutAnimation);
    },
    ToggleDevice({ commit }, device) {
      commit('TOGGLE_DEVICE', device);
    },
    ToggleIM({ commit }, imstate) {
      if (!imstate) {
        commit('TOGGLE_ORDERCARD', '');
        commit('TOGGLE_PRODUCTCARD', '');
      }
      commit('TOGGLE_IM', imstate);
    },
    ToggleOrderCardId({ commit }, showOrderCardId) {
      commit('TOGGLE_ORDERCARD', showOrderCardId);
    },
    ToggleProductCardId({ commit }, showProductCardId) {
      commit('TOGGLE_PRODUCTCARD', showProductCardId);
    },
    setImUnreadCount({ commit }, count) {
      commit('SET_IMUNREADCOUNT', count);
    },
  },
};

export default app;
