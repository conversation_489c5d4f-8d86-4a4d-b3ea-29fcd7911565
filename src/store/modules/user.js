import { getToken } from '@/utils/auth';

const user = {
  state: {
    user: '',
    status: '',
    code: '',
    token: getToken(),
    name: '',
    avatar: '',
    introduction: '',
    roles: [],
    setting: {
      articlePlatform: [],
    },
    nickList: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NICKLIST: (state, list) => {
      state.nickList = list;
    },
  },

  actions: {
    SetToken({ commit }, token) {
      commit('SET_TOKEN', token);
    },
    SetNickList({ commit }, list) {
      commit('SET_NICKLIST', list);
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        localStorage.removeItem('token');
        localStorage.removeItem('yximtoken');
        resolve();
      });
    },
  },
};

export default user;
