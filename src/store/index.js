import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import user from './modules/user';

import { getUserinforApi } from '@/api/index.js';
// import common from './modules/common'
import getters from './getters';

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    imUnreadCount: '0',
    hasLogin: false,
    openid: null,
    userInfo: {
      imaccount: '',
      phone: '',
      username: '',
      vxName: '',
      icon: '',
    },
    flowState: {},
    imOrderDetail: {},
  },
  mutations: {
    login(state, provider) {
      state.hasLogin = true;
    },
    logout(state) {
      state.hasLogin = false;
    },
    setOpenid(state, openid) {
      state.openid = openid;
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo;
    },
    clearUserInfo(state, userInfo) {
      state.userInfo = userInfo;
    },
    imUnreadCount(state, num) {
      state.imUnreadCount = num;
    },
    setFlowState(state, data) {
      state.flowState = data;
    },
    setImOrderDetail(state, data) {
      state.imOrderDetail = data;
    },
  },
  actions: {
    setImUnreadCount({ commit }, num) {
      commit('imUnreadCount', num);
    },
    getUserInfoStore({ commit },{isForcible=false}={}) {
      const userInfoKey = 'userInfo';
      const userInfoExpireKey = 'userInfoExpire';
      
      // 从本地存储获取用户信息和过期时间
      const cachedUserInfo = localStorage.getItem(userInfoKey);
      const expireTime = localStorage.getItem(userInfoExpireKey);
      const currentTime = new Date().getTime();
      
      // 判断缓存是否存在且未过期
      if (cachedUserInfo && expireTime && currentTime < parseInt(expireTime)&&!isForcible) {
        commit('setUserInfo', JSON.parse(cachedUserInfo));
        return;
      }

      
      // 如果缓存不存在或已过期，则请求接口
      getUserinforApi({}).then((res) => {
        if (res.code == 200) {
          commit('setUserInfo', res.data);
          // 存储用户信息和过期时间（10分钟后过期）
          localStorage.setItem(userInfoKey, JSON.stringify(res.data));
          localStorage.setItem(userInfoExpireKey, (currentTime + 10 * 60 * 1000).toString());
        }
      });
    },
    clearUserInfo({ commit }) {
      commit('clearUserInfo', {});
    },
    setFlowState({ commit }, data) {
      commit('setFlowState', data);
    },
    setImOrderDetail({ commit }, data) {
      commit('setImOrderDetail', data);
    },
  },
  modules: {
    app,
    user,
  },
  getters,
});

export default store;
