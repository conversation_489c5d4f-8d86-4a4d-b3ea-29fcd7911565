<template>
  <div style="width: 100%; background: #fffcf8">
    <div class="safe_width spaceBetweenNoAi footer_container">
      <div class="footer_left-logo">
        <img class="footer-logo_pic" src="../../../static/imgs/text_Logo.svg" />

        <!-- src="https://images2.kkzhw.com/mall/statics/pc/LOGO3.png" -->
      </div>
      <!-- <div class="footer_line"></div> -->
      <div style="align-items: flex-start; width: 572px" class="spaceBetween">
        <div class="footer_page_item">
          <div class="footer_page_tit">服务中心</div>
          <router-link class="footer_page_subTit" to="/helpCenter?id=65"
            >帮助中心</router-link
          >
          <router-link class="footer_page_subTit" to="/suggest"
            >投诉建议</router-link
          >
          <router-link class="footer_page_subTit" to="/introduce"
            >关于我们</router-link
          >
          <!-- <a class="footer_page_subTit" href="">我要建议</a>
					<a class="footer_page_subTit" href="">我要建议</a> -->
        </div>
        <div>
          <div class="footer_page_tit">买家指南</div>
          <!-- <div class="footer_page_subTit">如何注册</div> -->
          <router-link class="footer_page_subTit" to="/helpCenter?id=154"
            >如何购买</router-link
          >
          <!-- <div class="footer_page_subTit">搜索账号</div> -->
          <!-- <router-link class="footer_page_subTit" to="/trade/payMent">支付方式</router-link> -->
        </div>
        <div>
          <div class="footer_page_tit">安全交易</div>
          <!-- <div class="footer_page_subTit">钓鱼防骗</div>
					<div class="footer_page_subTit">预防盗号</div> -->
          <router-link to="/helpCenter?id=107" class="footer_page_subTit"
            >法律保护</router-link
          >
          <router-link class="footer_page_subTit" to="/account/approve"
            >实名认证</router-link
          >
          <router-link to="/helpCenter?id=99" class="footer_page_subTit"
            >用户协议</router-link
          >
        </div>
        <div>
          <div class="footer_page_tit">常见问题</div>
          <!-- <div class="footer_page_subTit">如何登录</div> -->
          <router-link class="footer_page_subTit" to="/forgetPwd"
            >忘记密码</router-link
          >
        </div>
      </div>
      <!-- <div class="footer_line"></div>
			<div class="footer_call">
				<div>客服电话</div>
				<div class="call_numer">029-********</div>
				<div class="call_text">24小时人工专属热线</div>
				<img class="fot_renPic" src="../../../static/iden.png">
				<img class="fot_renPic" src="../../../static/pinpai.png">
			</div> -->
    </div>
    <div class="footer_bottom_box">
      <div class="footer_bottom">
        <span style="display: inline-block; width: 560px"
          >公司版权:海口易游盾网络科技有限公司 Copyright © {{ yyyy }} kkzhw.com
          版权所有</span
        >
        <span style="font-family: Inter">
          <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank"
            >琼ICP备**********号-2</a
          >
          <a
            href="https://images2.kkzhw.com/mall/images/20240714/25lkgy_1720929611448.webp"
            target="_blank"
            >琼B2-20221680</a
          ></span
        >
        <span>
          <img src="../../../static/ment.png" />
          <a
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=46010602001859"
            target="_blank"
          >
            琼公网安备46010602001859号
          </a>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      yyyy: new Date().getFullYear(),
    };
  },
  watch: {},
  created() {
    this.getBreadcrumb();
  },
  methods: {
    getBreadcrumb() {},
    goWeChat() {},
    goWeibo() {},
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.footer_container {
  // padding: 64.257px 0;
  height: 190px;
  background: #fffcf8;
  padding-top: 30px;
}
.footer_call {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}
.call_numer {
  font-size: 28px;
  font-weight: 400;
  color: #ff6716;
  padding: 8px 0;
}
.call_text {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}
.footer_line {
  width: 1px;
  height: 91px;
  background: #dcdcdc;
}
.footer_page_tit {
  padding-bottom: 24px;
  color: #ff7a00;
  font-size: 16px;
  font-family: 'PingFang SC';
  letter-spacing: 1.28px;
  font-weight: 500;
}
.footer_page_subTit {
  display: block;
  cursor: pointer;
  // padding-bottom: 10px;
  display: block;
  color: #969696;
  font-size: 14px;
  font-family: 'PingFang SC';
  line-height: 24px;
  font-weight: 400;
  letter-spacing: 0.56px;
  &:hover {
    color: #000;
  }
}
.footer_bottom_box {
  min-width: 1200px;
  max-width: 100%;
  height: 38px;
  background: url('../../../static/imgs/footerBK.png');
  background-size: cover;

  // display: flex;
  // align-items: center;
  // justify-content: center;
}
.footer_bottom {
  // background: #333333;
  width: 1200px;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  // padding: 20px 0 30px;
  // text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  font-family: 'PingFang SC';
  letter-spacing: 0.56px;
  // margin: 0 auto;
  // justify-content: center;
  // background: linear-gradient(
  //   180deg,
  //   rgba(255, 122, 0, 1) 0%,
  //   rgba(255, 199, 0, 0.38) 100%
  // );

  // background: radial-gradient(rgba(255, 122, 0, 1), rgba(255, 199, 0, 0.38));
  height: 38px;
}
.fot_renPic {
  width: 120px;
  display: block;
  margin-bottom: 15px;
  cursor: pointer;
}
.footer_left-logo {
  width: 420px;
  height: 178px;
  .footer-logo_pic {
    width: 166.667px;
    height: 50px;
  }
  p {
    color: #969696;
    font-size: 14px;
    font-family: 'PingFang SC';
    letter-spacing: 0.56px;
  }
  .footer-address {
    margin: 0px;
    margin-top: 20px;
    // margin-bottom: 9.427px;
    letter-spacing: 0.56px;
    font-weight: 400;
    line-height: 24px;
    font-family: 'PingFang SC';
  }
}
.footer-phone {
  margin: 0px;
  margin-top: 9px;
  font-weight: 400;
  line-height: 24px; /* 112.5% */
  letter-spacing: 0.56px;
  font-family: 'PingFang SC';
}
.footer_media_content {
  display: flex;
  align-items: center;
  margin-top: 14.74px;
}
.footer_media {
  width: 36px;
  height: 36px;
  // line-height: 32px;
  // text-align: center;
  background: url(../../../static/imgs/footerKk_media_bk.svg) no-repeat center
    top;
  background-size: cover;
  cursor: pointer;
}
</style>
