<template>
  <div class="zimu_container spaceStart" style="margin-left: 40px">
    <!-- <div class="itemText_comSty">全部游戏</div> -->
    <div class="spaceBetween">
      <div
        v-for="(item, index) in initZimu"
        :class="item == letter ? 'active' : ''"
        :key="index"
        class="zimu_item"
        @click="choseLetter(item)"
      >
        {{ item }}
      </div>
    </div>
  </div>
</template>
<!-- :class="item == letter ? 'active' : ''" -->
<script>
export default {
  data() {
    return {
      initZimu: [
        '全部游戏',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
      ],
      letter: '全部游戏',
    };
  },
  watch: {},
  created() {},
  methods: {
    reset() {
      this.letter = '全部游戏';
    },
    // 字母筛选
    choseLetter(date) {
      this.letter = date;
      this.$emit('chooseLetter', this.letter);
      // this.initGame();
    },
  },
};
</script>

<style scoped>
.zimu_container {
  margin-top: 41px;
  /* width: 100%; */
  width: 838px;
  height: 46px;
  border-radius: 200px;
  background: #f5f5f5;
  font-size: 15.426px;
  box-sizing: border-box;
  /* padding: 0 20px; */
  color: #aeaeae;
  font-family: Inter;
  font-size: 14px;
}
.itemText_comSty {
  font-size: 14px;
  color: #ff6716;
}
.zimu_item {
  /* padding-right: 30px; */
  width: 23.5px;
  height: 23.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-right: 3px;
  border-radius: 50%;
}
.zimu_item:nth-child(1) {
  width: 71px;
  color: #ff7a00 !important;
  background: transparent !important;
  font-size: 16px;
  letter-spacing: 0.64px;
  font-family: 'PingFang SC';
  font-weight: 500;
  margin-right: 20px;
  margin-left: 29px;
}
.zimu_item:hover,
.zimu_item.active {
  color: #fff;
  background: #ffb74a;
}
</style>
