<template>
  <div class="tab-wrap">
    <div :class="tabHeadClass" class="tab-head">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        :class="btnClass(index)"
        :style="btnWidth ? `max-width:${btnWidth}px` : ''"
        class="tab-btn"
        @click="handelTabClick(index)"
      >
        <slot :data="item" :name="`tab-btn-${index}`">
          <img
            v-if="item.imgTitle && activeIndex === index"
            :src="item.imgTitle"
            class="textBtn"
            alt=""
          />
          <div v-else class="border-outside">
            <span>{{ item.title }}</span>
          </div>
        </slot>

        <i
          v-if="index !== tabs.length - 1 && activeIndex === index"
          class="right"
        ></i>
        <i v-if="index !== 0 && activeIndex === index" class="left"></i>
      </div>
    </div>
    <!-- <GradientBorder
      :no-top-left="activeIndex === 0"
      :no-top-right="activeIndex === tabs.length - 1 && tabs.length>1"
      :class="borderClass"
      style="flex:1;height:100%"
    > -->
    <div :class="tabBodyClass" class="tab-body">
      <slot :data="activeIndex" />
    </div>
    <!-- </GradientBorder> -->
  </div>
</template>
<script>
export default {
  name: 'MyTab',
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    borderClass: {
      type: String,
      default: '',
    },
    btnWidth: {
      type: Number,
      default: 0,
    },
    value: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  computed: {
    tabHeadClass() {
      if (this.activeIndex === 0) {
        return 'pd-left-0';
      } else if (this.activeIndex === this.tabs.length - 1) {
        return `pd-right-0`;
      } else return '';
    },
    tabBodyClass() {
      if (this.activeIndex === 0) {
        return 'no-top-left';
      } else if (
        this.activeIndex === this.tabs.length - 1 &&
        this.tabs.length > 1
      ) {
        return `no-top-right`;
      } else return '';
    },
  },
  mounted() {
    this.activeIndex = this.value;
  },
  methods: {
    btnClass(index) {
      const classArr = [];
      if (this.activeIndex === index) {
        classArr.push('selected');
      }
      return classArr;
    },
    handelTabClick(index) {
      this.activeIndex = index;
      this.$emit('click', index);
    },
  },
};
</script>

<style lang="scss" scoped>
$pd-x: 12px;
$b-radius: 28px;
$border-color: rgba(255, 225, 195, 0.8);
$bg-color: #fff;

.tab-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.tab-head {
  // width: 300px !important;
  display: flex;
  justify-items: flex-start;
  align-items: flex-end;
  position: relative;
  z-index: 9;
  padding: 0 $pd-x;

  &.pd-left-0 {
    padding-left: 0;
  }
  &.pd-right-0 {
    padding-right: 0;
  }
}
.tab-btn {
  position: relative;
  flex: 1;
  cursor: pointer;
  text-align: center;
  // color: $uni-color-paragraph;
  color: #ff7a00;
  padding: 18px 0;
  border-radius: $b-radius;
  font-size: 28px;
  line-height: 27px;
  background: #f0edea;
  font-family: YouSheBiaoTiHei;
  z-index: 11;

  &.selected {
    font-family: YouSheBiaoTiHei;
    z-index: 1;
    // color: $uni-color-primary;
    color: #ff7a00;
    font-size: 28px;
    background: $bg-color;
    padding-top: 28px;
    border: solid 1px $border-color;
    border-bottom: none;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom: none;
    z-index: 1;
    bottom: -2px;
    .border-outside {
      background: transparent !important;
    }
  }

  .left {
    &::before {
      // 遮盖三角区域
      content: '';
      position: absolute;
      left: -($b-radius);
      bottom: 0;
      width: $b-radius;
      height: $b-radius;
      background: $bg-color;
    }

    &::after {
      // 画圆角线
      content: '';
      position: absolute;
      left: -($b-radius);
      bottom: 0;
      width: $b-radius;
      height: $b-radius;
      border-bottom-right-radius: $b-radius;
      background: $border-color;
    }
  }

  .right {
    &::before {
      // 遮盖三角区域
      content: '';
      position: absolute;
      right: -($b-radius);
      bottom: 0;
      width: $b-radius;
      height: $b-radius;
      background: $bg-color;
    }

    &::after {
      // 画圆角线
      content: '';
      position: absolute;
      right: -($b-radius);
      bottom: 0;
      width: $b-radius;
      height: $b-radius;
      border-bottom-left-radius: $b-radius;
      background: $border-color;
    }
  }
}
.tab-body {
  background: $bg-color;
  border-radius: $b-radius;
  min-height: 100px;
  height: 100%;
  position: relative;
  // padding: 2px;
  // overflow: hidden;
  box-sizing: border-box;
  border: solid 1px rgba(255, 225, 195, 0.8);
  &.no-top-left {
    border-top-left-radius: 0;

    &::before {
      border-top-left-radius: 0;
    }
  }

  &.no-top-right {
    border-top-right-radius: 0;
  }
}
</style>
