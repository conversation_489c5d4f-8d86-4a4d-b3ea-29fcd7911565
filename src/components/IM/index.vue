<template>
  <div :class="showim ? 'showIm' : 'hideIm'" class="yxim-box">
    <div id="im-box" class="im-box">
      <IMApp v-if="uikitInit" @hide="hideIM" />
    </div>
  </div>
</template>

<script>
import isIMToken from '@/utils/isIMToken';
import IMApp from '@/components/IMApp/index.vue';
export default {
  name: 'Im',
  components: { IMApp },
  props: {
    showim: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      uikitInit: false,
    };
  },
  created() {
    this.initIm();
  },
  methods: {
    hideIM() {
      this.$emit('hideIM');
    },
    initIm() {
      if (location.pathname !== '/kkquse') {
        isIMToken().then((res) => {
          this.uikitInit = res;
        });
      }
    },
  },
};
</script>

<style scoped>
.yxim-box.showIm {
  right: 100px;
}
/* .yxim-box.hideIm {
  right: -3000px;
} */
.yxim-box.hideIm {
  right: -300px;
}
.yxim-box {
  transition: all 0.5s;
  z-index: 1001;
  position: fixed;
  right: 126px;
  top: 50%;
  transform: translate(0px, -50%);
  border-radius: 25.71px;
  background: #fcfcfc;
  box-shadow: -5px -5px 20px 0px rgba(0, 0, 0, 0.2);
  /* height: 600px; */
  /* border-radius: 30px; */
  /* background: #fcfcfc; */
  /* box-shadow: -5px -5px 20px 0px rgba(0, 0, 0, 0.2); */
}
</style>
