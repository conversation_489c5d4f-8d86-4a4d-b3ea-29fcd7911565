<template>
  <el-dialog
    :show-close="false"
    :visible.sync="visible"
    :width="dialogWidth"
    :append-to-body="true"
    custom-class="dialog2_box my-dialog-bk"
    close-on-click-modal
    style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(10px)"
    @close="dialogClone"
  >
    <div class="dialog2">
      <img
        v-if="!logo"
        src="../../../static/imgs/dialog_logo_text.svg"
        class="doalog2_left_logo"
        alt=""
      />
      <img
        v-else
        src="../../../static/imgs/text_Logo.svg"
        class="doalog2_left_logo"
        alt=""
      />

      <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
      <!-- -->
      <div>
        <slot name="right_title"></slot>
      </div>
    </div>
    <div style="position: relative; z-index: 99">
      <slot name="content"></slot>
    </div>
    <img class="dialogBk" src="../../../static/imgs/dialog_bk.png" alt="" />
    <img
      src="../../../static/imgs/dialog_clone.png"
      class="dialog_cloneBtn"
      @click="dialogClone"
    />
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogWidth: {
      type: String,
      default: '557px',
    },
    logo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {};
  },
  methods: {
    dialogClone() {
      this.$emit('dialogClone');
      this.visible = false;
      // this.$router.push({
      //   path: '/login',
      //   query: {
      //     redirect: location.href,
      //   },
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .dialog2_box {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .dialog2_box::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}

::v-deep .dialog2_box .el-dialog__body {
  /* height: 257.1px; */
  padding: 21.425px 28.281px;
  min-height: 320px;
}
::v-deep .dialog2_box .el-dialog__header {
  display: none;
}

.dialog2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.doalog2_left_logo {
  width: 196.253px;
  height: 42.85px;
  position: relative;
  z-index: 22;
}

.forgetClone {
  border-radius: 42px;
  margin-left: 130px;
  margin-top: 36.8px;
  z-index: 22;
  position: relative;
  font-family: 'PingFang SC';
  font-size: 15.4px;
  font-style: normal;
  font-weight: 500;
  border: none !important;
  z-index: 30;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.homeForgetClone {
  margin-top: 22.5px;
}

.dialogBk {
  width: 216px;
  height: 227.962px;
  position: absolute;
  right: 57.53px;
  top: 79.7px;
}
.dialog_cloneBtn {
  width: 46.2px;
  height: 46.2px;
  position: absolute;
  border-radius: 50%;
  top: -27px;
  right: -73.6px;
  cursor: pointer;
}
</style>
