<template>
  <el-dialog
    v-loading.fullscreen.lock="listLoading"
    :show-close="false"
    :visible.sync="visible"
    width="700px"
    custom-class="similarDealDialog_box my-dialog-bk"
    close-on-click-modal
    style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(10px)"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    @close="dialogClone"
  >
    <div class="dialog3">
      <!-- <img
        v-if="!logo"
        src="../../../static/imgs/dialog_logo_text.svg"
        class="doalog2_left_logo"
        alt=""
      /> -->
      <img
        src="../../../static/imgs/text_Logo.svg"
        class="doalog2_left_logo"
        alt=""
      />
      <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
      <!-- -->
      <div>
        <span class="titopText_right_title">近期相似成交</span>
        <!-- <slot name="right_title"></slot> -->
      </div>
    </div>
    <div
      style="position: relative; z-index: 99"
      class="similarDealDialog_content"
    >
      <el-table
        :data="tableData"
        height="430"
        style="width: 100%; border-radius: 24px"
        class="similarDealTable"
      >
        <el-table-column
          align="center"
          prop="productSn"
          label="商品"
          width="180"
        >
          <template slot-scope="scope">
            <img
              :src="scope.row.pic"
              style="width: 60px; height: 60px; border-radius: 9px"
              alt=""
            />
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" prop="price" width="120" label="成交价">
        </el-table-column> -->
        <el-table-column align="center" prop="createTime" label="描述">
          <template slot-scope="scope">
            <!-- {{ util.formatTime(scope.row.createTime, 'YYYY-MM-DD') }} -->
            <!-- :content="scope.row.name" -->
            <el-tooltip
              :content="scope.row.name"
              :visible-arrow="false"
              class="item"
              popper-class="similarPopper"
              effect="light"
              placement="top-start"
            >
              <div
                style="cursor: pointer"
                class="text_linTwo"
                @click="goPage(scope.row.productSn)"
              >
                {{ scope.row.name }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="similar_null_sorry" style="margin-top: 100px">
            <img
              style="width: 54px; height: 56px"
              src="../../../static/imgs/null.png"
              alt=""
            />
            <div
              style="
                margin-left: 15.85px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                text-align: left;
                height: 56px;
              "
            >
              <img
                style="width: 63px; height: 36px"
                src="../../../static/imgs/sorry_text.svg"
                alt=""
              />
              <div class="similar_sorry_text">暂时无相关数据</div>
            </div>
          </div>
        </template>
      </el-table>
      <!-- <div v-if="tableData.length > 0" class="paginationBox">
        <el-pagination
          :total="totalPage"
          layout="pager,jumper"
          class="similarDeal_page_pagination"
          @current-change="pageFun"
        >
        </el-pagination>
      </div> -->
    </div>
    <!-- <div class="similarDealDialog_footerBox">
      <div class="dialogBtn3 solidBtnBk" @click="dialogClone">
        <span>取消</span>
      </div>
      <div class="dialogBtn3 submitBtnBk" @click="submit">
        <span>
          <slot name="button">确认</slot>
        </span>
      </div>
    </div> -->
    <img
      class="similarDealDialogBk"
      src="../../../static/imgs/dialog_bk.png"
      alt=""
    />
    <img
      src="../../../static/imgs/dialog_clone.png"
      class="dialog_cloneBtn"
      @click="dialogClone"
    />
  </el-dialog>
</template>

<script>
import { sameProductList } from '@/api/myPost.js';
import util from '@/utils/index';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    price: {
      type: Number,
      default: 0,
    },
    productCategoryId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      util,
      totalPage: 100,
      pageSize: 4,
      tableData: [],
      tooltipTrue: true,
      listLoading: false,
    };
  },
  watch: {
    visible(newVal, oldVal) {
      console.log(newVal, oldVal);
      if (newVal) {
        this.initData();
      }
    },
  },
  mounted() {},
  methods: {
    initData() {
      this.listLoading = true;
      let data = {
        offDay: 30,
        pageSize: this.pageSize,
        price: this.price,
        productId: this.productCategoryId,
        // price: 1000,
        // productId: 76,
      };

      sameProductList(data)
        .then((res) => {
          if (res.code == 200) {
            // this.initDate = res.data.list;
            this.tableData = res.data.list;
            this.totalPage = res.data.total;
            this.listLoading = false;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页
    pageFun(val) {
      console.log(val);
      this.page = val;
      this.initData();
    },
    goPage(productSn) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${productSn}`,
      });
      window.open(routeUrl.href, '_blank');
    },
    dialogClone() {
      this.$emit('dialogClone');
      // this.visible = false;
      // this.$router.push({
      //   path: '/login',
      //   query: {
      //     redirect: location.href,
      //   },
      // });
    },
    submit() {
      this.$emit('dialogSubmit');
    },
  },
};
</script>
<style>
.similarPopper {
  width: 502px;
  flex-shrink: 0;
  border: none !important;
  border-radius: 24px;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 32px 42px 33px 35px;
  opacity: 1 !important;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
  font-family: 'PingFang SC';
}
</style>
<style lang="scss" scoped>
.similarDealTable::before {
  background: transparent !important;
}
::v-deep .similarDealDialog_box {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .similarDealDialog_box::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}

::v-deep .similarDealDialog_box .el-dialog__body {
  /* height: 257.1px; */
  padding: 25px 33px 32px 29px;
  min-height: 279px;
}
::v-deep .similarDealDialog_box .el-dialog__header {
  display: none;
}

.dialog3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.doalog2_left_logo {
  width: 229px;
  height: 50px;
  position: relative;
  z-index: 22;
}

.forgetClone {
  border-radius: 42px;
  margin-left: 130px;
  margin-top: 36.8px;
  z-index: 22;
  position: relative;
  font-family: 'PingFang SC';
  font-size: 15.4px;
  font-style: normal;
  font-weight: 500;
  border: none !important;
  z-index: 30;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.homeForgetClone {
  margin-top: 22.5px;
}

.similarDealDialogBk {
  width: 350px;
  height: 410px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.dialog_cloneBtn {
  width: 46.2px;
  height: 46.2px;
  position: absolute;
  border-radius: 50%;
  top: -46px;
  right: -42.6px;
}
.titopText_right_title {
  color: #ff720c;
  text-align: right;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px; /* 118.75% */
}
.similarDealDialog_content {
  margin-top: 20px;
  color: #1b1b1b;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
  border-radius: 24px;
}
.similarDealDialog_footerBox {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 99;
  margin-top: 20px;
}
.dialogBtn3 {
  height: 46px;
  padding: 0px 48px;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 60px;
}
.solidBtnBk {
  background: var(--btn-background-gradient);
  position: relative;
  span {
    background: var(--btn-background-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    z-index: 9;
  }
  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    margin: 3px;
    background: #fff;
    border-radius: 60px;
  }
}
.submitBtnBk {
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  color: #fff;
  margin-left: 16px;
}
.paginationBox {
  background: #fff;
  border-radius: 0px 0px 24px 24px;
  height: 60px;
  overflow: hidden;
}
.similarDeal_page_pagination {
  margin-top: 12px;
  position: relative;
}
.similarDeal_page_pagination /deep/ .el-pagination__jump {
  position: absolute;
  right: 43.707px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
}
.similarDeal_page_pagination /deep/ .el-pagination__jump .el-input__inner {
  height: 20px;
  border-radius: 20px;
  border: none;
  background: #f6f6f6;
}
.similarDeal_page_pagination /deep/ ul li {
  color: rgba(0, 0, 0, 0.4);
  min-width: 24px;
  padding: 0px 12px;
}
.similarDeal_page_pagination /deep/ ul .active {
  color: #2d2d2d !important;
}
.similarDeal_page_pagination /deep/ ul li:hover {
  color: #2d2d2d !important;
}
.similar_null_sorry {
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
  background: #fff;
  border-radius: 24px;
}
.similar_null_sorry .sorry_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 27.42px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.similar_null_sorry .similar_sorry_text {
  color: #969696;
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.similarDealTable /deep/ .el-table__header {
  display: block !important;
}
.similarDealTable /deep/ .el-table__cell {
  padding: 12px 0 !important;
  color: #222 !important;
  font-family: 'PingFang SC' !important;
}
.similarDealTable /deep/ .el-table__empty-block {
  display: block !important;
}
</style>
