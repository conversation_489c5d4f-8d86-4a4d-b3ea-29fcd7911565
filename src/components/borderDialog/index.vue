<template>
  <el-dialog
    :show-close="false"
    :visible.sync="visible"
    width="523px"
    custom-class="forgetPwdDialog my-dialog-bk"
    style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(10px)"
  >
    <div class="forgetPwdDialogContent">
      <img
        class="forgetPwdDialogContentImg"
        src="../../../static/imgs/text_Logo.svg"
        alt=""
      />
      <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
      <!-- -->
      <img
        v-if="type === 'edit'"
        style="z-index: 222; width: 120px"
        src="../../../static/imgs/login_change_password.svg"
        alt=""
      />
      <div v-if="type === 'home' && searchIndex === 0" class="colorTitleText">
        安全验证
      </div>
      <div v-if="type === 'home' && searchIndex === 1" class="colorTitleText">
        黑号查询
      </div>
      <div v-if="type === 'home' && searchIndex === 2" class="colorTitleText">
        账号查询
      </div>
    </div>
    <img
      class="forgetPwdDialogBk"
      src="../../../static/imgs/logo_Bk.png"
      alt=""
    />
    <!-- <p class="forgetPwdDialogText">修改成功！快去登录吧</p> -->
    <img
      v-if="type === 'edit'"
      class="forgetPwdDialogText"
      src="../../../static/imgs/login_change_password_success.svg"
      alt=""
    />
    <div v-if="type === 'home'" class="home_dialog_box">
      <div class="bord_color_search spaceCenter">
        <span>{{ value }}</span>
      </div>
      <span
        v-if="searchIndex === 0 && searchCode == 'success'"
        class="homeTipText homeTipTextSuccess"
        >当前账号是看看账号网官方客服</span
      >
      <span
        v-if="searchIndex === 0 && searchCode == 'error'"
        class="homeTipText homeTipTextError"
        >当前帐号非看看帐号网官方客服</span
      >
      <span
        v-if="searchIndex === 1 && searchCode == 'success'"
        class="homeTipText homeTipTextSuccess"
        >经检测，此号码未受过拉黑处理</span
      >
      <span
        v-if="searchIndex === 1 && searchCode == 'error'"
        class="homeTipText homeTipTextError"
        >经检测，此号码受过拉黑处理</span
      >
      <span
        v-if="searchIndex === 2 && searchCode == 'success'"
        class="homeTipText homeTipTextSuccess"
        >此收款账户为官方收款账户</span
      >
      <span
        v-if="searchIndex === 2 && searchCode == 'error'"
        class="homeTipText homeTipTextError"
        >此收款账户为非官方收款账户</span
      >
    </div>
    <el-button
      :class="type === 'edit' ? '' : 'homeForgetClone'"
      class="forgetClone"
      type="primary"
      @click.native.prevent="dialogClone"
    >
      关闭
    </el-button>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    searchIndex: {
      type: Number,
      default: 0,
    },
    searchCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {};
  },
  methods: {
    dialogClone() {
      this.$emit('dialogClone');
      // this.visible = false;
      // this.$router.push({
      //   path: '/login',
      //   query: {
      //     redirect: location.href,
      //   },
      // });
    },
  },
};
</script>

<style scoped>
::v-deep .forgetPwdDialog {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .forgetPwdDialog::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}

::v-deep .forgetPwdDialog .el-dialog__body {
  /* height: 257.1px; */
  padding: 38px 44px;
}
::v-deep .forgetPwdDialog .el-dialog__header {
  display: none;
}
.forgetPwdDialogContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.forgetPwdDialogContentImg {
  width: 135px;
  height: 40px;
  z-index: 22;
}
.forgetPwdDialogContentText {
  font-size: 32.5px;
  font-family: YouSheBiaoTiHei;
  color: #ff720c;
  z-index: 22;
  cursor: pointer;
}
.forgetPwdDialogBk {
  width: 237.173px;
  height: 249.49px;
  position: absolute;
  top: 25px;
  left: 143px;
  z-index: 1;
}
.forgetPwdDialogText {
  width: 258px;
  position: relative;
  margin-left: 93px;
  font-size: 26px;
  color: #ff720c;
  z-index: 22;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  margin-top: 61px;
  margin-bottom: 27px;
}
.forgetClone {
  border-radius: 60px;
  margin-left: 153px;
  letter-spacing: 0.64px;
  margin-top: 28px;
  /* padding: 12px 48px; */
  width: 129px;
  height: 46px;
  z-index: 22;
  position: relative;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  border: none !important;
  z-index: 30;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.homeForgetClone {
  /* margin-top: 28px; */
}
.btn_padding {
  padding: 15.4px 41.136px;
}
.colorTitleText {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-size: 32px;
  position: relative;
  z-index: 9;
  font-family: YouSheBiaoTiHei;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}
.home_dialog_box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 9;
}
.bord_color_search {
  width: 282px;
  height: 46px;
  overflow: hidden;
  white-space: nowrap;
  margin-top: 36px;
  border-radius: 40px;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  position: relative;
  cursor: pointer;
  text-decoration: none;
  color: #fff;
}
.bord_color_search::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  /* background: transparent; */
  line-height: 39px;
  z-index: 0;
  margin: 2px;
  background: #fffcf9;
  position: absolute;
}
.bord_color_search span {
  position: absolute;
  z-index: 10;
  color: #1b1b1b;
  text-align: center;
  font-family: Inter;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: 103%;
  line-height: 24px; /* 109.091% */
  letter-spacing: -0.55px;
}
.homeTipText {
  margin-top: 6px;
  font-size: 16px;
  height: 22px;
  font-family: 'PingFang SC';
  font-weight: 500;
  /* letter-spacing: 0.4px; */
}
.homeTipTextSuccess {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}
.homeTipTextError {
  background: linear-gradient(86.87deg, #ff002e 3.31%, #ffc0c0 142.11%);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}
</style>
