<template>
  <el-dialog
    :show-close="false"
    :visible.sync="visible"
    :width="dialogWidth"
    custom-class="toolDialog_box my-dialog-bk"
    close-on-click-modal
    style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(10px)"
    @close="dialogClone"
  >
    <div class="dialog3">
      <img
        v-if="!logo"
        src="../../../static/imgs/dialog_logo_text.svg"
        class="toolDialog_left_logo"
        alt=""
      />
      <img
        v-else
        src="../../../static/imgs/text_Logo.svg"
        class="toolDialog_left_logo"
        alt=""
      />
      <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
      <!-- -->
      <div>
        <span class="titopText_right_title">商品编辑</span>
        <!-- <slot name="right_title"></slot> -->
      </div>
    </div>
    <div style="position: relative; z-index: 99" class="toolDialog_content">
      <div style="margin-bottom: 10px" class="">
        提示：为了避免商品信息有误，请您认真填写；库存修改过程中，<span
          style="color: #ff720c"
          >有概率出现订单取消，商品库存回退情况</span
        >，请以实际库存为准
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">商品标题</div>
        <div>
          <el-tooltip
            :content="product.subTitle"
            :visible-arrow="false"
            class="item"
            popper-class="toolDialogPopper"
            effect="light"
            placement="top-start"
          >
            <div class="textOneLine">
              {{ product.subTitle }}
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">游戏</div>
        <div class="right_content">{{ product.productCategoryName }}</div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">区服</div>
        <div class="right_content">{{ product.gameAccountQufu }}</div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">物品类型</div>
        <div class="right_content">{{ getAttrTypeTxt('商品类型') }}</div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">商品数量</div>
        <div class="right_content">
          <el-input
            v-model="num"
            :min="0"
            type="number"
            style="width: 200px"
            placeholder="请输入数量"
            @input="num = num < 0 ? 0 : Number(Number(num).toFixed(4))"
          ></el-input>
          <div style="width: 200px">{{ getAttrTypeTxt('单位') }}</div>
        </div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">商品价格</div>
        <div class="right_content">
          <el-input
            v-model="price"
            :min="0"
            type="number"
            placeholder="请输入价格"
            @change="priceChange"
            @input="price = price < 0 ? 0 : Number(Number(price).toFixed(2))"
          ></el-input
          ><span>元</span>
        </div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">商品单价</div>
        <div class="right_content">
          <span
            v-if="price && price != 0 && num && num != 0"
            style="color: rgb(0, 153, 68)"
          >
            1 {{ getAttrTypeTxt('单位') }}={{
              (price / num).toFixed((price / num) % 1 ? 4 : 0)
            }}元，1元={{ (num / price).toFixed((num / price) % 1 ? 4 : 0)
            }}{{ getAttrTypeTxt('单位') }}
          </span>
        </div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">商品库存</div>
        <div class="right_content">
          <el-input
            v-model="inventory"
            :precision="0"
            type="number"
            min="0"
            placeholder="请输入价格"
            @input="
              inventory = inventory < 0 ? 0 : Math.floor(Math.abs(inventory))
            "
          ></el-input>
          <span>件</span>
        </div>
      </div>
      <div class="toolDialog_edit_content">
        <div class="left_title">手续费</div>
        <div class="right_content">
          <span v-if="feeRate.hasOwnProperty('rate')" style="color: #ff720c">预计交易手续费：{{(feeRate.rate *price).toFixed(
                        (feeRate.rate *price) % 1 ? 2 : 0
                      )}} 元</span>
          <span v-else style="color: #ff720c">0元</span>
        </div>
      </div>
    </div>
    <div class="toolDialogBtnBox">
      <div class="toolDialogBtn solidBtnBk" @click="dialogClone">
        <span>取消</span>
      </div>
      <div class="toolDialogBtn submitBtnBk" @click="submit">
        <span>
          <slot name="button">确认</slot>
        </span>
      </div>
    </div>
    <img class="toolDialogBk" src="../../../static/imgs/dialog_bk.png" alt="" />
    <img
      src="../../../static/imgs/dialog_clone.png"
      class="dialog_cloneBtn"
      @click="dialogClone"
    />
  </el-dialog>
</template>

<script>
import {
  getProductAttribute,
  productCreate,
  getUpdateInfo,
  productUpdate,
} from '@/api/submitAccount.js';
import { getProductCategory } from '@/api/search.js';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    right_title: {
      type: Boolean,
      default: false,
    },
    dialogWidth: {
      type: String,
      default: '757px',
    },
    logo: {
      type: String,
      default: '',
    },
    productId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      price: 0,
      num: 0,
      inventory: 0,
      productAttributeValueList:[],
      productAttributeList:[],
      product:{},
      feeRate:{}
    };
  },
  computed: {
      getAttrTypeTxt() {
        return (str) => {
          const str1 = this.productAttributeValueList
            .filter((ele) => [str].includes(ele.productAttributeName))
            .map((ele) => ele.value)
            .join(' ');
          return str1;
        }
    },
  },
  watch: {
    visible(newVal, oldVal) {
      if (newVal) {
        this.initData();
      }
    },
  },
  methods: {
    priceChange(w){
      // if(w>=2500){
      //   this.price=2500
      //   this.$message.error('最大金额2500元')
      // }
    },
    initData() {
      getUpdateInfo(this.productId).then((res) => {
        console.log(res);
        if(res.code==200){
         this.productAttributeValueList=res.data.productAttributeValueList
         this.productAttributeList=res.data.productAttributeList
         this.product=res.data.product
         this.price=res.data.product.price
         this.productAttributeValueList.forEach(item=>{
          if(item.productAttributeName=='数量'){
            this.num=item.value
          }
          if(item.productAttributeName=='发布件数'){
            this.inventory=item.value
          }
         })
        }
        getProductCategory(res.data.product.productCategoryId).then(res=>{
         if(res.code===200){
          let custom =  res.data.custom || '{}';
          custom = JSON.parse(custom);
          this.feeRate = custom.feeRate || {};
         }
        })
      });
    },

    dialogClone() {
      this.$emit('dialogClone');
      // this.visible = false;
      // this.$router.push({
      //   path: '/login',
      //   query: {
      //     redirect: location.href,
      //   },
      // });
    },
    getProductAttributeListType(items,type){
      return this.productAttributeList.find(item=>item.id==items.productAttributeId)[type]
    },
    submit() {
      let newProductAttributeValueList=[]
      this.productAttributeValueList.forEach(item=>{
        let obj={
          id:item.id,
          attriName:item.productAttributeName,
          productAttributeId:item.productAttributeId,
          searchType:this.getProductAttributeListType(item,'searchType'),
          type:this.getProductAttributeListType(item,'type'),
          value:item.value
        }
        if (item.productAttributeName === '数量') {
          obj.value = this.num;
        }
        if (item.productAttributeName === '发布件数') {
          obj.value = this.inventory;
        }
        if (item.productAttributeName === '比例') {
          const ratio = Math.round(
            (this.price / this.num) * 100
          );
          obj.value = ratio;
        }
        if (item.productAttributeName === '比例说明') {
          const str = `1元=${(this.num / this.price).toFixed(
            (this.num / this.price) % 1 ? 4 : 0
          )}${this.getAttrTypeTxt('单位')}<br/> ${(
            this.price / this.num
          ).toFixed(
            (this.price / this.num) % 1 ? 4 : 0
          )}元=1${this.getAttrTypeTxt('单位')}`;
          obj.value = str;
        }
        if (item.productAttributeName === '单位') {
          const str = `${this.getAttrTypeTxt('单位')}`;
          obj.value = str;
        }
        newProductAttributeValueList.push(obj)
      })
      this.product.price=this.price
      this.product.gameGoodsSaletype= 3
      this.product.subTitle= `${this.num}${this.getAttrTypeTxt('单位')}=${this.price}元`
      let data={
        productAttributeValueList:newProductAttributeValueList,
        ...this.product
      }
      productUpdate(this.productId, data).then((response) => {
        if (response.code == 200) {

          this.$message.success('修改成功');
          this.$emit('dialogSubmit');
          // this.afterSunbmit(response);
        }
      });

    },
  },
};
</script>
<style>
.toolDialogPopper {
  width: 502px;
  flex-shrink: 0;
  border: none !important;
  border-radius: 24px;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 32px 42px 33px 35px;
  opacity: 1 !important;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
  font-family: 'PingFang SC';
}
</style>
<style lang="scss" scoped>
::v-deep .toolDialog_box {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .toolDialog_box::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}

::v-deep .toolDialog_box .el-dialog__body {
  /* height: 257.1px; */
  padding: 25px 33px 32px 29px;
  min-height: 279px;
}
::v-deep .toolDialog_box .el-dialog__header {
  display: none;
}

.dialog3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.toolDialog_left_logo {
  width: 229px;
  height: 50px;
  position: relative;
  z-index: 22;
}

.forgetClone {
  border-radius: 42px;
  margin-left: 130px;
  margin-top: 36.8px;
  z-index: 22;
  position: relative;
  font-family: 'PingFang SC';
  font-size: 15.4px;
  font-style: normal;
  font-weight: 500;
  border: none !important;
  z-index: 30;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.homeForgetClone {
  margin-top: 22.5px;
}

.toolDialogBk {
  width: 139.741px;
  height: 147px;
  position: absolute;
  right: 196.63px;
  top: 84px;
}
.dialog_cloneBtn {
  width: 46.2px;
  height: 46.2px;
  position: absolute;
  border-radius: 50%;
  top: -46px;
  right: -42.6px;
}
.titopText_right_title {
  color: #ff720c;
  text-align: right;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px; /* 118.75% */
}
.toolDialog_content {
  margin-top: 30px;
  color: #1b1b1b;
  text-align: left;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
}
.toolDialogBtnBox {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 99;
  margin-top: 40px;
}
.toolDialogBtn {
  height: 46px;
  padding: 0px 48px;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 60px;
}
.solidBtnBk {
  background: var(--btn-background-gradient);
  position: relative;
  span {
    background: var(--btn-background-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    z-index: 9;
  }
  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    margin: 3px;
    background: #fff;
    border-radius: 60px;
  }
}
.submitBtnBk {
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  color: #fff;
  margin-left: 16px;
}
.toolDialog_edit_content {
  &:last-child {
    border-bottom: 1px solid #eee;
  }
  border: 1px solid #eee;
  border-bottom: 0px;
  height: 45px;
  width: 100%;

  display: flex;
  align-items: center;
  .left_title {
    width: 100px;
    padding-right: 10px;
    border-right: 1px solid #eee;
    background: #fafafa;
    height: 43px;
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .right_content {
    height: 45px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    /deep/ .el-input {
      height: 30px;
      margin-right: 10px;
      width: 200px;
      .el-input__inner {
        height: 30px;
        width: 200px;
      }
    }
  }
}
.textOneLine {
  width: 540px;
  height: 43px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 20px;
  line-height: 40px;
}
</style>
