<template>
  <el-dialog
    :show-close="false"
    :visible.sync="visible"
    :width="dialogWidth"
    :append-to-body="true"
    custom-class="dialog3_box my-dialog-bk"
    close-on-click-modal
    style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(10px)"
    @close="dialogClone"
  >
    <div class="dialog3">
      <img
        v-if="!logo"
        src="../../../static/imgs/dialog_logo_text.svg"
        class="doalog2_left_logo"
        alt=""
      />
      <img
        v-else
        src="../../../static/imgs/text_Logo.svg"
        class="doalog2_left_logo"
        alt=""
      />
      <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
      <!-- -->
      <div>
        <span v-if="right_title" class="titopText_right_title">提示</span>
        <!-- <slot name="right_title"></slot> -->
      </div>
    </div>
    <div style="position: relative; z-index: 99" class="dialog3_content">
      <slot name="content"></slot>
    </div>
    <div class="dialogBtnBox3">
      <div class="dialogBtn3 solidBtnBk" @click="dialogClone">
        <span>取消</span>
      </div>
      <div class="dialogBtn3 submitBtnBk" @click="submit">
        <span>
          <slot name="button">确认</slot>
        </span>
      </div>
    </div>
    <img class="dialogBk" src="../../../static/imgs/dialog_bk.png" alt="" />
    <img
      src="../../../static/imgs/dialog_clone.png"
      class="dialog_cloneBtn"
      @click="dialogClone"
    />
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    right_title: {
      type: Boolean,
      default: false,
    },
    dialogWidth: {
      type: String,
      default: '557px',
    },
    logo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {};
  },
  methods: {
    dialogClone() {
      this.$emit('dialogClone');
      // this.visible = false;
      // this.$router.push({
      //   path: '/login',
      //   query: {
      //     redirect: location.href,
      //   },
      // });
    },
    submit() {
      this.$emit('dialogSubmit');
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .dialog3_box {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .dialog3_box::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}

::v-deep .dialog3_box .el-dialog__body {
  /* height: 257.1px; */
  padding: 25px 33px 32px 29px;
  height: 279px;
}
::v-deep .dialog3_box .el-dialog__header {
  display: none;
}

.dialog3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.doalog2_left_logo {
  width: 229px;
  height: 50px;
  position: relative;
  z-index: 22;
}

.forgetClone {
  border-radius: 42px;
  margin-left: 130px;
  margin-top: 36.8px;
  z-index: 22;
  position: relative;
  font-family: 'PingFang SC';
  font-size: 15.4px;
  font-style: normal;
  font-weight: 500;
  border: none !important;
  z-index: 30;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.homeForgetClone {
  margin-top: 22.5px;
}

.dialogBk {
  width: 139.741px;
  height: 147px;
  position: absolute;
  right: 196.63px;
  top: 84px;
}
.dialog_cloneBtn {
  width: 46.2px;
  height: 46.2px;
  position: absolute;
  border-radius: 50%;
  top: -46px;
  right: -42.6px;
}
.titopText_right_title {
  color: #ff720c;
  text-align: right;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px; /* 118.75% */
}
.dialog3_content {
  margin-top: 56px;
  color: #1b1b1b;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
}
.dialogBtnBox3 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 99;
  margin-top: 40px;
}
.dialogBtn3 {
  height: 46px;
  padding: 0px 48px;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 60px;
}
.solidBtnBk {
  background: var(--btn-background-gradient);
  position: relative;
  span {
    background: var(--btn-background-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    z-index: 9;
  }
  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    margin: 3px;
    background: #fff;
    border-radius: 60px;
  }
}
.submitBtnBk {
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  color: #fff;
  margin-left: 16px;
}
</style>
