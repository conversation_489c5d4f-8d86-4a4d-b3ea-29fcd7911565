<template>
  <div class="spaceBetween">
    <div>
      <div class="spaceStart">
        <div class="pushSuccess_title">发布成功</div>
        <!-- &nbsp;&nbsp;
        <img src="../../../static/sart.png" class="colect_icon" /> -->
      </div>
      <div
        style="margin-top: 34.28px"
        class="pushSuccess_orderTable_head spaceBetween"
      >
        <div class="pushSuccess_widthOne">商品信息</div>
        <div class="pushSuccess_widthTwo">单价（¥）</div>
        <div class="pushSuccess_widthThree">编号</div>
        <!-- <div class="widthFive spaceEnd">操作</div> -->
      </div>
      <div
        style="
          padding: 18px 0px 24.1674px 40.27px;
          display: flex;
          align-items: center;
          border-radius: 0px 0px 20.567px 20.567px;
          border: 1px solid #e9e9e9;
          border-top: none;
        "
      >
        <div
          v-if="Object.keys(productObj).length !== 0"
          class="spaceStart pushSuccess_widthOne"
        >
          <div class="pushSuccess_orderShop_pic">
            <el-image
              :src="productObj.pic"
              style="width: 100%; height: 100%; border-radius: 10.28px"
              fit="cover"
            ></el-image>
          </div>
          <!-- <div>
              <div class="orderShop_tit">{{ item.productSn }}</div>
              <div class="orderShop_subT">{{ item.productCategoryName }}</div>
            </div> -->
          <!-- @click="goSellDetail(item)" -->
          <div class="pushSuccesss_order_goods_box" style="cursor: pointer">
            <div class="pushSuccess_orderShop_tit text_linThree">
              {{ productObj.subTitle }}
            </div>
            <div class="spaceStart mid_chennuo">
              <div class="spaceStart mid_chennuo_item">
                <img src="../../../static/imgs/goods_detail_icon1.svg" />

                <div>找回包赔</div>
              </div>
              <div class="spaceStart mid_chennuo_item">
                <!-- <img src="../../../static/d2.png" /> -->
                <img src="../../../static/imgs/goods_detail_icon2.svg" />
                <div>合同保障</div>
              </div>
              <div ref="buyDiv" class="spaceStart mid_chennuo_item">
                <!-- <img src="../../../static/d3.png" /> -->
                <img src="../../../static/imgs/goods_detail_icon3.svg" />
                <div>百人团队</div>
              </div>
            </div>
            <!-- <div class="orderShop_subT">
                游戏区服：{{ item.gameAccountQufu }}
              </div> -->
            <!-- <div class="order_goods_name">
               {{ productObj.productSn }}
            </div> -->
          </div>
        </div>
        <div class="pushSuccess_widthTwo pushSuccess_price">
          {{ productObj.price }}
        </div>
        <div
          style="margin-left: 76px"
          class="pushSuccess_widthThree pushSuccess_productSn"
        >
          {{ productObj.productSn }}
        </div>
      </div>
      <div class="spaceBetweenNoAi" style="padding-top: 20.567px">
        <!-- <div
          v-if="codeNum"
          style="font-weight: 600; font-size: 16px; padding-bottom: 5px"
        >
          您的账号编号：{{ codeNum }}
        </div> -->
        <div class="pushSucc_text">
          审核通过后将自动上架商品，为了更好与您服务及沟通，请注意系统消息;
          <br />
          如有上架相关问题请直接联系上架客服
        </div>

        <div class="spaceEnd">
          <el-button class="pushSuccess_reprovision_btn" @click="checkOrder"
            >查看账号</el-button
          >
          <el-button
            v-if="pushAccountType"
            class="pushSuccess_reprovision_btn"
            @click="goQuestion"
            >完善常见问题</el-button
          >
          <el-button class="pushSuccess_reprovision_btn" @click="goChat"
            >联系客服</el-button
          >
          <div class="pushSuccess_orderTable_btn solid" @click="pushAgain">
            继续发布
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import isLogin from '@/utils/isLogin.js';
export default {
  props: {
    productId: {
      type: String,
      default: '',
    },
    codeNum: {
      type: String,
      default: '',
    },
    gameName: {
      type: String,
      default: '',
    },
    productObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    pushAccountType: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },
  methods: {
    goQuestion() {
      this.$router.push({
        path: '/account/questions?productId=' + this.productId,
      });
    },
    goChat() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      getMemberHisKFList({
        cateId: this.$route.query.productCategoryId,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            let findKf = res.data;
            const { nim, store } = window.__xkit_store__;
            const imcode = findKf;
            const sessionId = `p2p-${imcode}`;
            m2kfTalk({
              cateId: this.$route.query.productCategoryId,
              kfIM: imcode,
            });
            if (store.sessionStore.sessions.get(sessionId)) {
              store.uiStore.selectSession(sessionId);
            } else {
              store.sessionStore.insertSessionActive('p2p', imcode);
            }
            if (this.productId) {
              this.$store.dispatch('ToggleProductCardId', this.productId);
            }
            this.$store.dispatch('ToggleIM', true);
          } else {
            this.$store.dispatch('ToggleIM', true);
          }
        }
      });
    },
    // 继续发布-刷新页面
    pushAgain() {
      this.$router.push({
        path: '/allSell',
      });
    },
    checkOrder() {
      this.$router.push({
        path: '/account/accountList',
      });
    },
  },
};
</script>
<style></style>
<style rel="stylesheet/scss" lang="scss" scoped>
.pushSucc_wrap {
  width: 100%;
  height: 160px;
  background: url(../../../static/pushBg.png) no-repeat center top;
  box-sizing: border-box;
  padding: 0px 30px 0;
  background-size: cover;
  font-size: 22px;
  font-weight: 500;
  color: #812e1e;
}
.ckechDt_push {
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  color: #812e1e;
  padding: 7px 20px;
  border: 1px solid #812e1e;
  border-radius: 18px;
  cursor: pointer;
}
.pushSucc_text {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.4);
}
.pushSuccess_widthOne {
  flex-shrink: 0;
  width: 618px;
}
.pushSuccess_widthTwo {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
  margin-left: 54.27995px;
}
.pushSuccess_widthThree {
  flex-shrink: 0;
  width: 140px;
  margin-left: 63.9003px;
  text-align: center;
}
.pushSuccess_price {
  color: #505050;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.pushSuccess_productSn {
  color: #505050;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.pushSuccess_orderTable_head {
  width: 100%;
  background: #fbf9f7;
  border-bottom: 1px solid #e9e9e9;
  border-radius: 20.567px 20.567px 0px 0px;
  /* padding: 13.712px 24px; */
  padding: 13.712px 78.48406px 13.712px 50.52015px;
  font-size: 17.14px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-weight: 400;
}
.pushSuccess_orderShop_pic {
  width: 204.786px;
  height: 121.5243px;
  margin-right: 22.31628px;
}
.pushSuccesss_order_goods_box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 121.5243px;
  font-family: 'PingFang SC';
}
.pushSuccess_orderShop_tit {
  width: 390.792px;
  color: #222;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.mid_chennuo {
  font-size: 14px;
  color: #505050;
  font-family: 'Noto Sans CJK SC';
  font-weight: 400;
  letter-spacing: 0.64px;
}
.mid_chennuo_item {
  padding: 0 8px;
}
.mid_chennuo_item:nth-child(1) {
  padding-left: 0px;
}
.mid_chennuo_item img {
  width: 28px;
  // margin-right: 3px;
}

.pushSuccess_orderTable_btn {
  font-size: 15.426px;
  color: #ff6716;
  padding: 0px 41.135px !important;
  height: 42.85px;
  font-family: 'PingFang SC';
  letter-spacing: 0.28px;
  font-weight: 500;
  border-radius: 20.56px;
  box-sizing: border-box;
  margin-left: 8.57px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
/*.orderTable_btn:first-child{
	margin-left: 0;
}*/
.pushSuccess_orderTable_btn.solid {
  /* background: linear-gradient(90deg, #ff8c5c, #ff4b67); */
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  color: #fff;
}

.pushSuccess_reprovision_btn {
  height: 42.85px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.28px;
  background: var(--btn-background-gradient) !important;
  position: relative;
  border: none;
  border-radius: 50px !important;
  padding: 0px 41.135px !important;
  margin-left: 8.57px !important;
}
.pushSuccess_reprovision_btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50px; /* 圆角需与主按钮一致 */
  background: #fff; /* 内部背景色 */
  z-index: 1;
  margin: 3px; /* 边框宽度 */
}
.pushSuccess_reprovision_btn /deep/ span {
  position: relative;
  z-index: 2;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.pushSuccess_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
</style>
