<template>
    <div>
      <div v-if="!isUploading" class="picUpload_wrap">
        <div v-if="shCover" class="uploadSingleShCoverBox">
          <div class="animationBox"></div>
            <div class="text">识别中</div>
          </div>
        <el-upload
          ref="upload"
          :action="useOss ? ossUploadUrl : minioUploadUrl"
          :data="useOss ? dataObj : null"
          :multiple="false"
          :on-remove="handleRemove"
          :show-file-list="showFileList"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-preview="handlePreview"
          list-type="picture-card"
          accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
            <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
                <img :src="urlPic" width="100%" alt="" />
            </el-dialog>
            
        <!-- <el-image
          v-if="urlPic"
          :src="urlPic"
          class="picUpload_pic"
          style="width: 100%; height: 100%"
          fit="cover"
        ></el-image>
        <i v-else class="el-icon-plus cover-uploader-icon"></i> -->
      </div>
      <div
        v-else
        class="picUpload_wrap"
        style="font-size: 14px; line-height: 23px"
      >
        <div class="el-loading-spinner">
          <i class="el-icon-loading" style="font-size: 30px"></i>
          <p class="el-loading-text">上传中~</p>
        </div>
      </div>
      <!-- 水印图片 -->
      <img
        ref="waterImg"
        style="width: 0; height: 0"
        src="../../../static/water_pc.png"
        crossorigin="Anonymous"
      />
    </div>
  </template>
  
  <script>
  import ImageCompressor from 'js-image-compressor';
  import { fileByBase64, base64ToFile } from '@/utils';
  import { stsTokenApi } from '@/api/index2.js';
  
  export default {
    props: {
      urlPic: {
        type: String,
        default: '',
      },
      nameKey: {
        type: String,
        default: '',
      },
      needWater: {
        type: Boolean,
        default: true,
      },
      shCover: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        dialogVisible: false,
        dataObj: {
          policy: '',
          signature: '',
          key: '',
          ossaccessKeyId: '',
          dir: '',
          host: '',
          // callback:'',
        },
        isUploading: false, // 单张上传状态
        useOss: true,
        ossUploadUrl: 'https://images2.kkzhw.com',
        minioUploadUrl: 'http://*************:8201/mall-admin/minio/upload',
      };
    },
    computed: {
      imageUrl() {
        return this.urlPic;
      },
      imageName() {
        if (this != null && this.urlPic !== '') {
          return this.urlPic.substr(this.urlPic.lastIndexOf('/') + 1);
        } else {
          return null;
        }
      },
      fileList() {
        return [
          {
            name: this.imageName,
            url: this.imageUrl,
          },
        ];
      },
      showFileList: {
        get: function () {
          return (
            this.urlPic !== null &&
            this.urlPic !== '' &&
            this.urlPic !== undefined
          );
        },
        set: function (newValue) {},
      },
    },
    methods: {
      handlePreview() {
        this.dialogVisible = true;
      },
      handleRemove() {
        this.$emit('handleRemove');
      },
      beforeUpload2(file) {
        return new Promise((resolve, reject) => {
          // 1.调用方法1： 把文件转换为base64字符串
          if (!this.needWater) {
            resolve(file);
          } else {
            fileByBase64(file, async (base64) => {
              // 2. 调用方法2：把base64转换为Canvas
              let tempCanvas = await this.imgToCanvas(base64);
              //3.调用方法3： 写入水印到Canvas
              const canvas = this.addWatermark(tempCanvas, '看看账号网');
              //4. 调用方法4：把Canvas转换为image文件
              const img = this.convasToImg(canvas);
              // const img = this.convasToImg(tempCanvas);
              //5.调用方法5：被image转换为File文件(第二个参数为文件名)
              let newFile = base64ToFile(img.src, file.name);
              resolve(newFile);
            });
          }
        });
      },
      /**
       * Base64转成canvas
       * @param  base64
       */
      async imgToCanvas(base64) {
        // 创建img元素
        const img = document.createElement('img');
        img.setAttribute('src', base64);
        await new Promise((resolve) => (img.onload = resolve));
        // 创建canvas DOM元素，并设置其宽高和图片一样
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        // 坐标(0,0) 表示从此处开始绘制，相当于偏移。
        canvas.getContext('2d').drawImage(img, 0, 0);
  
        return canvas;
      },
      /**
       * canvas添加水印
       * @param  canvas 对象
       * @param text 水印文字
       */
      addWatermark(canvas, text) {
        const ctx = canvas.getContext('2d');
        // 给上传的图片添加-水印图片
        const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
        ctx.fillStyle = pattern;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        // ctx.drawImage(this.$refs.waterImg, 0, 0)
        return canvas;
      },
      /**
       * canvas转成img
       * @param {canvas对象} canvas
       */
      convasToImg(canvas) {
        // 新建Image对象，可以理解为DOM
        let image = new Image();
        // canvas.toDataURL 返回的是一串Base64编码的URL
        // 指定格式 PNG
        image.src = canvas.toDataURL('image/png');
        return image;
      },
  
      handleUploadSuccess(res, file) {
        this.showFileList = true;
        this.fileList.pop();
        let url;
        if (!this.useOss) {
          // 不使用oss直接获取图片路径
          url = res.data.url;
        } else {
          url = this.dataObj.host + '/' + this.dataObj.dir + '/' + file.name;
        }
  
        this.fileList.push({ name: file.name, url: url });
        this.$emit('upSuccsessSingle', url, this.nameKey);
        this.isUploading = false;
      },
      rename(file, fineName) {
        // const timeStamp = new Date().getTime();
        // const name = `${timeStamp}_${file.name}`;
        const copyFile = new File([file], fineName, {
          type: file.type,
        });
        copyFile.uid = file.uid;
        const index = this.$refs.upload.uploadFiles.findIndex((ele) => {
          return ele.uid === file.uid;
        });
        this.$refs.upload.uploadFiles[index].raw = copyFile;
        this.$refs.upload.uploadFiles[index].name = copyFile.name;
        this.$refs.upload.uploadFiles[index].url = URL.createObjectURL(copyFile);
        return copyFile;
      },
  
      /**
       * 封面图片上传OSS-单张
       */
      async beforeUpload(file) {
        let _self = this;
  
        const copyFile = await stsTokenApi()
          .then((response) => {
            const ext = file.name.split('.').pop();
            const fileName = response.data.fileName;
            _self.dataObj.policy = response.data.policy;
            _self.dataObj.signature = response.data.signature;
            _self.dataObj.ossaccessKeyId = response.data.accessKeyId;
            _self.dataObj.key = response.data.dir + `/${fileName}.${ext}`;
            _self.dataObj.dir = response.data.dir;
            _self.dataObj.host = response.data.host;
            const copyFile = this.rename(file, `${fileName}.${ext}`);
            // _self.dataObj.callback = response.data.callback;
            return copyFile;
          })
          .catch((err) => {
            console.log(err);
            return false;
          });
        if (!copyFile) {
          return false;
        }
        this.isUploading = true;
        let blobO = await this.beforeUpload2(copyFile);
        // var str = this.getDayStr();
        // const ext = blobO.name.split('.').pop() || '';
        // 图片名称+时间戳+md5 避免重复
        // const rename = blobO.name.split(ext)[0] + new Date().getTime();
        // var strName = md5(rename) + '.' + ext;
        // let storeAs = 'storage/uploads/pcImg/' + str + '/' + strName; // OSS图片文件存放位置
        // const ossClient = new OSS(this.OSSOptions);
        let newFile = await this.compressionImage(blobO);
  
        return new Promise((resolve, reject) => {
          if (!this.useOss) {
            // 不使用oss不需要获取策略
            resolve(newFile);
          } else {
            resolve(newFile);
          }
        });
      },
      // 生成日期：20230508；做文件名用
      // getDayStr() {
      //   var date = new Date();
      //   var year = date.getFullYear();
      //   var month = date.getMonth() + 1;
      //   var day = date.getDate();
      //   if (month < 10) {
      //     month = '0' + '' + month;
      //   }
      //   if (day < 10) {
      //     day = '0' + '' + day;
      //   }
      //   var str = year + '' + '' + month + '' + day;
      //   return str;
      // },
      // 图片压缩
      compressionImage(file) {
        return new Promise((resolve, reject) => {
          new ImageCompressor({
            file: file,
            quality: 0.4,
            convertSize: 100000, // 1MB 的都要压缩
            redressOrientation: false,
            beforeCompress: function (result) {
              // console.log('压缩之前图片尺寸大小: ', result.size)
              // console.log('mime 类型: ', result.type)
            },
            success: function (result) {
              console.log('压缩之后图片尺寸大小: ', result.size);
              // console.log('mime 类型: ', result.type)
              // console.log('压缩率： ', ((file.size - result.size) / file.size * 100).toFixed(2) + '%')
              let file = new File([result], result.name, { type: result.type });
              resolve(file);
            },
            error(e) {
              reject(e);
            },
          });
        });
      },
    },
    beforeRouteLeave(to, from, next) {
      next();
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .picUpload_wrap .el-upload--picture-card {
    border: 0;
    background: none;
    position: absolute;
    z-index: 4;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
  }
  .picUpload_btn {
    position: absolute;
    z-index: 4;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    opacity: 0;
    cursor: pointer;
  }
  .picUpload_pic {
    position: absolute;
    z-index: 1;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    width: 100%;
    height: 100%;
  }
  .picUpload_wrap {
    width: 148px;
    height: 148px;
    border-radius: 0;
    border: 0;
    position: relative;
    text-align: center;
    line-height: 180px;
    font-size: 28px;
    color: #8c939d;
    overflow: hidden;
    position: relative
  }

  .uploadSingleShCoverBox {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        left: 0;
        top: 0;
        font-size: 14px;
        color: #fff;
        z-index: 99;

        .text {
          z-index: 100; 
          position: relative;
          top: 32px;
        }

        .animationBox {
          position: absolute;
          top: 0;
          width: 100%;
          height: 100%;
          animation: slideDown 1.5s infinite alternate;
          z-index: 9; 
          pointer-events: none; 
          background:linear-gradient(180deg, #f6a04699, #000);
        }

        @keyframes slideDown {
          0% {
            transform: translateY(-6%);
          }
          100% {
            transform: translateY(100%);
          }
        }
      }
  </style>
  