<template>
  <div style="width: 100%">
    <div class="nav_container">
      <div class="safe_width spaceBetween">
        <router-link
          :class="activeIndex == 0 ? 'active' : ''"
          class="nav_item"
          to="/"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('home')"
            @mouseleave="mouseleave6('home')"
          >
            <span v-if="activeIndex == 0 || mouseenterObj.home">首页</span>

            <img
              v-else
              style="width: 32px"
              class="header_text_img"
              src="../../../static/imgs/header_home.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 1 ? 'active' : ''"
          class="nav_item spaceCenter"
          to="/gameList"
          style="font-family: YouSheBiaoTiHei; font-size: 25.71px"
        >
          <div
            class="mouseenterContent spaceCenter"
            @mouseenter="mouseenter6('buy')"
            @mouseleave="mouseleave6('buy')"
          >
            <div class="footer_media spaceCenter">
              <IconFont
                v-if="activeIndex !== 1"
                :size="21"
                style="margin: 0"
                icon="maihao"
              />
              <img
                v-if="activeIndex == 1 || mouseenterObj.buy"
                style="width: 28px; height: 28px"
                src="../../../static/imgs/header_buy_icon.svg"
                alt=""
              />
            </div>
            <span
              v-if="activeIndex == 1 || mouseenterObj.buy"
              style="letter-spacing: 0px"
              >我要买</span
            >
            <img
              v-else
              style="width: 72px; margin: 0px"
              class="header_text_img"
              src="../../../static/imgs/header_buy.svg"
              alt=""
            />
          </div>
        </router-link>
        <!-- c active222-->
        <!-- :class="activeIndex == 2 ? 'active' : ''"  -->
        <el-dropdown :class="activeIndex == 2 ? 'active' : ''" class="nav_item">
          <div
            class="mouseenterContent spaceCenter cursor"
            style="
              font-family: YouSheBiaoTiHei;
              font-size: 25.71px;
              height: 73px;
              background: transparent;
              border-radius: 50px 50px 0px 0px;
            "
            @mouseenter="mouseenter6('sell')"
            @mouseleave="mouseleave6('sell')"
          >
            <div class="footer_media spaceCenter">
              <IconFont
                v-if="activeIndex !== 2"
                :size="21"
                style="margin: 0"
                icon="woyaomai"
              />
              <img
                v-if="activeIndex == 2 || mouseenterObj.sell"
                style="width: 28px; height: 28px"
                src="../../../static/imgs/header_sell_icon.svg"
                alt=""
              />
            </div>
            <!-- v-if="activeIndex == 2 || mouseenterObj.sell" -->
            <div
              v-if="activeIndex == 2 || mouseenterObj.sell"
              style="width: 99px; text-align: left"
            >
              <div>我要卖</div>
              <div style="position: absolute; top: -3.2px; right: 20px">
                <!-- <i
                  style="font-size: 18px; color: #fff"
                  class="el-icon-arrow-down el-icon--right"
                ></i> -->
                <img
                  style="width: 12px"
                  src="../../../static/imgs/header_arrow.svg"
                  alt=""
                />
              </div>
            </div>

            <img
              v-else
              style="width: 99px; margin-top: -2px"
              class="header_text_img"
              src="../../../static/imgs/header_sell.svg"
              alt=""
            />
          </div>
          <div
            @mouseenter="mouseenter6('sell')"
            @mouseleave="mouseleave6('sell')"
          >
            <el-dropdown-menu
              slot="dropdown"
              :append-to-body="false"
              style="
                width: 174px;
                padding: 20px 14px;
                text-align: center;
                border-radius: 0px 0px 20px 20px;
                box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
                font-size: 16px;
                margin-top: 7px;
              "
            >
              <el-dropdown-item
                @mouseenter.native="mouseenter6('sell_account')"
                @mouseleave.native="mouseleave6('sell_account')"
              >
                <router-link class="blockAll" to="/allSell">
                  <div>
                    <img
                      v-if="mouseenterObj.sell_account"
                      style="width: 68px; margin-top: -3px; margin-left: -1px"
                      src="../../../static/imgs/header_btn_sell_account.svg"
                      alt=""
                    />

                    <div v-else>我要卖号</div>
                  </div>
                </router-link>
              </el-dropdown-item>
              <el-dropdown-item
                @mouseenter.native="mouseenter6('recall')"
                @mouseleave.native="mouseleave6('recall')"
              >
                <router-link class="blockAll" to="/recyle">
                  <div>
                    <img
                      v-if="mouseenterObj.recall"
                      style="width: 69px; margin-top: -4px; margin-left: -1.5px"
                      src="../../../static/imgs/header_btn_recall.svg"
                      alt=""
                    />
                    <span v-else>号商回收</span>
                  </div>
                </router-link>
              </el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </el-dropdown>

        <router-link
          :class="activeIndex == 3 ? 'active' : ''"
          class="nav_item"
          to="/assure"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('intermediary')"
            @mouseleave="mouseleave6('intermediary')"
          >
            <span v-if="activeIndex == 3 || mouseenterObj.intermediary"
              >中介担保</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_intermediary.svg"
              alt=""
            />
          </div>
        </router-link>

        <router-link
          :class="activeIndex == 4 ? 'nav_item active' : 'nav_item'"
          to="/appraisal"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('account')"
            @mouseleave="mouseleave6('account')"
          >
            <span v-if="activeIndex == 4 || mouseenterObj.account"
              >账号估价</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_account.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 6 ? 'active' : ''"
          class="nav_item"
          to="/helpCenter"
          rel="nofollow"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('hand')"
            @mouseleave="mouseleave6('hand')"
          >
            <span v-if="activeIndex == 6 || mouseenterObj.hand">帮助中心</span>

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_center.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 7 ? 'active' : ''"
          to="/account/center"
          rel="nofollow"
          class="nav_item"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('persons')"
            @mouseleave="mouseleave6('persons')"
          >
            <span v-if="activeIndex == 7 || mouseenterObj.persons"
              >个人中心</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_persons.svg"
              alt=""
            />
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    activeIndex: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      mouseenterObj: {
        'home': false,
        'buy': false,
        'sell': false,
        'intermediary': false,
        'account': false,
        'hand': false,
        'persons': false,
        'sell_account': false,
        'recall': false,
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    mouseenter6(v) {
      this.mouseenterObj[v] = true;
    },
    mouseleave6(v) {
      console.log('执行了', v);

      this.mouseenterObj[v] = false;
    },
  },
  beforeRouteLeave(to, from, next) {
    next();
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.topPage_tit {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #666666;
  padding: 12px 0;
}

.search_header_ipt .el-input__inner {
  border: none;
  padding-left: 0;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-left: 10px;
  width: 300px;
}
.search_header_btn {
  color: #ff6716;
  width: 20px;
  font-size: 20px;
  margin-right: 20px;
  cursor: pointer;
}
.nav_container1 {
  width: 1200px;
  height: 68px;
  background: #fff;
  // box-shadow: 6px 0px 10px 1px rgba(179, 179, 179, 0.46);
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 58px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  margin-top: 20px;
  padding: 0 13px;
}
.nav_container {
  width: 1200px;
  height: 68px;
  background: #fff;
  // box-shadow: 6px 0px 10px 1px rgba(179, 179, 179, 0.46);
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 58px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  margin-top: 20px;
  padding: 0 13px;
}
.nav_item {
  transition: all 0.3s;
  cursor: pointer;
  font-size: 16px;
  font-family: 'PingFang SC';
  color: #ff720c;
  line-height: 46px;
  height: 46px;
  // width: 200px;
  min-width: 166px;
  text-align: center;
  position: relative;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.56px;
}
.nav_item.active,
.nav_item:hover {
  color: #fff;
  background: url(../../../static/imgs/header_nav_bk.svg);
  background-size: cover;
  border-radius: 50px;
}
.right_userH {
  font-size: 13px;
  color: #333333;
}
.nav_itemSeaech {
  font-size: 14px;
  color: #333333;
  line-height: 52px;
  height: 52px;
  min-width: 114px;
  text-align: center;
  position: relative;
  flex-shrink: 0;
}
.search_topBtn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
  height: 54px;
  line-height: 54px;
  padding: 0 30px;
  margin-left: 15px;
  border-radius: 10px;
}
.footer_media {
  width: 28px;
  height: 28px;
  // line-height: 32px;
  margin-right: 6px;
  // text-align: center;
  background: url(../../../static/imgs/footerKk_media_bk.svg) no-repeat center
    top;
  background-size: cover;
}
.mouseenterContent {
  width: 100%;
  height: 100%;
}
.header_text_img {
  width: 68px;
  margin-top: -3px;
}
</style>
