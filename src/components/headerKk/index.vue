<template>
  <div style="width: 100%">
    <!-- <router-link to="/account/center" rel="nofollow" v-if="userInfo.id" style="padding-right: 15px;">会员中心</router-link> -->
    <!-- <div class="dark_container">
      <div class="safe_width spaceBetween">
        <div class="topPage_tit">您好，欢迎来到看看账号网游戏交易平台！</div>
        <div class="spaceEnd right_userH">
         
          <span v-if="userInfo && userInfo.username">{{
            filterName(userInfo.username || '')
          }}</span>
          <span v-if="!userInfo.id">
            请先 <router-link to="/login" rel="nofollow">登录</router-link> 或者
            <router-link style="padding-right: 15px" to="/regin" rel="nofollow"
              >免费注册</router-link
            >
          </span>
          <span
            v-else
            class="cursor"
            style="padding-right: 15px; margin-left: 15px"
            @click="loginOut"
          >
            退出登录</span
          >
        </div>
      </div>
    </div> -->

    <div style="height: 90px" class="white_container spaceAlignCenter">
      <div class="safe_width">
        <div
          style="padding: 0"
          class="spaceBetween pdTopBottom headerKkContent"
        >
          <router-link to="/">
            <img
              src="../../../static/imgs/text_Logo.svg"
              class="home_logo_pic"
            />
          </router-link>

          <div class="spaceBetween">
            <!-- border-gradient-img-big-style -->
            <div class="spaceEnd search_header_wrap">
              <el-dropdown class="nav_itemSeaech">
                <span
                  class="el-dropdown-link el-dropdown-link_title cursor"
                  style="font-size: 16px; text-align: left; margin-left: 32px"
                >
                  {{ flagName
                  }}<i
                    style="font-weight: bold; color: #1b1b1b"
                    class="el-icon-arrow-down el-icon--right"
                  ></i>
                </span>
                <el-dropdown-menu
                  slot="dropdown"
                  :append-to-body="false"
                  style="
                    width: 198px;
                    text-align: center;
                    height: 570px;
                    overflow: auto;
                    border-radius: 20px;
                    padding: 0 20px;
                    background: #fff;
                    box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
                    margin-top: -4px;
                    border: none;
                  "
                >
                  <el-dropdown-item style="padding: 0">
                    <div
                      class="textOneLine"
                      style="
                        width: 158px;
                        color: #969696;
                        font-family: PingFang SC;

                        text-align: left;
                        text-indent: 16px;
                      "
                      @click="gameDt()"
                    >
                      所有游戏
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-for="(item, index) in gameList"
                    :key="index"
                    style="padding: 0"
                  >
                    <div
                      class="textOneLine"
                      style="
                        width: 158px;
                        color: #969696;
                        font-family: PingFang SC;

                        text-align: left;
                        text-indent: 16px;
                      "
                      @click="gameDt(item)"
                    >
                      {{ item.name }}
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-input
                v-model="keywords"
                class="search_header_ipt"
                placeholder="请输入要搜找的内容"
                @keyup.enter.native="searchHeader"
              >
                <!-- <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="searchHeader"
                ></i> -->
                <iconFont
                  slot="suffix"
                  :size="30"
                  icon="web_search"
                  color="#000000"
                  style="margin-right: 26px; margin-top: 4px; cursor: pointer"
                  @click="searchHeader"
                />
              </el-input>
            </div>
            <!-- <div class="search_topBtn" @click="searchHeader">搜索</div> -->
          </div>
          <div v-if="!userInfo.id" class="login_router_style_box">
            <router-link
              class="login_router_style_btn"
              to="/login"
              rel="nofollow"
              style="margin-right: 10px"
              >登录</router-link
            >
            <router-link class="regin_router_style" to="/regin" rel="nofollow">
            </router-link>
          </div>
          <div
            v-else
            style="
              color: #1b1b1b;
              font-size: 16px;
              font-weight: 400;
              font-family: PingFang SC;
              display: flex;
              align-items: center;
            "
          >
            <img
              style="width: 46.278px; height: 46.278px"
              src="../../../static/user_default.png"
              alt=""
            />
            <span v-if="userInfo && userInfo.username" class="headerNickName">{{
              filterName(userInfo.username || '')
            }}</span>
            <span
              class="cursor loginOut"
              style="margin-left: 26.71px"
              @click="loginOut"
            >
              退出登录</span
            >
          </div>
        </div>
      </div>
    </div>

    <div class="nav_container">
      <div class="safe_width spaceBetween">
        <router-link
          :class="activeIndex == 0 ? 'active' : ''"
          class="nav_item"
          to="/"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('home')"
            @mouseleave="mouseleave6('home')"
          >
            <span v-if="activeIndex == 0 || mouseenterObj.home">首页</span>

            <img
              v-else
              style="width: 32px"
              class="header_text_img"
              src="../../../static/imgs/header_home.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 1 ? 'active' : ''"
          class="nav_item spaceCenter"
          to="/gameList"
          style="font-family: YouSheBiaoTiHei; font-size: 26px"
        >
          <div
            class="mouseenterContent spaceCenter"
            @mouseenter="mouseenter6('buy')"
            @mouseleave="mouseleave6('buy')"
          >
            <div class="footer_media spaceCenter">
              <IconFont
                v-if="activeIndex !== 1"
                :size="21"
                class="footer_media_iconFont"
                style="margin: 0"
                icon="maihao"
                color="red"
              />
              <img
                v-if="activeIndex == 1 || mouseenterObj.buy"
                style="width: 28px; height: 28px"
                src="../../../static/imgs/header_buy_icon.svg"
                alt=""
              />
            </div>
            <span
              v-if="activeIndex == 1 || mouseenterObj.buy"
              style="letter-spacing: 0px"
              >我要买</span
            >
            <!-- v-else -->
            <img
              v-else
              style="width: 72px; margin: 0px"
              class="header_text_img"
              src="../../../static/imgs/header_buy.svg"
              alt=""
            />
          </div>
        </router-link>
        <!-- c active222-->
        <!-- :class="activeIndex == 2 ? 'active' : ''"  -->
        <el-dropdown :class="activeIndex == 2 ? 'active' : ''" class="nav_item">
          <div
            class="mouseenterContent spaceCenter cursor"
            style="
              font-family: YouSheBiaoTiHei;
              font-size: 26px;
              height: 73px;
              background: transparent;
              border-radius: 50px 50px 0px 0px;
            "
            @mouseenter="mouseenter6('sell')"
            @mouseleave="mouseleave6('sell')"
          >
            <div class="footer_media spaceCenter">
              <IconFont
                v-if="activeIndex !== 2"
                :size="21"
                class="footer_media_iconFont"
                style="margin: 0"
                icon="woyaomai"
              />
              <img
                v-if="activeIndex == 2 || mouseenterObj.sell"
                style="width: 28px; height: 28px"
                src="../../../static/imgs/header_sell_icon.svg"
                alt=""
              />
            </div>
            <!-- v-if="activeIndex == 2 || mouseenterObj.sell" -->
            <div
              v-if="activeIndex == 2 || mouseenterObj.sell"
              style="width: 99px; text-align: left"
            >
              <div>我要卖</div>
              <div style="position: absolute; top: -3.2px; right: 20px">
                <!-- <i
                  style="font-size: 18px; color: #fff"
                  class="el-icon-arrow-down el-icon--right"
                ></i> -->
                <img
                  style="width: 12px"
                  src="../../../static/imgs/header_arrow.svg"
                  alt=""
                />
              </div>
            </div>
            <!-- v-else -->
            <!-- v-else -->
            <img
              v-else
              style="width: 99px; margin-top: -2px"
              class="header_text_img"
              src="../../../static/imgs/header_sell.svg"
              alt=""
            />
          </div>
          <div
            @mouseenter="mouseenter6('sell')"
            @mouseleave="mouseleave6('sell')"
          >
            <el-dropdown-menu
              slot="dropdown"
              :append-to-body="false"
              style="
                width: 174px;
                padding: 20px 14px;
                text-align: center;
                border-radius: 0px 0px 20px 20px;
                box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
                font-size: 16px;
                margin-top: 7px;
                border: none;
              "
            >
              <el-dropdown-item
                @mouseenter.native="mouseenter6('sell_account')"
                @mouseleave.native="mouseleave6('sell_account')"
              >
                <router-link class="blockAll" to="/allSell">
                  <div>
                    <img
                      v-if="mouseenterObj.sell_account"
                      style="width: 68px; margin-top: -3px; margin-left: -1px"
                      src="../../../static/imgs/header_btn_sell_account.svg"
                      alt=""
                    />

                    <div v-else>我要卖号</div>
                  </div>
                </router-link>
              </el-dropdown-item>
              <el-dropdown-item
                @mouseenter.native="mouseenter6('recall')"
                @mouseleave.native="mouseleave6('recall')"
              >
                <router-link class="blockAll" to="/recyle">
                  <div>
                    <img
                      v-if="mouseenterObj.recall"
                      style="width: 69px; margin-top: -4px; margin-left: -1.5px"
                      src="../../../static/imgs/header_btn_recall.svg"
                      alt=""
                    />
                    <span v-else>号商回收</span>
                  </div>
                </router-link>
              </el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </el-dropdown>

        <router-link
          :class="activeIndex == 3 ? 'active' : ''"
          class="nav_item"
          to="/assure"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('intermediary')"
            @mouseleave="mouseleave6('intermediary')"
          >
            <span v-if="activeIndex == 3 || mouseenterObj.intermediary"
              >中介担保</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_intermediary.svg"
              alt=""
            />
          </div>
        </router-link>

        <router-link
          :class="activeIndex == 4 ? 'nav_item active' : 'nav_item'"
          to="/appraisal"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('account')"
            @mouseleave="mouseleave6('account')"
          >
            <span v-if="activeIndex == 4 || mouseenterObj.account"
              >账号估价</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_account.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 6 ? 'active' : ''"
          class="nav_item"
          to="/helpCenter"
          rel="nofollow"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('hand')"
            @mouseleave="mouseleave6('hand')"
          >
            <span v-if="activeIndex == 6 || mouseenterObj.hand">帮助中心</span>

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_center.svg"
              alt=""
            />
          </div>
        </router-link>
        <router-link
          :class="activeIndex == 7 ? 'active' : ''"
          to="/account/center"
          rel="nofollow"
          class="nav_item"
        >
          <div
            class="mouseenterContent"
            @mouseenter="mouseenter6('persons')"
            @mouseleave="mouseleave6('persons')"
          >
            <span v-if="activeIndex == 7 || mouseenterObj.persons"
              >个人中心</span
            >

            <img
              v-else
              class="header_text_img"
              src="../../../static/imgs/header_persons.svg"
              alt=""
            />
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
// import { searchFlagApi, allGameaApi } from '@/api/index';
import isLogin from '@/utils/isLogin';
import { getGameList } from '@/api/index2.js';
import { logout } from '@/api/login';
import utils from '@/utils/index';
export default {
  props: {
    activeIndex: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      keywords: '',
      gameList: [],
      productCategoryId: '',
      flagName: '所有游戏',
      mouseenterObj: {
        'home': false,
        'buy': false,
        'sell': false,
        'intermediary': false,
        'account': false,
        'hand': false,
        'persons': false,
        'sell_account': false,
        'recall': false,
      },
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  watch: {},
  created() {
    if (this.$route.query.productCategoryId) {
      this.productCategoryId = this.$route.query.productCategoryId;
    }
    if (this.$route.query.keyword) {
      this.keywords = this.$route.query.keyword;
    }
    if (isLogin()) {
      // this.$store.dispatch('getUserInfoStore');
    }
    this.initGame();
  },
  methods: {
    mouseenter6(v) {
      this.mouseenterObj[v] = true;
    },
    mouseleave6(v) {
      this.mouseenterObj[v] = false;
    },
    filterName(name) {
      if (!name) {
        return name;
      } else {
        return name.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      }
    },
    // 游戏-初始化
    initGame() {
      const cashedFlag=utils.cachedFunFlag('gameListData')
      if(cashedFlag){
        this.gameList=cashedFlag.data
        this.getFlagNameFun()
      }else{
        getGameList().then((res) => {
        if (res.code === 200) {
          this.gameList = res.data;
          utils.cachedFunFlag('gameListData',res.data)
          this.getFlagNameFun()
        }
      });
      }
      
    },
    getFlagNameFun(){
      if (this.productCategoryId) {
          const findIt = this.gameList.find((ele) => {
            return ele.id == this.productCategoryId;
          });
          if (findIt) {
            this.flagName = findIt.name;
          }
        }
    },
    // 搜索
    searchHeader() {
      // if (!this.keywords) {
      //   this.$message.error('请输入搜索内容');
      //   return;
      // }
      var str = window.location.href;
      if (this.productCategoryId && this.keywords) {
        if (str.indexOf('/playList') > 0) {
          // 向列表传递id改变
          this.$router.replace({
            path:
              '/playList?productCategoryId=' +
              this.productCategoryId +
              '&keyword=' +
              this.keywords,
          });
          this.$emit('changeproductCategoryId', this.productCategoryId);
        } else {
          this.$router.push({
            path:
              '/playList?productCategoryId=' +
              this.productCategoryId +
              '&keyword=' +
              this.keywords,
          });
        }
      } else {
        if (str.indexOf('/searchAll') > 0) {
          this.$router.replace({
            path: '/searchAll?keyword=' + this.keywords,
          });
          this.$emit('doSearch');
        } else {
          this.$router.push({
            path: '/searchAll?keyword=' + this.keywords,
          });
        }
      }
    },
    loginOut() {
      logout().then((res) => {
        if (res.code == 200) {
          localStorage.removeItem('token');
          localStorage.removeItem('yximtoken');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('userInfoExpire');
          localStorage.removeItem('contentData');
          localStorage.removeItem('gameListData');
          location.reload();
        }
      });
    },
    // 选择游戏
    gameDt(date) {
      if (date) {
        this.productCategoryId = date.id;
        this.flagName = date.name;
      } else {
        this.productCategoryId = '';
        this.flagName = '所有游戏';
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    next();
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.topPage_tit {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #666666;
  padding: 12px 0;
}
.search_header_wrap {
  // border: 2.571px solid transparent;
  border-radius: 40px;
  width: 599px;
  padding-left: 4px;
  height: 46px;
  background: var(--btn-background-gradient);
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    margin: 3px;
    background: #fff;
    border-radius: 40px;
  }
}
.search_header_ipt .el-input__inner {
  border: none;
  padding-left: 0;
  height: 38px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-left: -10px;
  width: 300px;
}
.search_header_ipt .el-input__icon {
  font-size: 25.71px;
  margin-right: 32px;
  margin-top: -1px;
  cursor: pointer;
  color: #000;
}
.search_header_btn {
  color: #ff6716;
  width: 20px;
  font-size: 20px;
  margin-right: 20px;
  cursor: pointer;
}
.nav_container {
  width: 1200px;
  height: 68px;
  background: #fff;
  // box-shadow: 6px 0px 10px 1px rgba(179, 179, 179, 0.46);
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 58px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  margin-top: 20px;
  padding: 0 13px;
}
.nav_item {
  transition: all 0.3s;
  cursor: pointer;
  font-size: 16px;
  font-family: 'PingFang SC';
  color: #ff720c;
  line-height: 46px;
  height: 46px;
  // width: 200px;
  min-width: 166px;
  text-align: center;
  position: relative;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 1.6px;
}
.nav_item.active,
.nav_item:hover {
  color: #fff;
  background: url(../../../static/imgs/header_nav_bk.svg);
  background-size: cover;
  border-radius: 50px;
  .footer_media_iconFont {
    color: #fff;
  }
}
.right_userH {
  font-size: 13px;
  color: #333333;
}
.nav_itemSeaech {
  font-size: 14px;
  color: #333333;
  line-height: 52px;
  height: 52px;
  min-width: 114px;
  text-align: center;
  margin-right: 20px;
  position: relative;
  flex-shrink: 0;
  .el-dropdown-menu {
    padding: 20px !important;
    left: 0px !important;
    &::-webkit-scrollbar {
      // width: 0; /* 隐藏滚动条整体宽度 */
      background: transparent; /* 背景透明 */
    }

    &::-webkit-scrollbar-track {
      background-color: transparent; /* 隐藏滚动条轨道 */
    }
  }
  // top: 36px;
}
.search_topBtn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
  height: 54px;
  line-height: 54px;
  padding: 0 30px;
  margin-left: 15px;
  border-radius: 10px;
}
.home_logo_pic {
  width: 166.667px;
  height: 50px;
  cursor: pointer;
}
.headerKkContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.login_router_style_btn {
  width: 129px;
  display: flex;
  height: 46px;
  // padding: 18px 48px;
  justify-content: center;
  align-items: center;
  border-radius: 46px;
  font-family: 'PingFang SC';
  font-size: 16px;
  background: url(../../../static/imgs/headerKk_login.png);
  background-size: 100% 100%;
  color: #fff;

  &:hover {
    color: #fff !important;
    // border: 1px solid #ffddbe !important;
    // background: #ff7a00 !important;
    // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset !important;
    background: url(../../../static/imgs/headerKk_login_hover.png);
    background-size: 100% 100%;
  }
}
.login_router_style {
}
.regin_router_style {
  width: 129px;
  display: flex;
  height: 46px;
  // padding: 18px 48px;
  justify-content: center;
  align-items: center;
  border-radius: 46px;
  font-family: 'PingFang SC';
  font-size: 16px;
  background: url(../../../static/imgs/headerKk_enrollment.svg);
  background-size: 100% 100%;
  color: #ff720c;

  &:hover {
    background: url(../../../static/imgs/headerKk_enrollment_hover.svg);
    background-size: 100% 100%;
  }
}
.login_router_style_box {
  display: flex;
  align-items: center;
}
.footer_media {
  width: 28px;
  height: 28px;
  // line-height: 32px;
  margin-right: 6px;
  // text-align: center;
  background: url('../../../static/imgs/footerKk_media_bk.svg') no-repeat center
    top;
  background-size: cover;
}
.mouseenterContent {
  width: 100%;
  height: 100%;
}
.header_text_img {
  width: 68px;
  margin-top: -3px;
}
.headerNickName {
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
  margin-left: 10.283px;
}
.loginOut {
  color: rgba(0, 0, 0, 0.4);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
</style>
