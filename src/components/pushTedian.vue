<template>
  <el-form-item :label="label">
    <div v-for="(item, index) in opetionDate" :key="index">
      <!-- 输入框情况 -->
      <div v-if="item.type == 1" class="tedian_item_com">
        <div class="teding_name_com">{{ item.name }}</div>
        <el-input
          v-model="item.iptVal"
          class="right_tedian_com"
          type="text"
          placeholder="请输入"
          @blur="changeOpetions"
        ></el-input>
      </div>
      <!-- 下拉选择+筛选 -->
      <div v-if="item.type == 2" class="tedian_item_com">
        <div class="teding_name_com">{{ fakeName(item.name) }}</div>
        <div class="right_tedian_com">
          <!-- 折叠面板模式 -->
          <!-- 非折叠面板 -->
          <div class="searchIpt_done_com" @click="showChilde(item)">
            <div
              v-if="item.choosedList.length > 0"
              class="spaceStart"
              style="flex-wrap: wrap; margin-top: 6.856px"
            >
              <div
                v-for="(v, i) in item.choosedList"
                :key="i"
                class="tedian_Choose_small_com spaceCenter active"
                @click.stop="chooseChoosed(v, item)"
              >
                <div class="content">
                  {{ fakeName(v.name) }}
                  <img
                    v-if="iconFilter(v)"
                    :src="iconFilter(v)"
                    class="icon_tedian"
                  />
                </div>
              </div>
            </div>
            <span v-else style="color: #606266">&nbsp;请选择</span>
            <i
              v-if="item.showChild"
              class="el-icon-arrow-up up_arror_tedian_com"
            ></i>
            <i v-else class="el-icon-arrow-down up_arror_tedian_com"></i>
          </div>
          <div v-if="item.showChild" class="tedian_container_content_box">
            <el-input
              v-model="item.iptSearchVal"
              :id="'listNode' + item.id"
              type="text"
              class="tedian_container_content_box_input"
              placeholder="请输入搜索内容或者新游戏物品"
              style="margin-top: 17.14px; width: 100%"
              @input="tedianChange(item)"
            >
            </el-input>
            <div
              class="tedian_container_com spaceStart"
              style="border-top: none"
            >
              <!-- 搜索的显示列表 -->
              <div
                v-if="item.searchList && item.searchList.length > 0"
                class="searchList_wrap_com"
              >
                <div class="spaceStart" style="flex-wrap: wrap">
                  <div
                    v-for="(v, i) in item.searchList"
                    :key="i"
                    :class="v.checked ? 'active' : ''"
                    class="tedian_itemChoose_com spaceCenter"
                    @click="chooseTeDianItem(v, item)"
                  >
                    {{ fakeName(v.name) }}
                    <img
                      v-if="iconFilter(v)"
                      :src="iconFilter(v)"
                      class="icon_tedian"
                    />
                  </div>
                </div>
              </div>
              <div
                v-for="(v, i) in item.childList"
                :key="i"
                :class="v.checked ? 'active' : ''"
                class="tedian_itemChoose_com spaceCenter"
                @click="chooseTeDianItem(v, item)"
              >
                {{ fakeName(v.name) }}
                <img
                  v-if="iconFilter(v)"
                  :src="iconFilter(v)"
                  class="icon_tedian"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 下拉选择+筛选 end -->
    </div>
  </el-form-item>
</template>

<script>
// import { optionsListApi } from '@/api/index';

export default {
  props: {
    label: {
      type: String,
      default: '特点描述',
    },
    flagId: {
      type: String,
      default: '',
    },
    // 编辑带过来的详情数据
    detailOptions: {
      type: Array,
      default() {
        return [];
      },
    },
    opetionDate: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      // detail_options: [],
      //   opetionDate: [],
    };
  },
  computed: {},
  watch: {
    flagId(newVal, oldVal) {
      this.initOpetionList();
    },
  },
  created() {
    if (this.flagId) {
      this.initOpetionList();
    }
  },
  methods: {
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    /**
     * 自定义数据添加
     * 搜索空-提示
     * 已添加过的不添加
     * 添加完成，清空输入框以及搜索内容
     * **/
    // 处理自定义数据加入
    sureAddZidingyi(date) {
      if (!date.iptSearchVal) {
        this.$message.error('请输入搜索内容或者新游戏物品');
        return;
      }
      var isTianjia = true;
      date.choosedList.forEach((v, i) => {
        if (v.name == date.iptSearchVal) {
          this.$message.error('您已添加此数据，无需重复添加');
          isTianjia = false;
        }
      });
      if (isTianjia) {
        var json = {
          name: date.iptSearchVal,
        };
        date.choosedList.push(json);
        date.iptSearchVal = '';
        date.searchList = [];
      }
    },
    // 处理已经添加的数据
    chooseChoosed(date, arrlist) {
      arrlist.choosedList.forEach((v, i) => {
        if (v.name == date.name) {
          arrlist.choosedList.splice(i, 1);
        }
      });
      arrlist.childList.forEach((v, i) => {
        if (v.name == date.name) {
          v.checked = false;
        }
      });
    },
    iconFilter(v) {
      if (v.name.indexOf('[绝]') !== -1) {
        return '../../static/push/jue.png';
      } else if (v.name.indexOf('[钱]') !== -1) {
        return '../../static/push/price.png';
      } else if (v.name.indexOf('[核]') !== -1) {
        return '../../static/push/he.png';
      }
    },
    // initOpetionList() {
    //   optionsListApi({
    //     flag_id: this.flagId,
    //   }).then((response) => {
    //     response.data.forEach((v, i) => {
    //       v.id = i;
    //     });
    //     this.opetionDate = response.data;
    //     // 详情赋值数据
    //     if (this.detailOptions) {
    //       this.detailOptions.forEach((item, index) => {
    //         this.opetionDate.forEach((v, i) => {
    //           if (item.name == v.name && item.type == 1) {
    //             v.iptVal = item.iptVal;
    //           }
    //           if (v.type == 2 && item.title == v.name) {
    //             v.choosedList = item.value;
    //           }
    //         });
    //       });
    //       // 给下拉展示的数据加上选中
    //       this.opetionDate.forEach((v, i) => {
    //         if (v.type == 2 && v.choosedList.length > 0) {
    //           v.choosedList.forEach((c, indexC) => {
    //             v.childList.forEach((child, indexChild) => {
    //               if (c.name == child.name) {
    //                 child.checked = true;
    //               }
    //             });
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    initOpetionList() {
      if (this.detailOptions) {
        this.detailOptions.forEach((item, index) => {
          this.opetionDate.forEach((v, i) => {
            if (v.type == 1 && item.title == v.name) {
              v.iptVal = item.value;
            }
            if (v.type == 2 && item.title == v.name) {
              if (item.value) {
                v.choosedList = item.value.split(',').map((ele) => {
                  return { name: ele };
                });
              } else {
                v.choosedList = [];
              }
            }
            if (v.type == 3 && item.title == v.name) {
              v.value = item.value;
            }
            if (v.type == 4 && item.title == v.name) {
              const value = v.professionDate.findIndex((it) => {
                return it === item.value;
              });
              v.indexPro = value;
              v.value = v.professionDate[v.indexPro];
            }
          });
        });
        // 给下拉展示的数据加上选中
        this.opetionDate.forEach((v, i) => {
          if (v.type == 2 && v.choosedList.length > 0) {
            v.choosedList.forEach((c, indexC) => {
              v.childList.forEach((child, indexChild) => {
                if (c.name == child.name) {
                  child.checked = true;
                }
              });
            });
          }
        });
      }
    },
    // 传递数据改变
    changeOpetions() {
      this.$emit('getopetion', this.opetionDate);
    },
    // 特点描述-下拉选择-重组选中数据
    chooseTeDianItem(date, arrlist) {
      // arrlist.choosedList = [];
      // date.checked = !date.checked;
      // arrlist.childList.forEach((item, index) => {
      //     if(item.checked){
      //         arrlist.choosedList.push(item);
      //     }
      // })
      if (!date.checked) {
        date.checked = true;
        arrlist.choosedList.push(date);
      } else {
        date.checked = false;
        arrlist.choosedList.forEach((v, i) => {
          if (v.name == date.name) {
            arrlist.choosedList.splice(i, 1);
          }
        });
      }
      this.changeOpetions();
    },
    // 打开-关闭下拉框
    showChilde(date) {
      this.$set(date, 'showChild', !date.showChild);
      if (date.showChild) {
        this.$nextTick(() => {
          var id = 'listNode' + date.id;
          var userName = document.getElementById(id);
          userName.focus();
        });
      }
      if (date.showChild == false) {
        date.searchList = [];
        date.iptSearchVal = '';
      }
    },
    // 关闭筛选框
    closeChooseTedian(date) {
      date.iptSearchVal = '';
      date.showChild = false;
    },
    // 下拉框里面搜索-有数据在搜索没有就不搜索
    tedianChange(e) {
      if (e.iptSearchVal) {
        let reg = new RegExp(e.iptSearchVal);
        e.searchList = e.childList.filter((item) => reg.test(item.name));
      } else {
        e.searchList = [];
      }
      this.changeOpetions();
    },
    // 添加自定义数据
    // addNewsChoose(date) {
    //   var json = {
    //     name: date.iptSearchVal,
    //   };
    //   date.zidingyiList.push(json);
    //   // date.iptSearchVal = '';
    // },
  },
};
</script>

<style scoped>
.addChoose {
  display: inline-block;
  padding: 0px 20px;
  height: 30px;
  line-height: 30px;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  background: #ff6716;
}
.searchNoDate {
  display: block;
  width: 100%;
  padding-bottom: 8px;
}
.icon_tedian {
  width: 18px;
  height: 18px;
  margin-left: 4px;
}
.tedian_Choose_small .icon_tedian {
  width: 14px;
  height: 14px;
}
</style>
