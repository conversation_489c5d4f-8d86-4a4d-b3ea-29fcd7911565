<template>
  <div>
    <div
      v-for="(item, index) in urlPic"
      v-if="urlPic && urlPic.length"
      :key="index"
      class="picUpload_wrapSmall picUpload_wrapSmall_img_list"
    >
      <div class="delet_item" @click="deletPic(index)">
        <el-select
              v-if="optionsList&&optionsList.length>0"
              @change="v => changeImgType(v, index)"
              v-model="item.name"
              class="imageSelect"
              size="mini"
            >
              <el-option
                v-for="ele in optionsList"
                :key="ele.name"
                :label="ele.name"
                :value="ele.name"
              ></el-option>
            </el-select>
        <img
          style="
            width: 25px;
            height: 25px;
            position: absolute;
            top: 0px;
            right: 0px;
          "
          src="../../../static/imgs/uploadList_right_icon.png"
          alt=""
        />
        <i class="el-icon-delete del_icon"></i>
      </div>
      <el-image
        :preview-src-list="urlPic"
        :src="item.url"
        :key="index"
        style="width: 100%; height: 100%; cursor: pointer"
        fit="cover"
        class="picUpload_wrapSmall_el-img"
      ></el-image>
    </div>
    <div v-if="urlPic.length<max" v-show="!isUploading" class="picUpload_wrapSmall">
      <el-upload
        ref="upload"
        :action="useOss ? ossUploadUrl : minioUploadUrl"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :http-request="customUpload"
        multiple
        list-type="picture"
        class="upload-input"
        accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
      >
        <el-button size="small" type="primary">+</el-button>
      </el-upload>
      <i class="el-icon-plus cover-uploader-icon"></i>
    </div>
    <div
      v-show="isUploading"
      class="picUpload_wrapSmall"
      style="font-size: 14px"
    >
      <p>正在上传中~</p>
    </div>
    <!-- 水印图片 -->
    <img
      ref="waterImg"
      style="width: 0; height: 0"
      src="../../../static/water_pc.png"
      crossorigin="Anonymous"
    />
  </div>
</template>

<script>
import ImageCompressor from 'js-image-compressor';
import { fileByBase64, base64ToFile } from '@/utils';
import { stsTokenApi } from '@/api/index2.js';
import axios from 'axios';
import { max } from 'lodash';

export default {
  props: {
    urlPic: {
      type: Array,
      default() {
        return [];
      },
    },
    nameKey: {
      type: String,
      default: '',
    },
    max:{
      type: Number,
      default: 10000,
    },
    optionsList:{
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: '',
      },
      isUploading: false, // 单张上传状态
      useOss: true,
      ossUploadUrl: 'https://images2.kkzhw.com',
      minioUploadUrl: 'http://*************:8201/mall-admin/minio/upload',
    };
  },
  computed: {
    imageUrl() {
      return this.value;
    },
    imageName() {
      if (this.value != null && this.value !== '') {
        return this.value.substr(this.value.lastIndexOf('/') + 1);
      } else {
        return null;
      }
    },
    fileList() {
      return [
        {
          name: this.imageName,
          url: this.imageUrl,
        },
      ];
    },
    showFileList: {
      get: function () {
        return (
          this.value !== null && this.value !== '' && this.value !== undefined
        );
      },
      set: function (newValue) {},
    },
  },
  methods: {
    changeImgType(v,i){
      this.$emit('changeImgType',v,i)
    },
    handlePreview() {
      this.dialogVisible = true;
    },
    beforeUpload2(file) {
      return new Promise((resolve, reject) => {
        // 1.调用方法1： 把文件转换为base64字符串
        fileByBase64(file, async (base64) => {
          // 2. 调用方法2：把base64转换为Canvas
          let tempCanvas = await this.imgToCanvas(base64);
          //3.调用方法3： 写入水印到Canvas
          const canvas = this.addWatermark(tempCanvas, '看看账号网');
          //4. 调用方法4：把Canvas转换为image文件
          const img = this.convasToImg(canvas);
          //5.调用方法5：被image转换为File文件(第二个参数为文件名)
          let newFile = base64ToFile(img.src, file.name);
          resolve(newFile);
        });
      });
    },
    /**
     * Base64转成canvas
     * @param  base64
     */
    async imgToCanvas(base64) {
      // 创建img元素
      const img = document.createElement('img');
      img.setAttribute('src', base64);
      await new Promise((resolve) => (img.onload = resolve));
      // 创建canvas DOM元素，并设置其宽高和图片一样
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      // 坐标(0,0) 表示从此处开始绘制，相当于偏移。
      canvas.getContext('2d').drawImage(img, 0, 0);
      return canvas;
    },
    /**
     * canvas添加水印
     * @param  canvas 对象
     * @param text 水印文字
     */
    addWatermark(canvas, text) {
      const ctx = canvas.getContext('2d');
      // 给上传的图片添加-水印图片
      // ctx.drawImage(this.$refs.waterImg, 0, 0)
      const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
      ctx.fillStyle = pattern;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      return canvas;
    },
    /**
     * canvas转成img
     * @param {canvas对象} canvas
     */
    convasToImg(canvas) {
      // 新建Image对象，可以理解为DOM
      let image = new Image();
      // canvas.toDataURL 返回的是一串Base64编码的URL
      // 指定格式 PNG
      image.src = canvas.toDataURL('image/png');
      return image;
    },
    // 删除已上传的图片
    deletPic(index) {
      this.$emit('deletPicList', index, this.nameKey);
    },
    handleUploadSuccess(res, file, fList) {
      if (fList.every((v) => v.status === 'success')) {
        const list = fList.filter((item) => item.percentage === 100);
        list.map((ele) => {
          delete ele.percentage;
          const uid = ele.uid;
          const url =
            this.dataObj[uid].host +
            '/' +
            this.dataObj[uid].dir +
            '/' +
            ele.name;
          this.fileList.push({ name: ele.name, url: url });
          this.$emit('upSuccsessList', url, this.nameKey);
          this.isUploading = false;
        });
      }
    },
    rename(file, fineName) {
      // const timeStamp = new Date().getTime();
      // const name = `${timeStamp}_${file.name}`;
      const copyFile = new File([file], fineName, {
        type: file.type,
      });
      copyFile.uid = file.uid;
      const index = this.$refs.upload.uploadFiles.findIndex((ele) => {
        return ele.uid === file.uid;
      });
      this.$refs.upload.uploadFiles[index].raw = copyFile;
      this.$refs.upload.uploadFiles[index].name = copyFile.name;
      this.$refs.upload.uploadFiles[index].url = URL.createObjectURL(copyFile);
      return copyFile;
    },
    customUpload(options) {
      const formData = new FormData();
      const data = this.dataObj[options.file.uid];
      Object.keys(data).forEach((key) => {
        if (key !== 'fileName') {
          formData.append(key, data[key]);
        }
      });
      formData.append('success_action_status', '200');
      formData.append('file', options.file, options.file.name);
      axios
        .post(this.ossUploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          if (res.status === 200) {
            if (options.onProgress) {
              options.onProgress({
                percent: 100,
              });
            }
            if (options.onSuccess) {
              options.onSuccess(res);
            }
          }
        });
    },
    /**
     * 角色图片上传OSS-多张
     */
    async beforeUpload(file) {
      let _self = this;
      const copyFile = await stsTokenApi()
        .then((response) => {
          const ext = file.name.split('.').pop();
          const uid = file.uid;
          const fileName = response.data.fileName;
          _self.dataObj[uid] = {};
          _self.dataObj[uid].fileName = fileName;
          _self.dataObj[uid].policy = response.data.policy;
          _self.dataObj[uid].signature = response.data.signature;
          _self.dataObj[uid].ossaccessKeyId = response.data.accessKeyId;
          _self.dataObj[uid].key = response.data.dir + `/${fileName}.${ext}`;
          _self.dataObj[uid].dir = response.data.dir;
          _self.dataObj[uid].host = response.data.host;
          const copyFile = this.rename(file, `${fileName}.${ext}`);
          // _self.dataObj.callback = response.data.callback;
          return copyFile;
        })
        .catch((err) => {
          console.log(err);
          return false;
        });
      if (!copyFile) {
        return false;
      }
      this.isUploading = true;
      let blobO = await this.beforeUpload2(copyFile);
      // var str = this.getDayStr();
      // const ext = blobO.name.split('.').pop() || '';
      // const rename = blobO.name.split(ext)[0] + new Date().getTime();
      // var strName = md5(rename) + '.' + ext;
      // let storeAs = 'storage/uploads/img/' + str + '/' + strName;

      // var that = this;
      // const ossClient = new OSS(that.OSSOptions);
      // 图片压缩
      let newFile = await this.compressionImage(blobO);

      return new Promise((resolve, reject) => {
        if (!this.useOss) {
          // 不使用oss不需要获取策略
          resolve(newFile);
        } else {
          resolve(newFile);
        }
      });
      // that.compressionImage(blobO).then((blob) => {
      //   ossClient
      //     .put(storeAs, blob)
      //     .then(function (result) {
      //       let newStr = result.url;
      //       newStr = newStr.replace(
      //         'https://kkzhw.oss-cn-hangzhou.aliyuncs.com',
      //         'https://images.kkzhw.com',
      //       );
      //       console.log(newStr);
      //       that.$emit('upSuccsessList', newStr, that.nameKey);
      //       that.isUploading = false;
      //     })
      //     .catch(function (err) {
      //       console.log(err);
      //     });
      // });
    },
    // 生成日期：20230508；做文件名用
    // getDayStr() {
    //   var date = new Date();
    //   var year = date.getFullYear();
    //   var month = date.getMonth() + 1;
    //   var day = date.getDate();
    //   if (month < 10) {
    //     month = '0' + '' + month;
    //   }
    //   if (day < 10) {
    //     day = '0' + '' + day;
    //   }
    //   var str = year + '' + '' + month + '' + day;
    //   return str;
    // },
    // 图片压缩
    compressionImage(file) {
      return new Promise((resolve, reject) => {
        new ImageCompressor({
          file: file,
          quality: 0.4,
          convertSize: 100000, // 1MB 的都要压缩
          redressOrientation: false,
          beforeCompress: function (result) {
            // console.log('压缩之前图片尺寸大小: ', result.size)
            // console.log('mime 类型: ', result.type)
          },
          success: function (result) {
            // console.log('压缩之后图片尺寸大小: ', result.size)
            // console.log('mime 类型: ', result.type)
            // console.log('压缩率： ', ((file.size - result.size) / file.size * 100).toFixed(2) + '%')
            let file = new File([result], result.name, { type: result.type });
            resolve(file);
          },
          error(e) {
            reject(e);
          },
        });
      });
    },
  },
  beforeRouteLeave(to, from, next) {
    next();
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.picUpload_wrapSmall {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  border: 1px dashed #d9d9d9;
  position: relative;
  text-align: center;
  line-height: 100px;
  font-size: 28px;
  color: #8c939d;
  overflow: hidden;
  float: left;
  margin-right: 16.64px;
  margin-bottom: 15px;
}
.picUpload_wrapSmall_img_list {
  border: none !important;
}

.picUpload_wrapSmall_img_list_view {
  width: 100%;
  height: 100%;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  right: 0px;
  top: 0px;
  color: #fff;
  cursor: pointer;
}
.upload-input {
  opacity: 0;
  width: 100%;
  position: absolute;
  height: 100%;
  .el-upload {
    width: 100%;
    height: 100%;
  }
}

.delet_item {
  position: absolute;
  width: 100%;
  height: 100%;
  // background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  right: 0px;
  top: 0px;
  color: #fff;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delet_item:hover {
  background: rgba(0, 0, 0, 0.5);

  .del_icon {
    display: block;
  }
}
.del_icon {
  display: none;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.imageSelect{
  position: absolute;
  top: -1px;
  left: 0px;
  width: 100px;
}
</style>
