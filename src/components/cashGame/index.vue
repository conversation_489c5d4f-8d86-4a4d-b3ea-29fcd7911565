<template>
  <div style="padding: 0 40px">
    <div class="spaceBetween eyes_normal">
      <div>最近浏览</div>
      <div class="spaceStart delet_eyesBtn" @click="clearCash">
        <i class="el-icon-delete"></i>&nbsp;
        <div>清空最近浏览</div>
      </div>
    </div>
    <div class="gameAll_wrap spaceStart hotGame_wrap">
      <div v-if="nshJson" class="game_wrap" @click="playPage(nshJson)">
        <div class="game_wrap_pic">
          <el-image
            :src="nshJson.icon"
            style="width: 100%; height: 100%"
            fit="cover"
          ></el-image>
        </div>
        <div>{{ nshJson.name }}</div>
      </div>
      <div
        v-for="(item, index) in cashFlagDateArr"
        v-if="index < 8 && item.id != 3"
        :key="index"
        class="game_wrap"
        @click="playPage(item)"
      >
        <div class="game_wrap_pic">
          <el-image
            :src="item.icon"
            style="width: 100%; height: 100%"
            fit="cover"
          ></el-image>
        </div>
        <div>{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    pageTo: {
      type: String,
      default: '',
    },
    gameList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    gameListAll: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      cashFlagDateArr: [], // 最近浏览
    };
  },
  computed: {
    nshJson() {
      const findIt = this.gameListAll.find((v) => {
        return v.name === '逆水寒手游';
      });
      return findIt;
    },
  },
  mounted() {
    this.cashFlagDateArr = JSON.parse(localStorage.getItem('cashFlagDateArr2'))
      ? JSON.parse(localStorage.getItem('cashFlagDateArr2'))
      : [];
  },
  methods: {
    // 跳转
    playPage(date) {
      this.cashFlagDate(date);
      this.$emit('playPage', date);
      // if (this.pageTo == 'assureList') {
      //   this.$router.push({
      //     path: '/assureList?flag_id=' + date.id + '&name=' + date.title,
      //   });
      // } else if (this.pageTo == 'appraisal') {
      //   this.$router.push({
      //     path: '/appraList?flag_id=' + date.id + '&name=' + date.title,
      //   });
      // } else {
      //   this.$router.push({
      //     path: '/playList?productCategoryId=' + date.id,
      //   });
      // }
    },
    // 缓存游戏数据
    cashFlagDate(date) {
      if (date.name === '逆水寒手游') {
        return;
      }
      var cashFlagDateArr = JSON.parse(
        localStorage.getItem('cashFlagDateArr2')
      );
      // 没有缓存的时候
      if (!cashFlagDateArr) {
        cashFlagDateArr = [];
        cashFlagDateArr.unshift(date);
        localStorage.setItem(
          'cashFlagDateArr2',
          JSON.stringify(cashFlagDateArr)
        );
        return;
      }
      // 有缓存分有没有id
      var num = cashFlagDateArr.findIndex((v) => {
        return v.id == date.id;
      });
      if (num == -1) {
        if (cashFlagDateArr.length >= 8) {
          cashFlagDateArr.pop();
        }
        cashFlagDateArr.unshift(date);
        localStorage.setItem(
          'cashFlagDateArr2',
          JSON.stringify(cashFlagDateArr)
        );
      }
    },
    // 清空缓存
    clearCash() {
      localStorage.removeItem('cashFlagDateArr2');
      this.cashFlagDateArr = [];
    },
  },
};
</script>

<style scoped>
.hotGame_wrap {
  border-bottom: 0.5px solid #ff7a00;
  padding-bottom: 42.73px;
}
.gameAll_wrap {
  flex-wrap: wrap;
}
.game_wrap {
  width: 77.13px;
  height: 100px;
  text-align: center;
  color: #222;
  font-family: 'PingFang SC';
  font-size: 13.712px;
  color: #222222;
  margin-top: 19.711px;
  margin-right: 24px;
  cursor: pointer;
  transition: all 0.3s;
  letter-spacing: 0.32px;
}
.game_wrap:nth-child(10n) {
  margin-right: 0;
}
.game_wrap:hover {
  transform: translateY(-5px);
}
.game_wrap_pic {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border-radius: 10.28px;
  overflow: hidden;
  margin-bottom: 5.45px;
}
.eyes_normal {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 20.56px;
  padding-top: 25.71px;
}
.delet_eyesBtn {
  cursor: pointer;
  font-size: 14px;
  color: #909090;
}
</style>
