<template>
  <div class="fixed_nav">
    <!-- <div class="fixedNav_tit">快捷导航</div> -->

    <!-- <div class="fixedNav_item">
			<img class="fixedNav_pic" src="../../../static/fix/1.png" />
			<div class="fixedNav_subT">下载APP</div>
		</div> -->
    <div
      class="fixedNav_item"
      @mouseenter="showDownload"
      @mouseleave="hideDownload"
    >
      <img class="fixedNav_pic" src="../../../static/fix/1.svg" />
      <div class="fixedNav_subT">下载APP</div>
      <div :class="showQr" class="downloadQr">
        <img class="qRlogo" src="../../../static/imgs/logo_Bk.svg" alt="" />
        <div class="tit">
          <div>账号交易</div>
          <div>看看账号更安全</div>
        </div>
        <div class="qr">
          <canvas
            id="QRCode_header_download"
            style="width: 128px; height: 128px"
          ></canvas>
          <img
            style="
              width: 34.26px;
              height: 34.26px;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            "
            src="../../../static/imgs/qrcode_wechat.png"
            alt=""
          />
        </div>
        <div>
          <div class="note">扫码下载APP</div>
          <div class="tip">建议用浏览器扫一扫</div>
        </div>
      </div>
    </div>

    <!-- <a v-if="loginEd" class="fixedNav_item ws-chat" :data-src="txImcode + '&token=' + token">
			<img class="fixedNav_pic" src="../../../static/fix/2.png" />
			<div class="fixedNav_subT">联系客服</div>
		</a> -->
    <!-- <router-link class="fixedNav_item" to="/gameCustormList" rel="nofollow">
      <img class="fixedNav_pic" src="../../../static/fix/2.png" />
      <div class="fixedNav_subT">联系客服</div>
    </router-link> -->
    <!-- <router-link to="/account/center" class="fixedNav_item">
			<img class="fixedNav_pic" src="../../../static/fix/user.jpg" />
			<div class="fixedNav_subT">个人中心</div>
		</router-link>
		<div class="fixedNav_item">
			<img class="fixedNav_pic" src="../../../static/fix/4.png" />
			<div class="fixedNav_subT">业务介绍</div>
		</div> 
		<router-link to="/trade/bussIntorduce" class="fixedNav_item">
			<img class="fixedNav_pic" src="../../../static/fix/4.png" />
			<div class="fixedNav_subT">业务介绍</div>
		</router-link>-->

    <router-link to="/suggest" class="fixedNav_item" rel="nofollow">
      <img
        class="fixedNav_pic"
        style="width: 28px"
        src="../../../static/fix/5.svg"
      />
      <div class="fixedNav_subT">投诉建议</div>
    </router-link>
    <router-link to="/account/footerMark" class="fixedNav_item" rel="nofollow">
      <img class="fixedNav_pic" src="../../../static/fix/6.svg" />
      <div class="fixedNav_subT">最近浏览</div>
    </router-link>
    <router-link to="/helpCenter" class="fixedNav_item" rel="nofollow">
      <img class="fixedNav_pic" src="../../../static/fix/7.svg" />
      <div class="fixedNav_subT">帮助中心</div>
    </router-link>
    <div class="fixedNav_item" @click="showIM">
      <div :class="showBounce ? 'jumping-box' : ''" class="imbox">
        <img class="fixedNav_pic" src="../../../static/lingdanNew.svg" />
        <div v-if="imunreadcount" class="imcount">
          {{ imunreadcount }}
        </div>
      </div>
      <div class="fixedNav_subT">消息</div>
    </div>
    <div class="fixedNav_item" @click="goTop">
      <img class="fixedNav_pic" src="../../../static/backTop.svg" />
      <div class="fixedNav_subT">返回顶部</div>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcode';
import isLogin from '@/utils/isLogin.js';
// import { getAccountTxApi } from '@/api/index'

export default {
  props: {
    // productCategoryId: '',
  },
  data() {
    return {
      showQr: '',
      loginEd: false,
      showBounce: false,
    };
  },
  computed: {
    imunreadcount() {
      if (this.$store.getters.imunreadcount > 99) {
        return '99';
      } else {
        return this.$store.getters.imunreadcount;
      }
    },
  },
  watch: {
    imunreadcount(nVal, oVal) {
      if (nVal > 0) {
        this.showBounce = true;
      } else {
        this.showBounce = false;
      }
    },
  },
  created() {
    // if(!isLogin()){
    //     this.loginEd = false;
    // }else{
    // 	this.loginEd = true;
    // 	this.bindConcat();
    // }
  },
  methods: {
    showDownload() {
      this.creatQrCode();
      this.showQr = 'showQr';
    },
    hideDownload() {
      this.showQr = '';
    },
    creatQrCode() {
      const picUrl = 'https://m.kkzhw.com/pages/download/download';
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 128, //宽
        height: 128, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_download');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        } else {
          console.log(msg, 11111);
          const canvas = msg.getContext('2d');
          const logoWidth = 34.56;
          const logoHeight = 34.56;
          const logo = new Image();
          logo.src = '../../../static/imgs/qrcode_wechat.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    goTop() {
      this.$emit('goPageTop');
    },
    showIM() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      this.$store.dispatch('ToggleIM', true);
    },
    backTopPage() {
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
      console.log(scrollEl);
      // window.scrollTo(0,0);
    },
  },
};
</script>

<style scoped lang="scss">
.jumping-box {
  animation: jump-left-right 2s infinite alternate;
}

@keyframes jump-left-right {
  0% {
    transform: translateX(0); /* 起始位置，不移动 */
  }
  5% {
    transform: translateX(-2px); /* 起始位置，不移动 */
  }
  10% {
    transform: translateX(2px); /* 起始位置，不移动 */
  }
  15% {
    transform: translateX(-2px); /* 起始位置，不移动 */
  }
  20% {
    transform: translateX(2px); /* 起始位置，不移动 */
  }
  25% {
    transform: translateX(0); /* 起始位置，不移动 */
  }
  75% {
    transform: translateX(0); /* 向右移动10px */
  }
  80% {
    transform: translateX(-2px); /* 起始位置，不移动 */
  }
  85% {
    transform: translateX(2px); /* 起始位置，不移动 */
  }
  90% {
    transform: translateX(-2px); /* 起始位置，不移动 */
  }
  95% {
    transform: translateX(2px); /* 起始位置，不移动 */
  }
  100% {
    transform: translateX(0); /* 起始位置，不移动 */
  }
}
.fixed_nav {
  width: 100px;
  background: #ffffff;
  border-radius: 30px;
  text-align: center;
  position: fixed;
  right: 0px;
  top: 25%;
  // margin-top: -280px;
  padding-bottom: 20px;
  z-index: 1000;

  box-shadow: -1px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.fixedNav_tit {
  font-size: 16px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  color: #ffffff;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 10px 10px 0px 0px;
  padding: 10px 0;
}
.fixedNav_item {
  padding: 18px 0 0;
  cursor: pointer;
  display: block;
  position: relative;
  &:hover {
    .fixedNav_subT {
      color: #222;
    }
  }
}
.imbox {
  position: relative;
}
.fixedNav_pic {
  width: 46px;
  height: 46px;
}
.fixedNav_subT {
  // padding: 4px 0 4px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  line-height: 24px;
  letter-spacing: 0.56px;
}
.downloadQr {
  transform: scale(0);
  transform-origin: 50% 95%;
  transition: all 0.4s;
  width: 229px;
  height: 305px;
  position: absolute;
  z-index: 1000;
  top: 0px;
  right: 106px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.95) 40%,
    rgba(255, 255, 255, 0.76) 100%
  );
  border-radius: 25.714px;
  box-shadow: 8.571px 8.571px 42.857px 0px rgba(255, 107, 0, 0.05);
  border: 1px solid transparent;
  background-clip: padding-box, border-box;

  background-origin: padding-box, border-box;

  background-image: linear-gradient(to right, #fff, #fff),
    linear-gradient(
      180deg,
      rgba(255, 255, 255, 1) 40%,
      rgba(255, 225, 195, 1) 100%
    );
  .qRlogo {
    width: 181.568px;
    height: 191px;
    position: absolute;
    top: 64px;
    left: 23.65px;
    z-index: -1;
  }
  .tit {
    color: #ff720c;
    text-align: center;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 23px;

    padding-top: 33px;
    font-family: YouSheBiaoTiHei;
    width: 100%;
  }
  .qr {
    // border-radius: 5px;
    border: 1px solid #ffb74a;
    width: 130px;
    height: 130px;
    margin: 17px auto 17px auto;
    background: #fff;
    position: relative;
  }
  .note {
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .tip {
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
  }
}
.downloadQr.showQr {
  transform: scale(1);
}
</style>
