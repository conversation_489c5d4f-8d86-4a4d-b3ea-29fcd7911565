<template>
  <div class="safe_width">
    <div class="login_header_wrap spaceBetween">
      <router-link to="/">
        <img class="logo_pic" src="../../../static/imgs/text_Logo.svg" />
      </router-link>

      <div class="spaceEnd login_header_nav border-gradient-color-style">
        <!-- <router-link to="/">首页</router-link> -->
        <router-link class="spaceStart" to="/">
          <!-- <img style="margin-right: 6px" src="../../../static/phone.png" /> -->
          <!-- <span>下载APP</span> -->
          <img src="../../../static/imgs/login_download.svg" alt="" />
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  watch: {},
  created() {},
  methods: {
    getBreadcrumb() {},
  },
};
</script>

<style scoped>
.login_header_wrap {
  padding: 30px 0;
  height: 90px;
}
.logo_pic {
  width: 166.66px;
  height: 50px;
}
.login_header_nav img {
  width: 146.5px;
  height: 43px;
}
.login_header_nav {
  width: 146.5px;
  height: 43px;
  font-size: 15.4px;
  font-family: PingFang SC;
  color: rgba(255, 122, 0, 1);
  display: flex;

  /* padding: 18px 48px; */
  justify-content: center;
  align-items: center;

  border: 2.571px solid transparent;

  border-radius: 16px;

  /* background-image: linear-gradient(to right, #fff, #fff),
    linear-gradient(
      180deg,
      rgba(255, 122, 0, 1) 8%,
      rgba(255, 199, 0, 0.38) 20%
    ); */
  border-radius: 60px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  position: relative;
}
</style>
