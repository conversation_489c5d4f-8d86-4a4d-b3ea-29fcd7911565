<template>
  <div class="game_wrap" @click="playPage">
    <div v-if="dataItem.productUnit == 'NEW'" class="game_wrap_new">
      <img
        src="../../../static/imgs/newIcon.svg"
        style="width: 43px; height: 20px; margin-top: -3px"
      />
    </div>
    <!-- <div v-if="dataItem.productUnit === 'HOT'" class="game_wrap_new">
      <img
        src="https://images2.kkzhw.com/mall/images/20240618/hotC.jpg"
        style="width: 60px; height: 30px; object-fit: cover"
      />
    </div> -->
    <div class="game_wrap_pic">
      <el-image
        :src="dataItem.icon"
        style="width: 100%; height: 100%"
        fit="cover"
      ></el-image>
    </div>
    <div>{{ dataItem.name }}</div>
  </div>
</template>

<script>
export default {
  props: {
    dataItem: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  watch: {},
  created() {},
  methods: {
    // 跳转
    playPage() {
      this.cashFlagDate();
      this.$emit('gamePage');
    },
    // 缓存游戏数据
    cashFlagDate() {
      var date = this.dataItem;
      if (date.name === '逆水寒手游') {
        return;
      }
      var cashFlagDateArr = JSON.parse(
        localStorage.getItem('cashFlagDateArr2')
      );
      // 没有缓存的时候
      if (!cashFlagDateArr) {
        cashFlagDateArr = [];
        cashFlagDateArr.unshift(date);
        localStorage.setItem(
          'cashFlagDateArr2',
          JSON.stringify(cashFlagDateArr)
        );
        return;
      }
      // 有缓存分有何没有
      var num = cashFlagDateArr.findIndex((v) => {
        return v.id == date.id;
      });
      if (num == -1) {
        if (cashFlagDateArr.length >= 8) {
          cashFlagDateArr.pop();
        }
        cashFlagDateArr.unshift(date);
        localStorage.setItem(
          'cashFlagDateArr2',
          JSON.stringify(cashFlagDateArr)
        );
      }
    },
  },
};
</script>

<style scoped>
.game_wrap {
  width: 80px;
  min-height: 105px;
  text-align: center;
  color: #222;
  font-family: 'PingFang SC';
  font-size: 14px;
  margin-bottom: 20px;
  margin-right: 24px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  font-weight: 400;
  /* line-height: normal; */
  line-height: 24px;
  letter-spacing: 0.56px;
}

.game_wrap:nth-child(11n) {
  margin-right: 0;
}
.game_wrap:hover {
  transform: translateY(-5px);
}
.game_wrap_pic {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5.45px;
}
.game_wrap_new {
  float: left;
  position: absolute;
  top: -2px;
  right: -1px;
  z-index: 100;
  /* padding-right: 0.2rem; */
}
</style>
