<template>
  <div class="share_page">
    <div class="spaceBetween line0">
      <div class="tit spaceBetween">
        <div class="img">
          <img :src="shopDetailJson.pic" class="productPic" />
        </div>
        <div class="note">
          <div class="sn">{{ shopDetailJson.productSn }}</div>
          <div class="cate spaceBetween">
            <div class="spaceStart">
              <img class="share1" src="../../../static/share/share1.png" />
              <div>{{ shopDetailJson.gameAccountQufu }}</div>
            </div>
            <div class="spaceStart">
              <img class="share1" src="../../../static/share/share5.png" />
              <div>{{ getGetAttribute('职业') }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="code_warp spaceBetween">
        <div class="code_note">扫码查看详情</div>
        <div class="qrcode_box">
          <canvas id="qrcode_share" style="width: 60px; height: 60px"></canvas>
        </div>
      </div>
    </div>

    <div class="spaceBetween line1">
      <div v-for="item in line1List" class="spaceBetween attr">
        <div>{{ item.label }}</div>
        {{ getGetAttribute(item.name) }}
      </div>
    </div>

    <div v-if="line2attr && line2attr.length" class="spaceBetween line2">
      <div class="line2_tit">
        <div class="line2_content spaceStart">
          <div v-for="(item, index) in line2attr" class="spaceStart item">
            <div>{{ fakeName(item) }}</div>
            <img
              v-if="iconFilter(item)"
              :src="iconFilter(item)"
              class="tag_tedian_pic"
            />
            <div v-if="index < line2attr.length - 1">,</div>
          </div>
        </div>
      </div>
    </div>

    <div class="spaceBetween line3">
      <div class="line3_tit">
        <div class="line3_content text_linThree">
          {{ shopDetailJson.description }}
        </div>
      </div>
    </div>
    <div class="spaceEnd line4">
      <div class="line4_tit spaceEnd">
        <div class="price">¥ {{ shopDetailJson.price }}</div>
      </div>
    </div>

    <div v-if="line5List && line5List.length" class="line5">
      <div class="line5_tit"></div>
      <div class="line5_content">
        <div
          v-for="(item, index) in line5List"
          :key="index"
          class="goodsHome_shopItem spaceBetween"
        >
          <div class="goodsHome_shopItem_box">
            <div class="goods_shopItem_pic">
              <img
                v-if="item.stock == 0 || item.stock == 1"
                src="../../../static/soled.jpg"
                class="soled_pic"
              />
              <img :src="item.pic" />
            </div>
            <div class="goods_showItem_right">
              <div class="goodsHome_Item_header">
                <div
                  v-if="item.subTitle"
                  class="subTitleMp"
                  v-html="fakeName(item.subTitle)"
                />
              </div>
            </div>
            <div class="price">¥ {{ item.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="spaceBetween line6">
      <img
        class="img"
        src="https://images2.kkzhw.com/mall/statics/pc/LOGO3.png"
      />
      <div class="note">*以上商品成交记录仅供参考</div>
    </div>
  </div>
</template>

<script>
import linkConfig from '../../../config/link.js';
import { sameProductList } from '@/api/myPost.js';

import { getDetail } from '@/api/playDetail.js';

import QRCode from 'qrcode';

export default {
  props: {
    productId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // flag_id: '',
      shopDetailJson: {},
      // 二维码标识串
      qrcodeText: '',
      // 二维码尺寸
      qrcodeSize: 56,
      // 最终生成的二维码图片
      qrcodeSrc: '',
      productAttributeValueList: [],
      line1List: [
        {
          name: '国色值',
          label: '国色值',
        },
        {
          name: '衣品',
          label: '衣品值',
        },
        {
          name: '评分',
          label: '评分',
        },
        {
          name: '已使用天赏石',
          label: '已使用天赏石',
        },
      ],
      line2attr: [],
      line5List: [],
    };
  },
  mounted() {
    if (this.productId) {
      this.qrcodeText =
        linkConfig.linkH5url +
        '/pages/accountDetail/accountDetail?productId=' +
        this.productId;
      this.getShopDtFun();
      this.makeSrc();
    }
  },
  methods: {
    getGetAttribute(name) {
      const findIt = this.productAttributeValueList.find(
        (ele) => ele.productAttributeName == name,
      );
      if (findIt) {
        return findIt.value;
      }
      return '';
    },
    fakeName(text) {
      if (!text) {
        return text;
      }
      return text
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },
    closeempty() {
      uni.navigateBackCustom();
    },
    // 获取商品数据
    getShopDtFun() {
      getDetail(this.productId).then((res) => {
        if (res.code == 200) {
          this.shopDetailJson = res.data.product;
          this.productAttributeValueList = res.data.productAttributeValueList;
          this.filterAttr();
          this.getSameSoldProduct();
        }
      });
    },
    getSameSoldProduct() {
      let data = {
        offDay: 30,
        pageSize: 3,
        price: this.shopDetailJson.price,
        productId: this.shopDetailJson.id,
      };
      sameProductList(data).then((res) => {
        if (res.code == 200) {
          this.line5List = res.data.list || [];
        }
      });
    },
    filterAttr() {
      let tempList = [];
      this.productAttributeValueList.forEach((element) => {
        let value = element.value;
        if (value) {
          value = value.split(',');
          value.forEach((str) => {
            if (/\[(核|绝|钱)\]/.test(str)) {
              tempList.push(str);
            }
          });
        }
      });
      this.line2attr = tempList;
    },
    iconFilter(v) {
      if (v.indexOf('[绝]') !== -1) {
        return '../../../static/push/jue.png';
      } else if (v.indexOf('[钱]') !== -1) {
        return '../../../static/push/price.png';
      } else if (v.indexOf('[核]') !== -1) {
        return '../../../static/push/he.png';
      }
    },
    makeSrc() {
      const picUrl = this.qrcodeText;
      let opts = {
        errorCorrectionLevel: 'L', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 2, //二维码留白边距
        text: picUrl, //二维码内容
        width: 60,
        height: 60,
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('qrcode_share');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.share_page {
  padding: 20px;
  position: absolute;
  top: 10%;
  left: 50%;
  width: 375px;
  margin-left: -187px;
  z-index: 1200;
  background-color: #fbf7ec;
  .tit {
    border: solid 3px #f7e09b;
    border-radius: 20px;
    align-items: flex-start;
    position: relative;
    height: 60px;
    .img {
      border: solid 3px #e8b646;
      border-radius: 20px;
      overflow: hidden;
      width: 60px;
      height: 60px;
      position: absolute;
      top: -3px;
      left: -3px;
      .productPic {
        width: 60px;
        height: 60px;
      }
    }
    .note {
      margin-left: 65px;
      margin-right: 5px;
      .sn {
        color: #f19836;
        font-size: 24px;
      }
      .cate {
        font-size: 12px;
        color: #6f3d1f;
        margin-top: 5px;
        .share1 {
          width: 15px;
          height: 15px;
          margin-right: 5px;
        }
      }
    }
  }

  .code_warp {
    .code_note {
      margin-right: 3px;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      width: 10px;
      font-size: 10px;
      line-height: 10px;
      color: #dda45f;
    }
    .qrcode_box {
      border: solid 1px #f2dd96;
      width: 62px;
      height: 62px;
    }
  }
  .line1 {
    flex-wrap: wrap;
    font-size: 12px;
    font-weight: 700;
    color: #6f3d1f;
    .attr {
      width: 38%;
      padding-left: 5px;
      height: 12px;
      border-left: solid 3px #f3b83f;
      margin-top: 13px;
    }
  }
  .line2 {
    margin: 20px 0 0 0;
    margin-left: -10px;
    .tag_tedian_pic {
      width: 15px;
      height: 15px;
      margin-left: 3px;
    }
    .line2_tit {
      .line2_content {
        flex-wrap: wrap;
        height: 40px;
        margin: 30px 0 0 10px;
        line-height: 20px;
        overflow: hidden;
        padding: 0 5px;
      }
      font-size: 12px;
      width: 350px;
      height: 85px;
      background: url('../../../static/share/share4.png') no-repeat;
      background-size: cover;
    }
  }
  .line3 {
    margin-left: -10px;
    margin-top: 10px;
    .line3_tit {
      .line3_content {
        margin: 30px 13px 0 10px;
        line-height: 24px;
        padding: 0 5px;
      }
      font-size: 12px;
      width: 350px;
      height: 116px;
      background: url('../../../static/share/share3.png') no-repeat;
      background-size: cover;
    }
  }
  .line4 {
    margin-top: 3px;
    .line4_tit {
      width: 220px;
      height: 37px;
      align-items: center;
      background: url('../../../static/share/share2.png') no-repeat;
      background-size: cover;
      .price {
        color: #cb4427;
        font-size: 19px;
        font-weight: 700;
        width: 135px;
        text-align: center;
        line-height: 19px;
      }
    }
  }
  .line5 {
    margin-top: -12px;
    .line5_tit {
      width: 105px;
      height: 18px;
      background: url('../../../static/share/share0.png') no-repeat;
      background-size: cover;
    }
    .line5_content {
      background: #fff;
      border-radius: 5px;
      .goodsHome_shopItem_box {
        margin: 10px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .price {
          color: #ce624f;
          font-size: 13px;
          font-weight: 700;
          flex: 1;
          text-align: center;
        }
        .goods_shopItem_pic {
          width: 25%;
          margin-right: 5px;
          height: 50px;
          text-align: center;
          position: relative;
          .soled_pic {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9;
          }
        }
        .goods_shopItem_pic > img {
          height: 100%;
          width: 100%;
        }
        .goods_showItem_right {
          width: 50%;
          .goodsHome_Item_header {
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-box-orient: vertical;
            padding: 0px 5px 0px;
            font-size: 10px;
            color: #999999;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }
  .line6 {
    margin-top: 10px;
    .img {
      width: 150px;
      height: 35px;
    }
    .note {
      color: #dab65a;
      font-size: 12px;
    }
  }
}
</style>
