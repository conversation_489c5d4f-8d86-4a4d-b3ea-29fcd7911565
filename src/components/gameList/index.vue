<template>
  <!-- class="page_comStyle" -->
  <div style="border-radius: 30px; min-height: 700px">
    <!-- <slot name="top"></slot> -->
    <!-- <div class="gameType_wrap spaceStart">
      <div
        :class="type == '' ? 'active' : ''"
        class="gameType_item"
        @click="chooseType(0)"
      >
        全部游戏
      </div>
      <div
        :class="type == 1 ? 'active' : ''"
        class="gameType_item"
        @click="chooseType(1)"
      >
        网络游戏
      </div>
      <div
        :class="type == 2 ? 'active' : ''"
        class="gameType_item"
        @click="chooseType(2)"
      >
        手机游戏
      </div>
    </div> -->
    <!-- { title: '   ' }, -->
    <myTab
      :tabs="[
        { title: '全部游戏' },
        { title: '网络游戏' },
        { title: '手机游戏' },
        { title: ' ' },
      ]"
      :class="`tab${type}`"
      class="game_tab_btn"
      style="margin-top: 16rpx"
      border-class="tab-border"
      @click="chooseType"
    >
      <template style="position: relative">
        <div class="gameType_tip">
          <IconFont
            :size="40"
            icon="game-tip"
            style="margin-right: 12px"
          />禁止未成年参与交易
        </div>
        <div class="game_search">
          <zimuList ref="zimuList" @chooseLetter="choseLetter" />
          <div class="game_search_input">
            <el-input
              v-model="searchValue"
              placeholder="输入游戏名称"
              @input="searchValueChange"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="doSearch"
              ></i>
            </el-input>
          </div>
        </div>
        <p
          v-show="tipFlag"
          style="
            position: absolute;
            right: 65px;
            top: 101px;
            font-size: 13.712px;
            color: #ff720c;
            font-family: PingFang SC;
          "
        >
          暂时没有搜索到您要的游戏
        </p>
        <cashGame
          v-if="needCash"
          :game-list-all="gameListAll"
          :game-list="gameList"
          :page-to="pageTo"
          @playPage="playPage"
        />
        <div
          class="gameAll_wrap spaceStart"
          style="align-items: flex-start; padding: 40px 37px 20px 37px"
        >
          <gameItem
            v-for="(item, index) in gameList"
            :key="index"
            :data-item="item"
            @gamePage="playPage(item)"
          />
        </div>
      </template>
    </myTab>

    <!-- 字母筛选 -->
    <!-- <zimuList ref="zimuList" @chooseLetter="choseLetter" />

    <cashGame
      v-if="needCash"
      :game-list-all="gameListAll"
      :game-list="gameList"
      :page-to="pageTo"
      @playPage="playPage"
    /> -->

    <!-- 游戏数据 -->
    <!-- <div
      class="gameAll_wrap spaceStart"
      style="align-items: flex-start; padding: 0px 50px"
    >
      <gameItem
        v-for="(item, index) in gameList"
        :key="index"
        :data-item="item"
        @gamePage="playPage(item)"
      />
    </div> -->
  </div>
</template>

<script>
import _ from 'lodash';
import gameItem from '@/components/gameItem/index';
import zimuList from '@/components/zimuList/index';
import cashGame from '@/components/cashGame/index';
import myTab from '@/components/myTab/index';
import { getGameList } from '@/api/index2.js';
export default {
  name: 'GameList',
  components: { gameItem, zimuList, cashGame, myTab },
  props: {
    pageTo: {
      type: String,
      default: '',
    },
    needCash: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: 0,
      gameList: [],
      hotListGame: [],
      gameListAll: [],
      gameListmb: [],
      gameListpc: [],
      searchValue: '',
      tipFlag: false,
    };
  },
  mounted() {
    this.initGame();
  },
  methods: {
    searchValueChange(v) {
      this.resetGameList();
      this.gameList = this.gameList.filter((ele) => {
        return ele.name.toLowerCase().indexOf(v.toLowerCase()) !== -1;
      });
      if (this.gameList && this.gameList.length == 0) {
        this.tipFlag = true;
      } else {
        this.tipFlag = false;
      }
    },
    doSearch() {},
    playPage(item) {
      this.$emit('playPage', item);
    },
    choseLetter(letter) {
      this.resetGameList();
      if (letter !== '全部游戏') {
        this.gameList = this.gameList.filter((ele) => {
          let key = ele.keywords[0] || '';
          return key.toLowerCase() === letter.toLowerCase();
        });
      }
    },
    chooseType(num) {
      this.type = num || 0;
      this.resetGameList();
      this.$refs.zimuList.reset();
    },
    resetGameList() {
      if (this.type === 0) {
        this.gameList = this.copyList(this.gameListAll);
      } else if (this.type === 1) {
        this.gameList = this.copyList(this.gameListpc);
      } else if (this.type === 2) {
        this.gameList = this.copyList(this.gameListmb);
      }
    },
    copyList(list) {
      return _.cloneDeep(list);
    },
    initGame() {
      getGameList().then((res) => {
        if (res.code == 200) {
          var arr = [];
          res.data.forEach((v) => {
            if (v.index == 1) {
              arr.push(v);
            }
          });
          this.hotListGame = arr;
          this.gameListAll = res.data;
          // sort大的排前
          this.gameListAll.sort((a, b) => {
            return b.sort - a.sort;
          });
          this.gameListmb = [];
          this.gameListpc = [];
          this.gameListAll.forEach((ele) => {
            if (ele.categoryTag === 'mobile_game') {
              // 手游
              this.gameListmb.push(ele);
            } else if (ele.categoryTag === 'pc_game') {
              // 端游
              this.gameListpc.push(ele);
            }
          });
          this.gameList = this.copyList(this.gameListAll);
        }
      });
    },
  },
};
</script>
<style lang="scss">
// .game_tab_btn .tab-btn {
//   display: flex !important;
//   // flex: 0 !important;
//   align-items: center !important;
//   justify-content: center !important;
//   font-size: 20px !important;
//   width: 192px !important;
//   // height: 60px !important;
//   letter-spacing: 0.88px;
//   flex: none !important;
//   height: 60px !important;
//   // border-left: 20px solid #fdf4e9 !important;
//   // padding: 0px;
//   // padding: 2.5px 0;
//   font-family: 'PingFang SC' !important;
//   // background: transparent !important;
//   // color: rgba(0, 0, 0, 0.4);
//   background: none !important;
//   color: rgba(0, 0, 0, 0.4) !important;
//   border-radius: 24px !important;

//   // border-left: 10px solid #fdf4e9 !important;

//   &:nth-child(4) {
//     background: #fff1e2;
//   }
//   .right {
//     &::before {
//       // background: #ffddbe;
//     }
//     &::after {
//       background: #ffddbe !important;
//       // background: red;
//       // height: 36px;
//       right: -29px !important;
//       bottom: 0.3px !important;
//       width: 29px !important;
//       height: 30px !important;
//       border-bottom-left-radius: 26px !important;
//     }
//   }
//   .left {
//     &::before {
//       // background: #ffddbe;
//     }
//     &::after {
//       background: #ffddbe !important;
//       left: -29px !important;
//       bottom: 0.3px !important;
//       width: 29px !important;
//       height: 30px !important;
//       border-bottom-right-radius: 26px !important;
//     }
//   }
// }
// .game_tab_btn .tab-btn:nth-child(4) {
//   color: transparent;
//   pointer-events: none;
//   background: transparent;
//   .border-outside {
//     background: #fff1e2 !important;
//     border-radius: 24px 24px 0px 26px !important;
//   }
// }

// .game_tab_btn .selected {
//   color: #ff7a00 !important;
//   height: 83px !important;
//   /* Title/Large */
//   font-family: YouSheBiaoTiHei !important;
//   font-size: 32px !important;
//   line-height: 38px !important;
//   font-weight: 400 !important;
//   padding-top: 11.5px !important;
//   // border-radius: 27px 27px 0px 0px !important;
//   border: 3px solid #ffddbe !important;
//   background: #fff !important;
//   border-bottom: none !important;
//   // padding-top: 19.711px !important;
//   bottom: -3px !important;
//   letter-spacing: 0px !important;
//   border-radius: 24px 24px 0px 0px !important;
// }
// .game_tab_btn .tab-body {
//   border: solid 3px #ffddbe !important;
//   &.no-top-right {
//     border-top-right-radius: 28px !important;
//   }
// }
</style>
<style lang="scss" scoped>
$noneBackground: #fff1e2;

::v-deep .game_tab_btn .tab-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 20px !important;
  width: 192px !important;
  letter-spacing: 0.88px;
  flex: none !important;
  height: 60px !important;
  font-family: 'PingFang SC' !important;
  background: none !important;
  color: rgba(0, 0, 0, 0.4) !important;
  border-radius: 24px !important;

  &:nth-child(4) {
    background: #fff1e2;
  }
  .right {
    &::after {
      background: #ffddbe !important;
      right: -29px !important;
      bottom: 0.2px !important;
      width: 29px !important;
      height: 30px !important;
      border-bottom-left-radius: 26px !important;
    }
  }
  .left {
    &::after {
      background: #ffddbe !important;
      left: -29px !important;
      bottom: 0.2px !important;
      width: 29px !important;
      height: 30px !important;
      border-bottom-right-radius: 26px !important;
    }
  }
}

::v-deep .game_tab_btn .tab-btn:nth-child(4) {
  color: transparent;
  pointer-events: none;
  background: transparent;
  .border-outside {
    background: #fff1e2 !important;
    border-radius: 24px 24px 0px 26px !important;
  }
}

::v-deep .game_tab_btn .selected {
  color: #ff7a00 !important;
  height: 83px !important;
  font-family: YouSheBiaoTiHei !important;
  font-size: 32px !important;
  line-height: 38px !important;
  font-weight: 400 !important;
  padding-top: 11.5px !important;
  border: 3px solid #ffddbe !important;
  background: #fff !important;
  border-bottom: none !important;
  bottom: -3px !important;
  letter-spacing: 0px !important;
  border-radius: 24px 24px 0px 0px !important;
}

::v-deep .game_tab_btn .tab-body {
  border: solid 3px #ffddbe !important;
  &.no-top-right {
    border-top-right-radius: 28px !important;
  }
}

.gameAll_wrap {
  flex-wrap: wrap;
}

.gameType_wrap {
  font-size: 16px;
  color: #909090;
}

.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}

.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}

::v-deep .tab0 .tab-btn:nth-child(2) {
  border-left: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 0px 17px !important;
  }
}

::v-deep .tab0 .tab-btn:nth-child(3) {
  border-left: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 0px 0px !important;
  }
}

::v-deep .tab1 .tab-btn:nth-child(1) {
  border-right: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 17px 0px !important;
  }
}

::v-deep .tab1 .tab-btn:nth-child(3) {
  border-left: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 0px 17px !important;
  }
}

::v-deep .tab2 .tab-btn:nth-child(1) {
  border-right: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 0px 0px !important;
  }
}

::v-deep .tab2 .tab-btn:nth-child(2) {
  border-right: 7.23px solid $noneBackground !important;
  .border-outside {
    border-radius: 24px 24px 17px 0px !important;
  }
}

::v-deep .tab2 .tab-btn:nth-child(3) {
  .right {
    &::after {
      border-bottom-left-radius: 28px !important;
    }
  }
}

.gameType_tip {
  position: absolute;
  top: -72px;
  right: 0px;
  color: #a86139;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  display: flex;
  align-items: center;
  font-weight: 400;
}
.game_search {
  display: flex;
  align-items: baseline;
}
.game_search_input {
  width: 242px;
  height: 46px;
  margin: 0px auto;
  display: flex;
  align-items: center;
  border-radius: 50px;
  border: 1.693px solid #ff6b00;
  padding: 0px 0px 0px 23px;
  .el-input__icon {
    font-size: 21.426px;
    width: 21.426px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: rgba(117, 117, 117, 1);
  }
  /deep/ .el-input__inner {
    padding: 0px;
  }
  ::v-deep input {
    height: 42px;
    border-radius: 48px;
    border: none;
    // color: #c0c0c0;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 14px;
    letter-spacing: 0.36px;
  }
}
.game_tab_btn {
  &:not(.selected) {
    width: 100%;
    height: 100%;
    background: none;
    border-radius: 24px 24px 0px 0px !important;
  }
  /deep/.border-outside {
    width: 100%;
    height: 100%;
    height: 60px !important;
    background: transparent !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 27px;
    // border-radius: 24px 24px 0px 0px !important;
    background: #f5f5f5 !important;
  }
}
</style>
