<template>
  <transition name="viewer-fade">

    <div ref="el-image-viewer__wrapper" :style="{ 'z-index': viewerZIndex }" tabindex="-1"
      class="el-image-viewer__wrapper" @wheel="handleMouseWheel">
      <div class="el-image-viewer__mask el-image-viewer__mask2"
        style="background: rgba(0, 0, 0, 0.65); backdrop-filter: blur(3px)" @click.self="handleMaskClick"></div>
      <!-- CLOSE -->
      <span class="el-image-viewer__btn el-image-viewer__close" @click="hide">
        <i class="el-icon-close"></i>
      </span>
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span :class="{ 'is-disabled': !infinite && isFirst }" class="el-image-viewer__btn el-image-viewer__prev"
          @click="prev">
          <i class="el-icon-arrow-left" />
        </span>
        <span :class="{ 'is-disabled': !infinite && isLast }" class="el-image-viewer__btn el-image-viewer__next"
          @click="next">
          <i class="el-icon-arrow-right" />
        </span>
      </template>
      <!-- ACTIONS -->
      <div v-if="urlList && urlList.length > 0" style="
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          font-family: PingFang SC;
          font-size: 17px;
          font-weight: 500;
          position: absolute;
          z-index: 99998;
          border-radius: 10px;
          height: 100px;
        " :style="{ width: urlList.length > 6 ? '800px' : 120 * urlList.length+'px' }"
        class="el-image-viewer__btn el-image-viewer__actions">
        <div style="    position: absolute;
    bottom: 73px;z-index: 999999;">{{ index + 1 }}/{{ urlList.length }}</div>
        <!-- style="position: absolute"  -->
        <div class="thumb-wrap">
          <span v-if="urlList.length > 6" style="  
    font-size: 26px;
    margin-right:10px;
    color: #fff; position: relative;
          z-index: 999999;" @click="prev">
            <i class="el-icon-arrow-left" />
          </span>
          <ul @wheel.stop="handleWheel" ref="thumbList">
            <li v-for="(img, index2) in urlList" :key="img" :class="{ active: index === index2 }"
              @click="showCurrent(index2)">
              <img :src="img">
            </li>
          </ul>
          <span v-if="urlList.length > 6" style="
    font-size: 26px;
    margin-left:10px;
    color: #fff;" @click="next">
            <i class="el-icon-arrow-right" />
          </span>
        </div>

        <!-- {{ index + 1 }}/{{ urlList.length }} -->
      </div>
      <!-- CANVAS -->

      <div ref="targetDiv" class="el-image-viewer__canvas img-list-wrap el-image-viewer__canvas_table">
        <div v-if="index == 0 && tableDataFlag" @wheel.stop class="priviewTableStyle">
          <!-- <img class="topTips_tit_play" src="../../static/imgs/dialog_bk.png" alt="" /> -->
          <div class="topTips_tit topTips_tit_play ">
            <div style="position: relative;z-index: 99;">
              <img style="height: 43px;" src="../../static/imgs/text_Logo.svg" alt="" />
            </div>
            <div style="margin-top: 20px;" class="spaceBetween">
              <div class="spaceStart">
                <div>商品编号｜{{ productSn }}</div>
                <div style="margin-left: 20px;" class="spaceBetween topGame_price">
                  <div class="spaceStart" style="    font-family: 'PingFang SC';
    color: #ff720c;">
                    ¥ {{ price }}
                    <div v-if="product&&getJJPrice(product)" class="priviewJjPrice">
                              <img src="../../static/imgs/reducePrice.png" alt="" style="width: 18px;margin-left: -3px;" />已降价¥ {{
                              getJJPrice(product) }}
                            </div>
                  </div>
                </div>
              </div>
              <div>
                <a style="font-size: 14px;color: #ff720c;margin-right: 10px;" @click="popupCustom" target="_blank">咨询</a>
              <a style="font-size: 14px;color: #ff720c;" :href="`/gd/${productSn}`" target="_blank">前往购买</a>
              </div>
            </div>
            <!-- <el-radio-group class="" v-model="tabPosition" style="margin: 10px 0px 0px 0px;">
    <el-radio-button label="detail">商品详情</el-radio-button>
    <el-radio-button label="right">卖家说</el-radio-button>
  </el-radio-group> -->
          </div>
        
          <el-table class="priviewTableBk"
            style="margin-top: 10px;border-radius:5px;padding: 0px 0px 0px 20px;margin-right: 20px;" max-height="550"
            v-if="tableData.length > 1" :data="tableData" :span-method="arraySpanMethod">
            <el-table-column prop="name" width="140">
              <template slot-scope="scope">
                <span v-html="scope.row.name"></span>
              </template>
            </el-table-column>
            <el-table-column prop="value" width="840">
              <template slot-scope="scope">
                <span v-html="scope.row.selectType == 2 ? scope.row.name : scope.row.value"></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- :style="imgStyle" :style="{ transform: `scale(${scale})` }"-->
        <img v-for="(url, i) in urlList" v-if="tableDataFlag ? i === index && index > 0 : i === index" ref="img"
          :key="url" :src="currentImg" :style="imgStyle" style="border-radius: 12px;" class="el-image-viewer__img "
          @load="handleImgLoad" @error="handleImgError" @mousedown="handleMouseDown" />
        <!-- @mousedown="handleMouseDown" -->
      </div>
      
    </div>
    <!-- <el-dialog
        :visible.sync="dialogVisibleDaishou"
        width="30%"
        center
        title="添加客服微信咨询"
      >
        <div class="code_wrap_imgage">
          <canvas
            id="QRCode_header_kfwx"
            style="width: 150px; height: 150px"
          ></canvas>
        </div>
        <div style="text-align: center; padding-top: 20px">
          扫码添加客服进行咨询，微信号：{{ kfObj['微信号'] }}
        </div>
      </el-dialog> -->
  </transition>
</template>

<script>
import { on, off } from 'element-ui/src/utils/dom';
import { rafThrottle, isFirefox } from 'element-ui/src/utils/util';
import { PopupManager } from 'element-ui/src/utils/popup';
import { getKfList, getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import isLogin from '@/utils/isLogin';
const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen',
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original',
  },
};

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';
import {
  readHistoryCreate,

} from '@/api/playDetail';
export default {
  name: 'ElImageViewer',
  props: {
    urlList: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    tableDataFlag: {
      type: Boolean,
      default: false,
    },
    productSn: {
      type: String,
      default: ''
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    onSwitch: {
      type: Function,
      default: () => { },
    },
    onClose: {
      type: Function,
      default: () => { },
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
    appendToBody: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    gameSysinfoReadcount: {
      type: Number,
      default: 0,
    },
    gameSysinfoCollectcount: {
      type: Number,
      default: 0,
    },
    price: {
      type: Number,
      default: 0,
    },
    product: {
      type: Object,
      default: () => { },
    }
  },

  data() {
    return {
      kfObj:{},
      tabPosition:'detail',
      dialogVisibleDaishou:true,
      scale: 1,
      minScale: 0.5,
      maxScale: 5,
      zoomRate: 0.1,
      index: this.initialIndex,
      isShow: false,
      infinite: true,
      loading: false,
      mode: Mode.CONTAIN,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      distanceToBottom: 0,
      targetDiv: ''
    };
  },
  computed: {
    isSingle() {
      return this.urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentImg() {
      return this.urlList[this.index];
    },
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${this.scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform 0s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
      };
      if (this.mode === Mode.CONTAIN) {
        style.maxWidth = style.maxHeight = '100%';
      }
      return style;
    },
    viewerZIndex() {
      const nextZIndex = PopupManager.nextZIndex();
      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
    },
  },
  watch: {
    index: {
      handler: function (val) {
        this.reset();
        this.onSwitch(val);
      },
    },
    currentImg(val) {
      this.$nextTick((_) => {
        const $img = this.$refs.img[0];
        if (!$img.complete) {
          this.loading = true;
        }
      });
    },
  },
  mounted() {
    getKfList({
            game: this.product.brandName,
            type: '咨询客服',
          }).then((res) => {
            if (res.code == 200) {
              let list = res.data || [];
              if (list.length) {
                this.kfObj = _.sample(list);
              }
            }
    });
    this.deviceSupportInstall();
    if (this.appendToBody) {
      document.body.appendChild(this.$el);
    }

    const timeoutId = setTimeout(() => {
      if (this.product && Object.keys(this.product).length > 0) {
        if (this.product.price < 5000000) {
          readHistoryCreate({
            productId: this.product.id,
            categoryId: this.product.productCategoryId,
            productPrice: this.product.price,
          });
        }
      }
    }, 1000);

    this.$once('hook:beforeDestroy', () => {
      clearTimeout(timeoutId);
    });
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
  },
  destroyed() {
    // if appendToBody is true, remove DOM node after destroy
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  methods: {
    createWx() {
      let qrcodeImg = `${this.kfObj.wxurl}`;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 150, //宽
        height: 150, //高
        text: qrcodeImg, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_kfwx');
      QRCode.toCanvas(msg, qrcodeImg, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
    popupCustom() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
      } else {
        if (this.kfObj.wxurl && this.kfObj['微信号']) {
          this.dialogVisibleDaishou = true;
          this.$nextTick(() => {
            this.createWx();
          });
        } else {
          getMemberHisKFList({
            cateId: this.product.productCategoryId,
            productId: this.product.id,
          }).then((res) => {
            if (res.code == 200) {
              const findKf = res.data;
              if (findKf) {
                const imcode = findKf;
                const sessionId = `p2p-${imcode}`;
                if (window.__xkit_store__) {
                  const { nim, store } = window.__xkit_store__;
                  m2kfTalk({
                    cateId: this.product.productCategoryId,
                    kfIM: imcode,
                  });
                  if (store.sessionStore.sessions.get(sessionId)) {
                    store.uiStore.selectSession(sessionId);
                  } else {
                    store.sessionStore.insertSessionActive('p2p', imcode);
                  }
                  this.$store.dispatch('ToggleProductCardId', this.product.id);
                  this.$store.dispatch('ToggleIM', true);
                }
              } else {
                this.$store.dispatch('ToggleIM', true);
              }
              setTimeout(()=>{
                this.hide()
              },300)
            }
          });
        }
      }
    },
    getJJPrice(item) {
      const data = JSON.parse(item.priceHistory || '[]');
      data.sort((a, b) => a.changeTime - b.changeTime);
      if (!data.length) {
        return '';
      }
      const priceDiff = data[0].price - data[data.length - 1].price;
      // 添加判断，如果价格差是负数，返回空字符串
      if (priceDiff < 0) {
        return '';
      }

      return priceDiff;
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.selectType === 2) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },
    handleWheel(event) {
      event.preventDefault();
      const scrollAmount = event.deltaY;
      const scrollableDiv = event.currentTarget;
      scrollableDiv.scrollLeft += scrollAmount;
    },
    handleMouseWheel(event) {
      event.preventDefault();
      const delta = event.deltaY;
      if (delta < 0) {
        // 鼠标滚轮向上滚动，放大图片
        if (this.scale < this.maxScale) {
          this.scale += this.zoomRate;
        }
      } else {
        // 鼠标滚轮向下滚动，缩小图片
        if (this.scale > this.minScale) {
          this.scale -= this.zoomRate;
        }
      }
    },
    showCurrent(index) {
      this.index = index;
      this.reset();
      this.onSwitch(this.index);
      this.moveThumbToCenter(index);
    },
    moveThumbToCenter(targetIndex) {
      this.scale = 1
      const thumbList = this.$refs.thumbList;
      const thumbItems = thumbList.querySelectorAll('li');
      const targetThumb = thumbItems[targetIndex];
      const thumbWidth = targetThumb.offsetWidth;
      const containerWidth = thumbList.offsetWidth;
      const targetOffsetLeft = targetThumb.offsetLeft;
      const containerScrollLeft = thumbList.scrollLeft;
      const centerOffset = containerWidth / 2 - thumbWidth / 2;

      // 计算需要滚动的距离
      const scrollTo = targetOffsetLeft - centerOffset;

      // 使用平滑滚动
      thumbList.scrollTo({
        left: scrollTo,
        behavior: 'smooth'
      });

    },
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
    },
    deviceSupportInstall() {
      this._keyDownHandler = (e) => {
        e.stopPropagation();
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions('zoomIn');
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut');
            break;
        }
      };
      this._mouseWheelHandler = rafThrottle((e) => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions('zoomIn', {
            zoomRate: 0.015,
            enableTransition: false,
          });
        } else {
          this.handleActions('zoomOut', {
            zoomRate: 0.015,
            enableTransition: false,
          });
        }
      });
      on(document, 'keydown', this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },
    deviceSupportUninstall() {
      off(document, 'keydown', this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    handleImgLoad(e) {
      this.loading = false;
    },
    handleImgError(e) {
      this.loading = false;
      e.target.alt = '加载失败';
    },
    handleMouseDown(e) {
      if (this.loading || e.button !== 0) return;

      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      const dragSpeed = 2; // 调整这个值来改变拖拽速度，值越大速度越快
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + (ev.pageX - startX) * dragSpeed;
        this.transform.offsetY = offsetY + (ev.pageY - startY) * dragSpeed;
      });
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', (ev) => {
        off(document, 'mousemove', this._dragHandler);
      });

      e.preventDefault();
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.hide();
      }
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      };
    },
    toggleMode() {
      if (this.loading) return;

      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
    },
    prev() {
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
      this.moveThumbToCenter(this.index)
    },
    next() {
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
      this.moveThumbToCenter(this.index)
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case 'clocelise':
          transform.deg += rotateDeg;
          break;
        case 'anticlocelise':
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
  },
};
</script>
<style lang="scss">
.el-image-viewer__canvas_table {
  .el-table__header {
    display: none;
  }
}

.imagePriviewCarousel .el-carousel__button {
  /* display:none */
}

.img-list-wrap {
  width: 100%;
  height: 100%;
  /* display: flex;
      justify-content: center;
      align-items: center; */


}

.img-list-wrap img {
  /* /* display: block; */
  display: block;
  object-fit: scale-down;
  transition: all 0s;
  max-width: 800px !important;
  max-height: 600px !important;
}

.thumb-wrap {
  display: flex;
  align-items: center;

  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    max-width: 700px;
    overflow-x: auto;
    /* 允许横向滚动 */
    white-space: nowrap;
    /* 设置横行滚动 */
    scrollbar-width: none;
    /* 隐藏滚动条（Firefox） */
  }

  ul::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条（Chrome, Safari） */
  }

  li {
    width: 100px;
    height: 60px;
    border: solid 1px #ececec;
    position: relative;
    margin-right: 5px;
    border-radius: 10px;
    cursor: pointer;
    box-sizing: border-box; // 添加
    flex-shrink: 0;

    &:last-child {
      margin-right: 0;
    }

    img {
      max-width: 96px;
      max-height: 56px;
      display: block;
      object-fit: scale-down;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .active {
    border: solid 2px;
    border-color: #e5242b !important;
  }
}

.el-image-viewer__mask2 {
  opacity: 1;

}
</style>
<style lang="scss" scoped>
.topTips_tit {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  line-height: 0px;
}

.topTips_tit_play {
  width: 1034px;
  // margin-top: -20px;
  position: relative;
  left: -17px;
  z-index: 99;
  padding: 0px 20px 20px 20px;
  background: #fffcf9;
  // box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08);
  box-shadow: 0 8px 6px -6px rgba(0, 0, 0, 0.08);
  border-radius: 10px 10px 0px 0px;
  /* 添加下阴影 */
}

.priviewTableStyle {
  padding: 20px;
  width: 1040px;
  position: absolute;
  z-index: 9;
  background: radial-gradient(31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%),
    radial-gradient(39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%),
    radial-gradient(71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%),
    radial-gradient(92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

.priviewTableStyle::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  position: absolute;
}

.priviewTableBk {
  background: #fffcf9;

  /deep/ td {
    padding: 5px 0px;
  }

  /deep/.el-table__row {
    background: #fffcf9;
  }

  /deep/.el-table__row:last-child {
    .cell{
      margin-bottom: 50px;
    }
  }
}
.priviewJjPrice {
  background: #fff2e6;
  font-size: 14px;
  font-family: 'PingFang SC';
  color: #ff720c;
  padding: 3px 6px;
  border-radius: 50px;
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.code_wrap_imgage {
  width: 180px;
  height: 180px;
  box-sizing: border-box;
  padding: 15px;
  margin: 0 auto;
  background: url(../../static/share_page_qrcode_bg.png) no-repeat center top;
  background-size: cover;
}
</style>
