<template>
  <div class="order_box">
    <div class="title_box">
      <div class="order_title">
        选择您要咨询的订单或商品
        <i class="el-icon-close close_btn" @click="hide"></i>
      </div>
      <div class="spaceBetween tabs">
        <div
          :class="type === 3 ? 'active' : ''"
          class="typelist_item"
          @click="changeType(3)"
        >
          浏览记录
        </div>
        <div
          :class="type === 4 ? 'active' : ''"
          class="typelist_item"
          @click="changeType(4)"
        >
          我的收藏
        </div>
        <div
          :class="type === 1 ? 'active' : ''"
          class="typelist_item"
          @click="changeType(1)"
        >
          买家订单
        </div>
        <div
          :class="type === 2 ? 'active' : ''"
          class="typelist_item"
          @click="changeType(2)"
        >
          卖家订单
        </div>
      </div>
    </div>
    <div class="scroll_content_box">
      <div v-infinite-scroll="loadMore" class="scroll_box">
        <div v-if="type === 3 || type === 4">
          <div v-if="footerList.length === 0" class="empty_order">暂无数据</div>
          <div v-else>
            <!-- <div style="height: 8px"></div> -->
            <div
              v-for="item in footerList"
              :key="item.id"
              class="collect_item commonBox"
            >
              <div
                v-if="item.createTime"
                class="spaceBetween collect_header"
                style="width: 100%"
              >
                <div>
                  {{ util.formatTime(item.createTime, 'YYYY-MM-DD HH:mm') }}
                </div>
              </div>
              <div class="collect_item_pic">
                <img :src="item.productPic" class="productPic" />
              </div>
              <div class="collect_item_right">
                <div class="spaceBetween">
                  <div class="collect_tit">
                    <div class="collect_tit_two">{{ item.productName }}</div>
                  </div>
                </div>
                <div class="collect_price spaceBetween">
                  <div class="collect_tit_name_text">
                    {{ item.productCategoryName }}
                  </div>
                  <div class="list-price-new">¥ {{ item.productPrice }}</div>
                </div>
              </div>
              <div class="collect_btn_box">
                <div class="cancle_btn" @click.stop="sendFooter(item)">
                  发送
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div v-if="orderList.length === 0" class="empty_order">暂无数据</div>
          <div v-else>
            <!-- <div style="height: 8px"></div> -->
            <div
              v-for="item in orderList"
              :key="item.id"
              class="collect_item commonBox"
            >
              <div
                v-if="item.orderSn"
                class="spaceBetween collect_header"
                style="width: 100%"
              >
                <div class="spaceStart">
                  <span class="sn-box">订单编号：{{ item.orderSn }}</span>
                </div>
                <div class="status-box">
                  {{ util.getStatus(item.orderStatus) }}
                </div>
              </div>
              <div v-for="(ele, idx) in item.orderItemList" :key="idx">
                <div
                  v-if="ele.itemType === 0"
                  style="display: flex; align-items: flex-start"
                >
                  <div class="collect_item_pic">
                    <img :src="ele.productPic" class="productPic" />
                  </div>
                  <div class="collect_item_right">
                    <div class="collect_tit">
                      <div class="collect_tit_two">{{ ele.productName }}</div>
                    </div>

                    <div class="collect_price spaceBetween">
                      <div class="collect_tit_name_text">
                        {{ item.productCategoryName }}
                      </div>
                      <div class="list-price-new">¥ {{ item.payAmount }}</div>
                      <!-- <div class="collect_footer">
                          <div
                            class="cancle_btn downShop"
                            @click.stop="sendOrder(item)"
                          >
                            发送
                          </div>
                        </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="collect_btn_box">
                <div class="cancle_btn" @click.stop="sendFooter(item)">
                  发送
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDetail } from '@/api/playDetail';
import { m2kfSendOrder, m2kfSendProduct } from '@/api/kf';
import util from '@/utils/index';
import { myOrderList, mySellerList } from '@/api/confirmOrder.js';
import { getReadHistoryList } from '@/api/playDetail.js';
import { getProductCollectionList } from '@/api/accountDetail.js';
export default {
  props: {
    myorderModal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      util,
      type: 3,
      orderList: [],
      page: 1,
      pageSize: 10,
      totalPage: 1,
      status: -1,
      showPayPopup: false,
      footerList: [],
    };
  },
  mounted() {
    this.getOrder();
  },
  methods: {
    loadMore() {
      if (this.page < this.totalPage) {
        this.page++;
        this.getOrder('add');
      }
    },
    hide() {
      this.$emit('hide');
    },
    sendFooter(productDetail) {
      getDetail(productDetail.productId||productDetail.accountItem.productId).then((res) => {
        if (res.code === 200) {
          const productAttributeList = res.data.productAttributeList;
          const { store, nim } = window.__xkit_store__;
          let sessionId = store.uiStore.selectedSession;
          const splitList = sessionId.split('-');
          const scene = splitList[0];
          splitList.shift();
          const to = splitList.join('-');
          const myAccount = store.userStore.myUserInfo.account;
          const content = `<div class="spaceBetween msg-flexstart">
            <img src="${productDetail.productPic||productDetail.accountItem.productPic}" class="msg-productImg" />
            <div>
              <div class="twoLine">${productDetail.productSubTitle?productDetail.productSubTitle.substring(0,30):productDetail.accountItem.productName.substring(0,30)}</div>
              <div class="msg-red">￥${productDetail.productPrice||productDetail.accountItem.productPrice}</div>
            </div>
          </div>`;
          let type4List = [];
          productAttributeList.forEach((ele) => {
            if (ele.type == 4) {
              type4List.push(ele.name);
            }
          });
          const { productSn, productId, productCategoryId, productStatusTxt } =
            productDetail;

          if (productStatusTxt == '已售' || productStatusTxt == '已下架') {
            type4List = [];
          }
          const attach = {
            data: {
              type: 'product',
              productSn,
              productId,
              productCategoryId,
            },
            body: {
              title: productDetail.productSubTitle?productDetail.productSubTitle.substring(0,30):productDetail.accountItem.productName.substring(0,30),
              content,
              type4List,
            },
            type: 'kk_product_msg_fed',
          };
          m2kfSendProduct({
            productId: productDetail.productId||productDetail.accountItem.productId,
            kfIM: to,
          });
          store.msgStore
            .sendCustomMsgActive({
              scene: scene,
              from: myAccount,
              to: to,
              attach: JSON.stringify(attach),
            })
            .then((res) => {
              // 让消息滚动到可视区域
              document.getElementById(`${res.idClient}`).scrollIntoView();
            })
            .catch((err) => {
              console.log('发送失败', err);
            });
          this.hide();
        }
      });
    },
    sendOrder(orderDetail) {
      const { store, nim } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = store.userStore.myUserInfo.account;
      const orderId = orderDetail.id;
      const orderSn = orderDetail.orderSn;

      const { orderItemList } = orderDetail;
      const findProduct = orderItemList.find((ele) => {
        return ele.itemType === 0;
      });
      const productSn = findProduct.productSn;
      const productId = findProduct.productId;
      const status = util.getStatus(orderDetail.orderStatus);
      const createTime = util.formatTime(
        orderDetail.createTime,
        'YYYY-MM-DD HH:mm:ss'
      );
      const content = `
      <div>
        <div class="spaceBetween msg-flexstart">
          <img src="${findProduct.productPic}" class="msg-productImg" />
          <div>
            <div class="twoLine">${findProduct.productName}</div>
            <div class="msg-red">￥${orderDetail.payAmount}</div>
          </div>
        </div>
        <div class="spaceBetween">
            <div>订单状态：</div>
            <div>${status}</div>
        </div>
        <div class="spaceBetween">
            <div>订单号：</div>
            <div>${orderDetail.orderSn}</div>
        </div>
        <div class="spaceBetween">
            <div>订单时间：</div>
            <div>${createTime}</div>
        </div>
      </div>`;

      const attach = {
        data: {
          type: 'order',
          orderSn,
          orderId,
          productId,
          productSn,
        },
        body: {
          title: '我要咨询这笔订单',
          content,
        },
        type: 'kk_order_msg_fed',
      };
      m2kfSendOrder({
        orderId,
        kfIM: to,
      });
      store.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach),
        })
        .then((res) => {
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        })
        .catch((err) => {
          console.log('发送失败', err);
        });
      this.hide();
    },
    changeType(type) {
      this.type = type;
      this.page = 1;
      this.getOrder();
    },
    getOrder(str) {
      if (str !== 'add') {
        this.orderList = [];
        this.footerList = [];
      }
      if (this.type === 1) {
        myOrderList({
          status: -1,
          pageNum: this.page,
          pageSize: 10,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.orderList = this.orderList.concat(list);
            } else {
              this.orderList = list;
            }
          }
        });
      } else if (this.type === 2) {
        mySellerList({
          status: -1,
          pageNum: this.page,
          pageSize: 10,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.orderList = this.orderList.concat(list);
            } else {
              this.orderList = list;
            }
          }
        });
      } else if (this.type === 3) {
        getReadHistoryList({
          pageSize: 10,
          pageNum: this.page,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.footerList = this.footerList.concat(list);
            } else {
              this.footerList = list;
            }
          }
        });
      } else {
        getProductCollectionList({
          pageSize: 10,
          pageNum: this.page,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.footerList = this.footerList.concat(list);
            } else {
              this.footerList = list;
            }
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.empty_order {
  width: 335px;
  height: 34px;
  margin-top: 12px;
  border-radius: 20px;
  background: #f9f6f3;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; /* 128.571% */
  letter-spacing: 0.56px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.collect_item {
  overflow: hidden;
  margin: 4px 0;
  background: #fff;
  // padding: 0px 9px 8px 8px;
  border-radius: 12px;
}
.collect_item:nth-child(1) {
  margin-top: 0px;
}
.collect_item_pic {
  width: 96px;
  height: 85px;
  overflow: hidden;
  border-radius: 6px;
  margin-left: 8px;
  margin-right: 9px;
  margin-top: 10px;
  // margin-bottom: 10px;
  float: left;
  .productPic {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}
.collect_item_pic > image {
  width: 100%;
  height: 100%;
}
.collect_item_right {
  width: 211px;
  height: 85px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 10px;
}
.collect_item_top {
  font-size: 12px;
  color: #000;
  font-weight: 600;
}
.collect_tit {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
}
.collect_tit_two {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  width: 211px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}
.collect_tit_name_text {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
.collect_price {
  font-weight: 700;
  font-size: 14px;
}
.list-price-new {
  color: #ff720c;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.list-price-old {
  color: #808080;
  text-decoration: line-through;
}
.collect_footer {
}
.collect_header {
  height: 31px;
  background: #fbf9f7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px 0px 22px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.48px;
  .status-box {
    width: 60px;
  }
}
.cancle_btn {
  height: 30px;
  width: 73px;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.24px;
  border-radius: 24px;
  cursor: pointer;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  display: flex;
  align-items: center;
  justify-content: center;
}
.yijiaContent {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 24px;
}
.postPrice_container {
  background-color: #fff;
}

.postPrice_iptWrap {
  border-bottom: 1px solid #f3f3f3;
  padding: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #666666;
}

.priceIcon {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
}

.priceIpt_post {
  text-align: right;
  width: 95px;
}

.postPrice_right {
  width: 210px;
  font-size: 14px;
  font-weight: 500;
  color: #656565;
}

.order_box {
  height: 448px;
  background: #fff;
  overflow: hidden;
  width: 375px;
  position: absolute;
  // right: 171px;
  left: 276px;
  box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.05);
  top: 64px;
  z-index: 110;
  border-radius: 24px;
  .scroll_box {
    height: 350px;
    overflow: auto;
    margin: 0px 12px 0px 20px;
    padding-bottom: 20px;
    margin-top: 8px;
  }
  .title_box {
    position: sticky;
    top: 0;
    z-index: 90;
  }
  .order_title {
    background: #fff;
    position: relative;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    z-index: 10;
    .close_btn {
      position: absolute;
      right: 20px;
      top: 16px;
      font-size: 20px;
      cursor: pointer;
    }
  }
  .tabs {
    background: #fff;
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    position: relative;
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.48px;
    z-index: 9px;
    .typelist_item {
      color: rgba(0, 0, 0, 0.4);
      position: relative;
      cursor: pointer;
      margin: 0px 14px;
    }

    .typelist_item.active {
      color: #1b1b1b;
    }
  }
}
.scroll_content_box {
  background: linear-gradient(
    180deg,
    #fdf5ed 1.76%,
    rgba(253, 245, 237, 0.5) 21.17%,
    #fdf5ed 98.83%
  );
  overflow: hidden;
}
.collect_btn_box {
  width: 316px;
  margin-left: 8px;
  padding-top: 9px;
  margin-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 8px;
  display: flex;
  justify-content: end;
}
</style>
