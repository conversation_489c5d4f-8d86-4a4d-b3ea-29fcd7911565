<template>
  <div class="session-box" @click="goSession">
    <div
      class="common-complex-avatar-wrapper common-complex-avatar-wrapper-nocursor"
    >
      <div>
        <span class="ant-badge ant-badge-status"
          ><span
            :class="clazz"
            class="ant-avatar ant-avatar-circle"
            style="
              width: 36px;
              height: 36px;
              line-height: 36px;
              font-size: 18px;
              vertical-align: middle;
            "
            ><span
              class="ant-avatar-string"
              style="
                line-height: 36px;
                transform: scale(0.777778) translateX(-50%);
              "
              >{{ formatNick(item.fromNick) }}</span
            ></span
          ></span
        >
      </div>
    </div>
    <div class="conversation-item-content">
      <div class="conversation-item-content-name">
        <div>
          <span class="nickname">{{ item.fromNick }}</span>
          <!-- <span class="gficon">官方</span> -->
        </div>
      </div>
      <div class="conversation-item-content-msg">
        <div class="conversation-item-content-read-status">
          <div class="common-percent-wrap"></div>
        </div>
        <div class="conversation-item-content-msg-body">
          {{ item.text }}
        </div>
      </div>
    </div>
    <div class="conversation-item-state">
      <div class="conversation-item-state-date">
        {{ util.formatTime(item.time, 'YYYY-MM-DD') }}
      </div>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
export default {
  props: {
    clazz: {
      type: String,
      default: '',
    },
    item: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      util,
    };
  },
  methods: {
    formatNick(name) {
      return name.slice(0, 2);
    },
    goSession() {
      const { store } = window.__xkit_store__;
      const { sessionId, to, scene } = this.item;
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive(scene, to);
      }
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-0 {
  background-color: rgb(248, 173, 80);
}
.bg-1 {
  background-color: rgb(229, 104, 145);
}
.bg-2 {
  background-color: rgb(122, 72, 217);
}
/deep/ .conversation-item-state-date {
  margin-right: 10px;
}
.session-box {
  width: 370px;
  display: flex;
  cursor: pointer;
}

/deep/ .conversation-item-content-name {
  color: #1b1b1b;
  font-size: 12px;
  font-weight: 500 !important;
  letter-spacing: 0.24px;
}

.search {
  width: 50%;
}

.add {
  margin-left: 20px;
}

.content {
  width: 100%;
  height: 578px;
  display: flex;
}

.left {
  width: 60px;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-icon {
  margin: 20px 0 25px 0;
  border-radius: 50%;
  border: 1px solid #e8e8e8;
  display: none;
}

.iconfont {
  font-size: 24px;
}

.chat-icon,
.contact-icon {
  margin: 0 0 25px 0;
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  height: 45px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.active {
  color: #2a6bf2;
}

.logout-icon {
  font-size: 22px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  margin-left: 100px;
  margin-right: 15px;
}

.icon-label {
  font-size: 12px;
  text-align: center;
}

.right {
  flex: 1;
  display: flex;
  // border-radius: 0px 0px 30px 30px;
}

.right-list {
  width: 243.388px;
  // border-right: 1px solid #e8e8e8;
}
/deep/ .gficon {
  background: #70dccf;
  color: #fff;
  border-radius: 2px;
  padding: 2px 4px;
  display: inline-block;
  margin-left: 10px;
  font-size: 11px;
  margin-top: 0;
  position: absolute;
  top: 12px;
}
/deep/ .nickname {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ .gfmsg {
  position: absolute;
  left: 100px;
  top: 0px;
  width: 34px;
  background: #70dccf;
  border-radius: 5px;
  padding: 1px;
  color: #fff;
  text-align: center;
  font-size: 12px;
}

/deep/ .chat-message-list-item-content {
  margin-top: 3px;
}

.right-content {
  flex: 1;
}
/deep/ .chat-message-list .chat-message-list-stranger-noti {
  display: none;
}
.icon-guanbi:hover {
  background: #e3e4e6;
  cursor: pointer;
}
.icon-guanbi {
  font-size: 30px;
}
</style>
