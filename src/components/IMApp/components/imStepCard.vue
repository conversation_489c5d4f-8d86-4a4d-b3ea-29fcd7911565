<template>
  <div ref="stepsContainer" class="stepCard">
    <div
      v-for="(item, index) in stepList"
      :key="index"
      class="content_item spaceBetween"
    >
      <div :class="item.current === 1 ? 'active' : ''" class="cricle"></div>
      <div class="item_txt">
        <div>{{ item.stepName }}</div>
        <div
          v-for="(value, key, index) in item.subSteps"
          :key="index"
          class="spaceBetween sub_item"
        >
          <div>{{ value.name }}</div>
          <div>{{ value.startTime }}</div>
        </div>
      </div>
      <div style="display: none">{{ lightCricle }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    flow: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    lightCricle() {
      const findIndex = this.stepList.findIndex((ele) => ele.current == 1);
      setTimeout(() => {
        // 假设你的步骤元素有类名 'step' 并且它们都是 stepsContainer 的子元素
        const stepsContainer = this.$refs.stepsContainer;
        if (
          stepsContainer &&
          findIndex >= 0 &&
          findIndex < stepsContainer.children.length
        ) {
          stepsContainer.children[findIndex].scrollIntoView({
            behavior: 'smooth',
          });
        }
      }, 200);
      return findIndex;
    },
    stepList() {
      let tempList = [];
      if (this.flow.mainSteps) {
        Object.keys(this.flow.mainSteps).forEach((key) => {
          // let index = parseInt(key) - 1;
          let item = this.flow.mainSteps[key];
          // list[index] = item;
          tempList.push(item);
        });
        tempList.sort((a, b) => {
          return a.step - b.step;
        });
      }
      // tempList.reverse();
      return tempList;
    },
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
.stepCard {
  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道的背景色 */
    width: 0; /* 隐藏滚动条轨道 */
    height: 0; /* 隐藏滚动条轨道 */
  }
  &::-webkit-scrollbar {
    width: 5px; /* 设置滚动条的宽度 */
    height: 5px; /* 设置滚动条的高度 */
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
  }
  padding: 15px;
  font-size: 12px;
  height: 322px;
  overflow-y: auto;
  .content_item {
    // line-height: 28px;
    align-items: flex-end;
    border-left: 1px dashed #969696;
    padding: 0 0 19.711px 16.283px;
    position: relative;
    .item_txt {
      width: 100%;
      color: #1b1b1b;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px; /* 133.333% */
      letter-spacing: 0.24px;
    }
    .item_txt div:nth-child(1) {
      margin-bottom: 4px;
    }
    .sub_item {
      color: #969696;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px; /* 160% */
      letter-spacing: 0.2px;
    }
    .cricle {
      background: #969696;
      border: 1px solid #bdbdbd;
      border-radius: 50%;
      height: 16px;
      width: 16px;
      position: absolute;
      left: -8.5px;
      top: 0px;
    }
    .active {
      // background: red;
      height: 16px;
      width: 16px;
      background: url(../../../../static/imgs/imStepCard_cricle_active.svg);
      border: none;
    }
    .red_txt {
      // color: #fe5a1e;
    }
  }
}
</style>
