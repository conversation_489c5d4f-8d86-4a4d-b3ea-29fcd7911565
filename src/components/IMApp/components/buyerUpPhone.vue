<template>
  <div class="accountForm el-reset-clazz">
    <div class="tit">买家提供手机号：</div>
    <div class="note">请买家提供手机号进行换绑</div>
    <div class="form_box">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="手机号" prop="mobile">
          <el-input
            type="tel"
            maxlength="11"
            v-model="form.mobile"
            placeholder="请输入账号"
          ></el-input>
        </el-form-item>
        <el-form-item class="spaceEnd">
          <div class="elbtn cancel" @click="onCancel">取消</div>
          <div class="elbtn" @click="onSubmit('form')">
            <i class="el-icon-loading" v-show="loading"></i>
            提交
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
import request from '@/utils/request';
export default {
  props: {
    preFillMobile: {
      type: [String, Number],
      default: '',
    },
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      form: {
        mobile: this.preFillMobile,
      },
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            type: 'pattern', // 或者根据您的验证库可能是 'regex' 或其他类似的属性
            pattern: /^1\d{10}$/, // 正则表达式，匹配以1开头，后面跟10位数字的字符串
            message: '手机号格式不正确，请输入11位手机号',
            trigger: 'blur', // 触发验证的时机，这里设置为失去焦点时
          },
        ],
      },
    };
  },
  methods: {
    onCancel() {
      this.$emit('close');
    },
    onSubmit(formName) {
      if (this.loading) {
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = util.getUrlParams(this.url);
          this.url = this.url.split('?')[0];
          let data = this.form;
          data = Object.assign({}, data, obj);
          this.loading = true;
          request({
            url: this.url,
            method: 'post',
            data,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.onCancel();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  cursor: pointer;
  margin-left: 10px;
  color: #409eff;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.cancel {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
}
.cancel:hover {
  background: #fff;
  border-color: #409eff;
  color: #409eff;
}
.accountForm {
  .tit {
    font-size: 16px;
  }
  .note {
    margin: 20px 0;
    color: #ff6716;
  }
  .el-form-item {
    margin-bottom: 30px;
  }
  .form_box {
    flex-wrap: wrap;
    width: 400px;
  }
  background: #fff;
  position: absolute;
  border: 1px solid #ccc;
  top: 50px;
  right: 50px;
  padding: 10px;
}
</style>
