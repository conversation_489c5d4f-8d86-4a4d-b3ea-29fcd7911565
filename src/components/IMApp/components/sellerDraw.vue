<template>
  <div class="spzx_box el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="400px"
      :modal="false"
      center
      title="填写收款信息"
    >
      <div class="note">
        请输入与看看账号实名一致的收款帐户，否则可能会导致交易后无法收到款项.您在看看账号的实名是：<span
          class="note_red"
          >{{ actionData.value }}</span
        >
      </div>
      <el-form class="form_box">
        <el-form ref="form" :model="form">
          <el-form-item label="收款方式">
            <el-radio-group v-model="form.transType">
              <el-radio v-if="form.transType == 2" label="2">支付宝</el-radio>
              <el-radio v-if="form.transType == 0" label="0">银行卡</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '请输入收款人姓名',
              trigger: 'blur',
            }"
            label="收款人姓名"
            prop="accountName"
          >
            <el-input v-model="form.accountName" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '请输入',
              trigger: 'blur',
            }"
            :label="form.transType == '0' ? '银行卡号' : '支付宝账号'"
            prop="accountNumber"
          >
            <el-input v-model="form.accountNumber" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '请输入',
              trigger: 'blur',
            }"
            :label="form.transType == '0' ? '银行卡号' : '支付宝账号'"
            prop="accountNumber2"
          >
            <el-input v-model="form.accountNumber2" placeholder="请输入">
            </el-input>
          </el-form-item>
        </el-form>
      </el-form>
      <div v-if="errorTxt" class="error">{{ errorTxt }}</div>
      <div class="spaceEnd">
        <div class="elbtn cancel" @click="onCancel">取消</div>
        <div class="elbtn" @click="onSubmit('form')">
          <i class="el-icon-loading" v-show="loading"></i>提交
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import util from '@/utils/index';
import request from '@/utils/request';

const defaultForm = {
  transType: '',
  accountType: '1',
  accountName: '',
  accountNumber: '',
  accountNumber2: '',
};
export default {
  props: {
    actionObj: {
      type: Object,
      default() {
        return {};
      },
    },
    actionData: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      errorTxt: '',
      form: Object.assign({}, defaultForm),
      rules: {},
    };
  },
  mounted() {
    this.form.transType = this.actionData.transType;
  },
  methods: {
    onCancel() {
      this.form = Object.assign({}, defaultForm);
      this.$emit('close');
    },
    onSubmit(formName) {
      if (this.form.accountNumber != this.form.accountNumber2) {
        this.errorTxt = '两次账号输入不一致';
        return;
      }

      if (this.form.accountName !== this.actionData.value) {
        this.errorTxt = '实名信息不一致';
        return;
      }

      if (this.loading) {
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = util.getUrlParams(this.actionObj.url);
          let url = this.actionObj.url.split('?')[0];
          let data = this.actionObj.fedtempParams || {};
          data = Object.assign(data, obj, this.form);
          this.loading = true;
          request({
            url,
            method: 'post',
            data,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.onCancel();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.error {
  color: #f56c6c;
  font-size: 12px;
}
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  cursor: pointer;
  margin-left: 10px;
  color: #409eff;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.cancel {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
}
.cancel:hover {
  background: #fff;
  border-color: #409eff;
  color: #409eff;
}
.spzx_box {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  /deep/ .el-dialog__body {
    padding-top: 5px;
  }
  /deep/ .el-radio__input.is-checked + .el-radio__label {
    color: #3995fb;
  }
  /deep/ .el-radio__input.is-checked .el-radio__inner {
    border-color: #3995fb;
    background: #3995fb;
  }
  /deep/ .el-form-item__label {
    text-align: left;
    font-size: 12px;
    float: none;
  }
  /deep/ .el-dialog__header {
    border-bottom: 1px solid #ccc;
  }
  .form_box {
    margin: 10px 0;
    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/ .el-dialog__headerbtn:hover .el-dialog__close {
    color: #ccc;
  }
  .note {
    .note_red {
      font-weight: 700;
      color: #f56c6c;
    }
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #da8300;
    line-height: 24px;
    border-radius: 2px;
    padding: 0 10px;
    font-size: 12px;
  }
  .el-dialog__wrapper {
    position: relative;
    top: -60px;
    overflow: auto;
    left: 42px;
    padding-bottom: 10px;
  }
}
</style>
