<template>
  <div class="uploadImg">
    <div class="tit">上传图片：</div>
    <div class="note">请根据群内指引上传图片</div>
    <div class="spaceBetween img_box">
      <!-- <uploadList
        :url-pic="imgurl"
        :need-water="false"
        name-key="img"
        @upSuccsessList="picUpLoadSuc"
        @deletPicList="deletPic"
      /> -->
      <uploadSingle
        :url-pic="imgurl"
        name-key="img"
        @upSuccsessSingle="picUpLoadSuc"
      />
    </div>
    <div class="spaceEnd">
      <div class="elbtn cancel" @click="hideUploadImg">取消</div>
      <div class="elbtn" @click="doConfirm('form')">
        <i class="el-icon-loading" v-show="loading"></i>提交
      </div>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
// import uploadList from '@/components/uploadList/index';
import uploadSingle from '@/components/uploadSingle/index';
import request from '@/utils/request';
export default {
  components: { uploadSingle },
  props: {
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      imgurl: '',
    };
  },
  methods: {
    picUpLoadSuc(url, key) {
      this.imgurl = url;
    },
    hideUploadImg() {
      this.$emit('hideUploadImg');
    },
    doConfirm() {
      if (!this.imgurl) {
        this.$message.error('请选择图片上传');
        return;
      }
      if (this.url) {
        if (this.loading) {
          return;
        }
        let obj = util.getUrlParams(this.url);
        this.url = this.url.split('?')[0];
        const data = Object.assign(
          {},
          {
            images: [this.imgurl],
          },
          obj,
        );
        this.loading = true;
        request({
          url: this.url,
          method: 'post',
          data,
        })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message);
              this.hideUploadImg();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  margin-left: 10px;
  cursor: pointer;
  color: #409eff;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.cancel {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
}
.cancel:hover {
  background: #fff;
  border-color: #409eff;
  color: #409eff;
}
.uploadImg {
  .tit {
    font-size: 16px;
  }
  .note {
    margin: 20px 0;
    color: #ff6716;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  .img_box {
    flex-wrap: wrap;
    width: 400px;
  }
  background: #fff;
  position: absolute;
  border: 1px solid #ccc;
  top: 50px;
  right: 50px;
  padding: 20px;
  .note {
    margin: 20px 0;
  }
}
</style>
