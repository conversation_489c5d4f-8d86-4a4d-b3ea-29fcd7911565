<template>
  <div class="kfBox">
    <div class="order_title">
      选择客服
      <i class="el-icon-close close_btn" @click="hide"></i>
    </div>
    <div class="kfListTabs">
      <!-- <el-tabs v-model="activeName" :stretch="true" @tab-click="handleClick">
        <el-tab-pane label="售前客服" name="1"></el-tab-pane>
        <el-tab-pane label="售后客服" name="2"></el-tab-pane>
      </el-tabs> -->
      <div
        :class="activeName == 1 ? 'active' : ''"
        class="tab_btn"
        @click="handleClick(1)"
      >
        售前客服
      </div>
      <div class="divider"></div>
      <div
        :class="activeName == 2 ? 'active' : ''"
        class="tab_btn"
        @click="handleClick(2)"
      >
        售后客服
      </div>
    </div>
    <div class="kfListContentBox_top">
      <div class="kfListContentBox">
        <div style="height: 12px"></div>
        <div v-for="item in list" class="spaceBetween item">
          <div>
            <img :src="item.pic" class="pic" />
          </div>
          <div class="kfListContentBox_name">
            {{ item.name }}
          </div>
          <div>
            <!-- <el-link type="primary" @click="goChat(item)">立即咨询</el-link> -->
            <div class="kfListContentBox_Btn" @click="goChat(item)">
              <span>立即咨询</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { changeKfList, m2kfTalk } from '@/api/kf';
export default {
  data() {
    return {
      activeName: '1',
      list: [],
    };
  },
  mounted() {
    this.getKf();
  },
  methods: {
    getKf() {
      const { nim, store } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      let scene = 'team';
      if (sessionId.indexOf('team-') === 0) {
        sessionId = sessionId.replace('team-', '');
        sessionId = parseInt(sessionId);
      } else {
        scene = 'p2p';
        sessionId = sessionId.replace('p2p-', '');
      }
      let obj = {
        currentKferIM: sessionId,
        type: this.activeName == 1 ? '咨询客服' : '售后客服',
      };
      changeKfList(obj).then((res) => {
        if (res.code == 200) {
          this.list = res.data;
        }
      });
    },
    handleClick(v) {
      this.activeName = v;
      this.getKf();
    },
    hide() {
      this.$emit('hide');
    },
    getNickName() {
      const { nim, store } = window.__xkit_store__;
      let accounts = [];
      store.sessionStore.sessions.forEach((ele) => {
        if (ele.id.indexOf('p2p') == 0) {
          accounts.push(ele.id.replace('p2p-', ''));
        }
      });
      nim.nim.getUsers({
        accounts,
        sync: true,
        done: (err, result) => {
          this.$store.dispatch('SetNickList', result);
          const sessionNames = document.querySelectorAll(
            '.conversation-item-content-name-forjs'
          );
          sessionNames.forEach((ele) => {
            let account = ele.innerText || '';
            let findIt = result.find((item) => item.account == account);
            const name = findIt && findIt.nick;
            if (name && name !== account) {
              ele.innerText = name;
            }
          });
        },
      });
    },
    goChat(item) {
      const { nim, store } = window.__xkit_store__;
      const imcode = item.IM;
      const sessionId = `p2p-${imcode}`;
      // m2kfTalk({
      //   kfIM: imcode,
      // });
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imcode);
        this.getNickName();
      }
      // this.$store.dispatch('ToggleIM', true);
      this.hide();
    },
  },
};
</script>

<style lang="scss" scoped>
.kfBox {
  height: 448px;
  background: #fff;
  overflow: hidden;
  width: 375px;
  position: absolute;
  // right: 171px;
  left: 276px;
  box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.05);
  top: 64px;
  // border: 1px solid #ccc;
  z-index: 110;
  border-radius: 24px;
  .item {
    border-radius: 12px;
    background: #fff;
    box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.05);
    padding: 10px 16px;
    margin-bottom: 4px;
  }
  .pic {
    width: 60px;
    height: 60px;
  }
  .order_title {
    background: #fff;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    text-align: center;
    height: 54px;
    line-height: 54px;
    color: #000;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    .close_btn {
      position: absolute;
      right: 20px;
      top: 16px;
      font-size: 24px;
      cursor: pointer;
    }
  }
}
.kfListTabs {
  position: relative;
  height: 54px;
  // background: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.4);
  font-family: YouSheBiaoTiHei;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 133.333% */
  letter-spacing: 0.36px;
  .tab_btn {
    cursor: pointer;
  }
  .active {
    background: var(--btn-background-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .divider {
    height: 7.5px;
    width: 0.5px;
    background: rgba(0, 0, 0, 0.4);
    margin: 0px 57px 0px 61px;
  }
}
.kfListContentBox_top {
  background: linear-gradient(
    180deg,
    #fdf5ed 1.76%,
    rgba(253, 245, 237, 0.5) 21.17%,
    #fdf5ed 98.83%
  );
}
.kfListContentBox {
  height: 341px;
  // padding: 0px 20px;
  margin: 0px 12px 0px 20px;

  overflow: auto;
  &::-webkit-scrollbar-track {
    background: #f4f4f4; /* 滚动条轨道的背景色 */
    // width: 0; /* 隐藏滚动条轨道 */
    // height: 0; /* 隐藏滚动条轨道 */
  }
  &::-webkit-scrollbar {
    width: 8px; /* 设置滚动条的宽度 */
    height: 3px; /* 设置滚动条的高度 */
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
  }
  .kfListContentBox_name {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 133.333% */
    letter-spacing: 0.24px;
  }
  .kfListContentBox_Btn {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 133.333% */
    letter-spacing: 0.24px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    padding: 0px 15.5px;
    background: var(--btn-background-gradient);
    border-radius: 24px;
    position: relative;
    cursor: pointer;
    &::before {
      content: '';
      position: absolute;
      left: 0px;
      right: 0px;
      bottom: 0px;
      top: 0px;
      margin: 1px;
      background: #fff;
      border-radius: 24px;
    }
    span {
      position: relative;
      z-index: 3;
      background: var(--btn-background-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
}
</style>
