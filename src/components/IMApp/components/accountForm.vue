<template>
  <div class="accountForm el-reset-clazz">
    <div class="tit">卖家提供交易账号：</div>
    <div class="note">请提供账号与密码供买家验号</div>
    <div class="form_box">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="idLabel" prop="uid">
          <el-input
            v-model="form.uid"
            placeholder="请输入id,没有输入1"
          ></el-input>
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input v-model="form.username" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            placeholder="请输入密码，如是手机账号请输入1"
          ></el-input>
        </el-form-item>
        <el-form-item class="spaceEnd">
          <div class="elbtn cancel" @click="onCancel">取消</div>
          <div class="elbtn" @click="onSubmit('form')">
            <i class="el-icon-loading" v-show="loading"></i>提交
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
import request from '@/utils/request';
export default {
  props: {
    iswz: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '',
    },
  },
  computed: {
    idLabel() {
      return this.iswz ? '营地id' : 'UID';
    },
  },
  data() {
    const validateEn = (rule, value, callback) => {
      const regex = /^[\x00-\x7F]*$/;
      if (!regex.test(value)) {
        callback(new Error('请输入正确的格式'));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      form: {
        uid: '',
        username: '',
        password: '',
      },
      rules: {
        uid: [
          { required: true, message: '请输入uid', trigger: 'blur' },
          { validator: validateEn, trigger: 'blur' },
        ],
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          { validator: validateEn, trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validateEn, trigger: 'blur' },
        ],
      },
    };
  },
  methods: {
    onCancel() {
      this.$emit('hideAccountForm');
    },
    onSubmit(formName) {
      if (this.loading) {
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = util.getUrlParams(this.url);
          this.url = this.url.split('?')[0];
          let data = this.form;
          data = Object.assign({}, data, obj);
          this.loading = true;
          request({
            url: this.url,
            method: 'post',
            data,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.onCancel();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  cursor: pointer;
  margin-left: 10px;
  color: #409eff;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.cancel {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
}
.cancel:hover {
  background: #fff;
  border-color: #409eff;
  color: #409eff;
}
.accountForm {
  .tit {
    font-size: 16px;
  }
  .note {
    margin: 20px 0;
    color: #ff6716;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
  .form_box {
    flex-wrap: wrap;
    width: 400px;
  }
  background: #fff;
  position: absolute;
  border: 1px solid #ccc;
  top: 50px;
  right: 50px;
  padding: 20px;
  .note {
    margin: 20px 0;
  }
}
</style>
