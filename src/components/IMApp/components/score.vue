<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="400px"
      :modal="false"
      top
      title="请对本次交易进行评价"
    >
      <div class="scorebox">
        <div class="score spaceStart">
          <div>交易流程：</div>
          <el-rate v-model="postForm.flowScore"></el-rate>
        </div>
        <div class="score spaceStart">
          <div>交易客服：</div>
          <el-rate v-model="postForm.kfScore"></el-rate>
        </div>
        <div>
          <div class="desc">
            <el-input
              v-model="postForm.suggest"
              :rows="2"
              type="textarea"
              maxlength="100"
              placeholder="请输入建议,最多100字"
            ></el-input>
          </div>
        </div>
        <div class="spaceAround btnbox">
          <div class="elbtn" @click="submitForm">
            <i class="el-icon-loading" v-show="loading"></i>提交
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import util from '@/utils/index';
import request from '@/utils/request';
const TXTMAP = {
  '1': '极差',
  '2': '失望',
  '3': '一般',
  '4': '满意',
  '5': '惊喜',
};
export default {
  props: {
    actionObj: {
      type: Object,
      default() {
        return {};
      },
    },
    actionData: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      errorTxt: '',
      errorTxt2: '',
      postForm: {
        kfScore: null,
        flowScore: null,
        // flowScoreDesc: null,
        suggest: '',
      },
    };
  },
  methods: {
    onCancel() {
      this.postForm = {
        flowScore: null,
        // flowScoreDesc: null,
        suggest: '',
      };
      this.$emit('close');
    },
    submitForm() {
      if (!this.postForm.flowScore) {
        this.errorTxt = '请先进行评分';
        return;
      }
      // if (this.postForm.flowScore) {
      //   this.postForm.flowScoreDesc = TXTMAP[this.postForm.flowScore];
      // }
      if (this.loading) {
        return;
      }
      this.errorTxt = '';
      let obj = util.getUrlParams(this.actionObj.url);
      let url = this.actionObj.url.split('?')[0];
      let data = this.actionObj.fedtempParams || {};
      data = Object.assign(data, obj, this.postForm);
      this.loading = true;
      request({
        url,
        method: 'post',
        data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message);
            this.onCancel();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  .el-dialog__title {
    font-size: 16px;
  }
}
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  cursor: pointer;
  color: #409eff;
  background: #ecf5ff;
  width: 100%;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.scorebox {
  .desc {
    margin-top: 20px;
    /deep/ .el-textarea__inner {
      border-color: #409eff;
    }
  }
  .btnbox {
    margin-top: 40px;
  }
  .score {
    margin: 0 auto 10px;
    width: 240px;
  }
  .tip {
    text-align: center;
    font-size: 12px;
    color: #000;
    margin: 20px 0;
  }
  .btns {
    width: 260px;
    margin: 0 auto;
    .btn {
      cursor: pointer;
      width: 115px;
      height: 35px;
      line-height: 35px;
      border: 1px solid #999;
      color: #999;
      border-radius: 20px;
      text-align: center;
    }
    .btn.active {
      color: #409eff;
      border-color: #409eff;
    }
  }
}
</style>
