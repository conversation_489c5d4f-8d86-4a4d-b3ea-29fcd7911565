<template>
  <div class="spzx_box el-reset-clazz">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :close-on-click-modal="false"
      width="400px"
      :modal="false"
      center
      title="买家咨询信息提交"
    >
      <div class="note">老板请如实填写，避免影响交易</div>
      <el-form class="form_box">
        <el-form ref="form" :model="form">
          <el-form-item
            :rules="{
              required: true,
              message: '请输入值再提交',
              trigger: 'blur',
            }"
            :label="attr.name"
            prop="attr_value"
          >
            <el-input
              v-model="form.attr_value"
              :placeholder="attr.name"
              v-if="attr.selectType == 0"
              type="tel"
            >
            </el-input>
            <el-radio-group
              :placeholder="attr.name"
              v-model="form.attr_value"
              v-else-if="attr.selectType == 1 && attr.inputList.length === 2"
            >
              <el-radio
                :key="item"
                v-for="item in attr.inputList"
                :label="item"
                >{{ item }}</el-radio
              >
            </el-radio-group>
            <el-select
              v-model="form.attr_value"
              :placeholder="attr.name"
              v-else-if="attr.selectType == 1 && attr.inputList.length > 2"
            >
              <el-option
                v-for="item in attr.inputList"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
            <el-date-picker
              v-else-if="attr.inputType == 2 && attr.selectType == 4"
              v-model="form.attr_value"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入该答复有效期',
                trigger: 'blur',
              },
              { validator: validatePositiveInteger, trigger: 'blur' },
            ]"
            prop="effDays"
            label="该答复有效期"
          >
            <!-- <el-select v-model="form.effDays" placeholder="该答复有效期">
              <el-option
                v-for="(item, index) in effDaysList"
                :label="item"
                :value="item"
                :key="index"
                >{{ item }}</el-option
              >
            </el-select> -->
            <div class="spaceStart">
              <el-input
                v-model="form.effDays"
                placeholder="答复有效期"
                type="tel"
                style="width: 50%"
              >
              </el-input>
              <span style="margin-left: 10px">天</span>
            </div>
          </el-form-item>
        </el-form>
      </el-form>
      <div class="spaceEnd">
        <div class="elbtn cancel" @click="onCancel">取消</div>
        <div class="elbtn" @click="onSubmit('form')">
          <i class="el-icon-loading" v-show="loading"></i>提交
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import util from '@/utils/index';
import request from '@/utils/request';

export default {
  props: {
    actionObj: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      effDaysList: [1, 7, 30],
      form: {
        effDays: 7,
        attr_value: '',
      },
      attr: {},
      rules: {},
    };
  },
  mounted() {
    let attr_quetionPmsProductAttribute =
      this.actionObj.fedtempParams.attr_quetionPmsProductAttribute;
    this.attr = JSON.parse(attr_quetionPmsProductAttribute);
    if (this.attr.selectType != 0) {
      this.attr.inputList = this.attr.inputList.split(',');
    }
  },
  methods: {
    validatePositiveInteger(rules, value, callback) {
      if (parseInt(value) > 360) {
        callback(new Error('最大360天'));
      } else {
        callback();
      }
    },
    onCancel() {
      this.form = {
        effDays: 7,
        attr_value: '',
      };
      this.$emit('close');
    },
    onSubmit(formName) {
      if (this.loading) {
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = util.getUrlParams(this.actionObj.url);
          let url = this.actionObj.url.split('?')[0];
          let data = this.actionObj.fedtempParams || {};
          data = Object.assign(data, obj, this.form);
          this.loading = true;
          request({
            url,
            method: 'post',
            data,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.onCancel();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.elbtn {
  display: inline-block;
  text-align: center;
  padding: 12px 20px;
  line-height: 14px;
  cursor: pointer;
  margin-left: 10px;
  color: #409eff;
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}
.elbtn:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.cancel {
  color: #606266;
  background: #fff;
  border: 1px solid #dcdfe6;
}
.cancel:hover {
  background: #fff;
  border-color: #409eff;
  color: #409eff;
}
.spzx_box {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  right: 0;
  /deep/ .el-dialog__body {
    padding-top: 5px;
  }
  /deep/ .el-radio__input.is-checked + .el-radio__label {
    color: #3995fb;
  }
  /deep/ .el-radio__input.is-checked .el-radio__inner {
    border-color: #3995fb;
    background: #3995fb;
  }
  /deep/ .el-form-item__label {
    text-align: left;
    font-size: 12px;
    float: none;
  }
  /deep/ .el-dialog__header {
    border-bottom: 1px solid #ccc;
  }
  .form_box {
    margin: 10px 0;
    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/ .el-dialog__headerbtn:hover .el-dialog__close {
    color: #ccc;
  }
  .note {
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #da8300;
    line-height: 30px;
    border-radius: 2px;
    padding: 0 10px;
    font-size: 12px;
  }
  .el-dialog__wrapper {
    position: relative;
    top: 0;
    overflow: auto;
    left: 42px;
    padding-bottom: 10px;
  }
}
</style>
