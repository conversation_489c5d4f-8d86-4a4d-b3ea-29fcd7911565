<template>
  <div style="padding-right: 6.856px">
    <div class="orderCard">
      <div class="curFlowName">{{ curFlowName }}</div>
      <div class="spaceStart order">
        <img :src="productDetail.productPic" class="order_img" />
        <div class="order_content">
          <div class="content_item">
            订单编号：<span class="red_txt">{{ orderDetail.orderSn }}</span>
          </div>
          <div class="content_item">
            商品编号：<span class="black_txt">
              {{ productDetail.productSn }}</span
            >
          </div>
          <div class="content_item">
            创建时间：<span class="black_txt">
              {{
                util.formatTime(orderDetail.createTime, 'YYYY-MM-DD HH:mm:ss')
              }}</span
            >
          </div>
          <!-- <div class="content_item">
          账号区服：<span class="red_txt">{{ productDetail }}</span>
        </div> -->
          <div class="content_item">
            游戏名称：<span class="black_txt">{{
              orderDetail.productCategoryName
            }}</span>
          </div>
          <div class="content_item">
            订单类型：<span class="red_txt">{{ getOrderType() }}</span>
          </div>
          <div v-if="baopeiList.length === 0" class="content_item">
            <div>包赔类型：<span class="black_txt">无</span></div>
          </div>
          <div v-else>
            <div
              v-for="(item, index) in baopeiList"
              :key="index"
              class="content_item"
            >
              <div>
                {{ item.productAttrCopy && item.productAttrCopy[0].key }}：<span
                  class="black_txt"
                  >{{ item.productName }}</span
                >
              </div>
            </div>
          </div>
          <div class="content_item">
            成交金额：<span class="black_txt"
              >￥{{ productDetail.productPrice }}</span
            >
          </div>
          <div class="content_item">
            订单状态：<span class="black_txt">{{
              orderDetail.orderStatusName
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
const ORDERTYPEMAP = {
  '0': '普通订单',
  '1': '包赔订单',
  '2': '包赔订单',
  '3': '议价订单',
  '4': '定金订单',
  '5': '补款订单',
  '6': '回收订单',
  '7': '担保订单',
  '8': '商品改价订单',
};
export default {
  props: {
    curFlowName: {
      type: String,
      default: '',
    },
    orderDetail: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      util,
    };
  },

  computed: {
    productDetail() {
      if (this.orderDetail && this.orderDetail.orderItemList) {
        const findIt = this.orderDetail.orderItemList.find((ele) => {
          return ele.itemType === 0;
        });
        return findIt || {};
      }
      return {};
    },
    baopeiList() {
      if (this.orderDetail && this.orderDetail.orderItemList) {
        const list = this.orderDetail.orderItemList.filter((ele) => {
          let { productAttr, itemType } = ele;
          if (itemType == 1) {
            try {
              productAttr = JSON.parse(productAttr);
              ele.productAttrCopy = productAttr;
            } catch (e) {
              console.log(e);
            }
          }
          return ele.itemType === 1;
        });
        return list;
      }
      return [];
    },
  },
  methods: {
    getOrderType() {
      return this.orderDetail.orderTypeName;
    },
  },
};
</script>
<style lang="scss" scoped>
.orderCard {
  max-height: 350px;

  border-bottom: 1px solid #ececec;
  padding-bottom: 10.283px;
  margin-bottom: 10.283px;
  font-size: 12px;
  .curFlowName {
    color: #ff720c;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 128.571% */
    letter-spacing: 0.56px;
    padding-bottom: 10.283px;
    border-bottom: 1px solid #ececec;
    margin-bottom: 10.283px;
  }
  .orderHistory {
    margin-right: 10px;
  }
  .order {
    align-items: flex-start;
    .order_img {
      width: 42.85px;
      height: 42.85px;
      object-fit: cover;
      margin-right: 10.283px;
      border-radius: 6px;
    }
    .order_content {
      color: #666;
      // line-height: 22px;
      .content_item {
        // margin-bottom: 10px;
        color: #969696;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: 400;
        letter-spacing: 0.24px;
        line-height: 22px;
        .red_txt {
          color: #ff720c;
        }
        .black_txt {
          color: #1b1b1b;
        }
      }
    }
  }
}
</style>
