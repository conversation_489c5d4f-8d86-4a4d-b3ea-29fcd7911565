<template>
  <div class="spaceBetween productCard">
    <img :src="productDetail.pic" class="productPic" />
    <div class="detailbox">
      <div
        class="spaceBetween"
        style="width: 100%; height: 36px; align-items: flex-start"
      >
        <div class="text_linTwo cardContentText">
          {{ productDetail.name }}
        </div>
        <i class="el-icon-close close_btn" @click="doClose"></i>
      </div>
      <div class="spaceBetween" style="width: 100%; align-items: flex-end">
        <div class="price">￥{{ productDetail.price }}</div>
        <div class="linkbox spaceCenter" @click="sendLink">发送链接</div>
      </div>
    </div>
  </div>
</template>

<script>
import { m2kfSendProduct } from '@/api/kf';
import { getDetail } from '@/api/playDetail';
export default {
  data() {
    return {
      productDetail: {},
      productAttributeList: [],
    };
  },
  mounted() {
    let productId = this.$store.getters.showProductCardId;
    if (productId) {
      this.getProduct(productId);
    }
  },
  methods: {
    getProduct(productId) {
      getDetail(productId).then((res) => {
        if (res.code === 200) {
          this.productAttributeList = res.data.productAttributeList;
          this.productDetail = res.data.product;
        }
      });
    },
    doClose() {
      this.$emit('doClose');
    },
    sendLink() {
      const { store, nim } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = store.userStore.myUserInfo.account;
      const productDetail = this.productDetail;
      const { productSn, productCategoryId, publishStatus, stock } =
        productDetail;
      let type4List = [];
      this.productAttributeList.forEach((ele) => {
        if (ele.type == 4) {
          type4List.push(ele.name);
        }
      });
      if (stock == 0 || publishStatus == -2) {
        type4List = [];
      }
      const content = `<div class="spaceBetween msg-flexstart">
      <img src="${productDetail.pic}" class="msg-productImg" />
      <div>
        <div class="twoLine">${productDetail.subTitle.substring(0,30)}</div>
        <div class="msg-red">￥${productDetail.price || ''}</div>
      </div>
    </div>`;
      const attach = {
        data: {
          type: 'product',
          productSn,
          productId: productDetail.id,
          productCategoryId,
        },
        body: {
          title: productDetail.subTitle.substring(0,30),
          content,
          type4List,
        },
        type: 'kk_product_msg_fed',
      };
      m2kfSendProduct({
        productId: productDetail.id,
        kfIM: to,
      });
      store.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach),
        })
        .then((res) => {
          this.$emit('doClose');
          // 让消息滚动到可视区域
          document.getElementById(`${res.idClient}`).scrollIntoView();
        })
        .catch((err) => {
          this.$emit('doClose');
          console.log('发送失败', err);
        });
    },
  },
};
</script>

<style scoped>
.close_btn {
  font-size: 20px;
  cursor: pointer;
}
.productCard {
  position: fixed;
  bottom: 117px;
  right: 152px;
  z-index: 999;
  height: 77.13px;
  background: #fff;
  /* -webkit-box-shadow: 0 0 6px #ccc;
  box-shadow: 0 0 6px #ccc; */
  border-radius: 17.14px;
  background: #fff;
  box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
  width: 394.219px;
  padding: 8.57px 10.283px 10.283px 9.427px;
  border: 1px solid #e5e5e5;
}
.cardContentText {
  width: 290px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 15.426px;
  margin-top: 1.714px;
  letter-spacing: 0.56px;
}
.detailbox {
  overflow: hidden;
  /* margin: 0px 10px; */
  margin-left: 12px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 2px;
}
.price {
  color: #ff720c;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.linkbox {
  background: var(--btn-background-gradient);
  width: 86.577px;
  height: 25.71px;
  border-radius: 24px;
  color: #fff;
  cursor: pointer;
  font-family: 'PingFang SC';
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  font-size: 12px;
  font-weight: 500;
}
.productPic {
  min-width: 85.7px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  /* margin-left: 11px; */
}
</style>
