<template>
  <div>
    <!-- <div>
      订单编号：
      <div>{{ orderDetail.orderSn }}</div>
    </div> -->
    <div class="spaceBetween productCard">
      <img :src="productDetail.productPic" class="productPic" />
      <div class="detailbox">
        <div
          class="spaceBetween"
          style="width: 100%; height: 36px; align-items: flex-start"
        >
          <div style="width: 90%" class="text_linTwo cardContentText">
            {{ productDetail.productName }}
          </div>
          <i class="el-icon-close close_btn" @click="doClose"></i>
        </div>
        <div class="spaceBetween" style="width: 100%; align-items: flex-end">
          <div class="price">￥{{ productDetail.productPrice }}</div>
          <div class="linkbox spaceCenter" @click="sendLink">发送链接</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import util from '@/utils/index';
import { m2kfSendOrder } from '@/api/kf';
import { getOrderDetail } from '@/api/confirmOrder.js';
export default {
  data() {
    return {
      orderDetail: {},
      productDetail: {},
    };
  },
  mounted() {
    let orderId = this.$store.getters.showOrderCardId;
    if (orderId) {
      this.getOrder(orderId);
    }
  },
  methods: {
    getOrder(orderId) {
      if (orderId) {
        getOrderDetail(orderId).then((res) => {
          if (res.code === 200) {
            this.orderDetail = res.data;
            this.productDetail = this.orderDetail.orderItemList.find(
              (ele) => ele.itemType === 0
            );
          }
        });
      }
    },
    doClose() {
      this.$emit('doClose');
    },
    sendLink() {
      const orderDetail = this.orderDetail;
      const { store, nim } = window.__xkit_store__;
      let sessionId = store.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = store.userStore.myUserInfo.account;
      const orderId = orderDetail.id;
      const orderSn = orderDetail.orderSn;

      const { orderItemList } = orderDetail;
      const findProduct = this.productDetail;
      const productSn = findProduct.productSn;
      const productId = findProduct.productId;
      const status = util.getStatus(orderDetail.orderStatus);
      const createTime = util.formatTime(
        orderDetail.createTime,
        'YYYY-MM-DD HH:mm:ss'
      );
      const content = `
      <div>
        <div class="spaceBetween msg-flexstart">
          <img src="${findProduct.productPic}" class="msg-productImg" />
          <div>
            <div class="twoLine">${findProduct.productName.substring(0,30)}</div>
            <div class="msg-red">￥${orderDetail.payAmount}</div>
          </div>
        </div>
        <div class="spaceBetween">
            <div>订单状态：</div>
            <div>${status}</div>
        </div>
        <div class="spaceBetween">
            <div>订单号：</div>
            <div>${orderDetail.orderSn}</div>
        </div>
        <div class="spaceBetween">
            <div>订单时间：</div>
            <div>${createTime}</div>
        </div>
      </div>`;

      const attach = {
        data: {
          type: 'order',
          orderId,
          orderSn,
          productId,
          productSn,
        },
        body: {
          title: '我要咨询这笔订单',
          content,
        },
        type: 'kk_order_msg_fed',
      };
      m2kfSendOrder({
        orderId,
        kfIM: to,
      });
      store.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach),
        })
        .then((res) => {
          // 让消息滚动到可视区域
          this.$emit('doClose');
          document.getElementById(`${res.idClient}`).scrollIntoView();
        })
        .catch((err) => {
          this.$emit('doClose');
          console.log('发送失败', err);
        });
    },
  },
};
</script>

<style scoped>
.close_btn {
  font-size: 20px;
  cursor: pointer;
}
/* .productCard {
  position: fixed;
  bottom: 154px;
  right: 86px;
  z-index: 999;
  height: 96px;
  background: #fff;
  -webkit-box-shadow: 0 0 6px #ccc;
  box-shadow: 0 0 6px #ccc;
  width: 400px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
} */
.productCard {
  position: fixed;
  bottom: 117px;
  right: 152px;
  z-index: 999;
  height: 77.13px;
  background: #fff;
  /* -webkit-box-shadow: 0 0 6px #ccc;
  box-shadow: 0 0 6px #ccc; */
  border-radius: 17.14px;
  background: #fff;
  box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
  width: 394.219px;
  padding: 8.57px 10.283px 10.283px 9.427px;
  border: 1px solid #e5e5e5;
}
.cardContentText {
  width: 290px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  line-height: 15.426px;
  font-weight: 500;
  margin-top: 1.714px;
  letter-spacing: 0.56px;
}
.detailbox {
  overflow: hidden;
  /* margin: 0px 10px; */
  margin-left: 12px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 2px;
}
.price {
  color: #ff720c;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.linkbox {
  background: var(--btn-background-gradient);
  width: 86.577px;
  height: 25.71px;
  border-radius: 24px;
  color: #fff;
  cursor: pointer;
  font-family: 'PingFang SC';
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  font-size: 12px;
  font-weight: 500;
}
.productPic {
  min-width: 85.7px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  /* margin-left: 11px; */
}
</style>
