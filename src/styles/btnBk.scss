$btnGraniendNotBorder: url(../../static/imgs/btn_bk_not_border.png);
$btnGraniend: url(../../static/imgs/btn_bk1x.png);
$btnGraniend2x: url(../../static/imgs/btn_bk2x.png);
$btnGraniendPlayList: url(../../static/imgs/playList_btn_bk.png);
:root {
  --btn-background: url(../../static/imgs/header_nav_bk.svg);
  --game-list-background-gradient: linear-gradient(
      180deg,
      #fff1e2 16.51%,
      #fff9f3 69.94%,
      #ffe1c3 100%
    )
    local;
  --btn-background-gradient-hover: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 122, 0, 0.6) 0%,
      rgba(255, 122, 0, 0.6) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.153) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  // radial-gradient(
  //     238.39% 44.19% at 96.59% 31.25%,
  //     rgba(255, 122, 0, 0.6) 0%,
  //     rgba(255, 122, 0, 0.6) 100%
  //   ),
  //   radial-gradient(
  //     182.56% 55.34% at 5.68% 100%,
  //     rgba(246, 251, 34, 0.15) 0%,
  //     rgba(255, 158, 69, 0) 100%
  //   ),
  //   radial-gradient(
  //     137.51% 118.3% at 32.95% 0%,
  //     rgba(255, 137, 137, 0.83) 21.25%,
  //     rgba(255, 169, 106, 0.51) 88.62%
  //   ),
  //   radial-gradient(
  //     178.09% 220.16% at 94.89% -132.81%,
  //     #ff7a00 67.59%,
  //     rgba(255, 199, 0, 0.38) 100%
  //   );
  --btn-background-gradient: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 10%
    ),
    radial-gradient(
      0.2% 151% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 60%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 90%
    ),
    radial-gradient(
      98.05% 220% at 94.89% -125.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
}
.btnActiveNotBorder {
  color: #fff;
  border: none !important;
  background: $btnGraniendNotBorder !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  &:focus,
  &:hover {
    background: $btnGraniendNotBorder !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }
}
.loginBtnDefalut {
  background: #969696;
  color: #fff;
  border: 1px solid #ffddbe;
  // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  &:focus,
  &:hover {
    border: 1px solid #fff;
    // background: $btnGraniend !important;
    // background-size: 100% 100% !important;
    // background-repeat: no-repeat !important;
    background: var(--btn-background-gradient);
  }
}
.loginBtnActive {
  color: #fff;
  // border: none !important;
  // background: $btnGraniend !important;
  // background-size: 100% 100% !important;
  // border: 0px solid #fff;
  // background-repeat: no-repeat !important;
  background: var(--btn-background-gradient);
  &:focus,
  &:hover {
    // border: 0px solid #fff;
    // background: $btnGraniend !important;
    // background-size: 100% 100% !important;
    // // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset !important;
    // background-repeat: no-repeat !important;
    border: 1px solid #ffddbe;
    background: var(--btn-background-gradient);
  }
}
.btnDefalut2x {
  background: #969696;
  color: #fff;
  border-color: #969696;
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  &:focus,
  &:hover {
    border: 1px solid #ffddbe;
    background: var(--btn-background-gradient);
    box-shadow: none;
    // background: $btnGraniend2x !important;
    // background-size: 100% 100% !important;
    // background-repeat: no-repeat !important;
    // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset !important;
  }
}
.btnActive2x {
  color: #fff;
  // border: none !important;
  // background: $btnGraniend2x !important;
  // background-size: 100% 100% !important;
  // border: 1px solid #fff;
  // background-repeat: no-repeat !important;
  border: 1px solid #ffddbe;
  background: var(--btn-background-gradient);
  &:focus,
  &:hover {
    // border: 1px solid #fff;
    // background: $btnGraniend2x !important;
    // background-size: 100% 100% !important;
    // background-repeat: no-repeat !important;
    border: 1px solid #ffddbe;
    background: var(--btn-background-gradient);
  }
}

.playListBtnBk {
  color: #fff;
  // border: none !important;
  // background: $btnGraniendPlayList !important;
  // background-size: 100% 100% !important;
  // // border: 1px solid #fff;
  // background-repeat: no-repeat !important;
  border: 1px solid #ffddbe;
  background: var(--btn-background-gradient);
  &:focus,
  &:hover {
    // // border: 1px solid #fff;
    // background: $btnGraniendPlayList !important;
    // background-size: 100% 100% !important;
    // background-repeat: no-repeat !important;
    border: 1px solid #ffddbe;
    background: var(--btn-background-gradient);
  }
}

.border-gradient-img-big-style {
  background: url(../../static/imgs/border_big_bk.png);
  background-size: 100% 100%;
}
.border-gradient-img-medium-style {
  background: url(../../static/imgs/border_medium_bk.png);
  background-size: 100% 100%;
}
// .border-gradient-img-small-style {
//   background: url(../../static/imgs/border_bk.png);
//   background-size: 100% 100%;
// }

.border-gradient-color-style {
  background-clip: padding-box, border-box;

  background-origin: padding-box, border-box;

  background-image: linear-gradient(to right, #fff, #fff),
    linear-gradient(
      180deg,
      rgba(255, 122, 0, 0.6) 8%,
      rgba(255, 199, 0, 0.6) 20%
    ) !important;
}

.btn_gradient_color_style {
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset !important;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 10%
    ),
    radial-gradient(
      0.2% 151% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 60%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 90%
    ),
    radial-gradient(
      98.05% 220% at 94.89% -125.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500) !important;
  color: #fff !important;
  border: 1px solid #ffddbe;
}

.text-gradient-color-style {
  background: var(--btn-background-gradient);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.gameListBk {
  background: var(--game-list-background-gradient) !important;
  padding-bottom: 80px;
}

.playListBk {
  background: linear-gradient(180deg, #fdf5ed 36.61%, #fff 53.46%, #ffe1c3 100%)
    local !important;
  padding-bottom: 80px;
}
