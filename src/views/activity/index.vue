<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="pdTopBottom">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>活动</el-breadcrumb-item>
      </el-breadcrumb>

      <div
        class="safe_width spaceBetween"
        style="align-items: flex-start; margin-bottom: 60px"
      >
        <div class="right_container">
          <div class="spaceAround btns">
            <img
              class="btn"
              src="../../../static/activity/btn1.png"
              @click="showDetail(0)"
            />
            <img
              class="btn"
              src="../../../static/activity/btn2.png"
              @click="showDetail(1)"
            />
            <img
              class="btn"
              src="../../../static/activity/btn3.png"
              @click="showDetail(2)"
            />
          </div>
          <div v-if="detailIndex == 0" class="pageDate">
            <img
              class="pic"
              src="https://images2.kkzhw.com/mall/statics/site/%E8%B5%9B%E4%BA%8B%E9%A1%B5%E9%9D%A2%E4%BB%8B%E7%BB%8D.jpg"
            />
          </div>
          <div v-if="detailIndex == 1" class="pageDate">
            <img
              class="pic"
              src="https://images2.kkzhw.com/mall/statics/huodong/kkzhw_huodong_richeng.jpg"
            />
          </div>
          <div v-if="detailIndex == 2" class="pageDate gridBox">
            <!-- <img class="empty" src="../../../static/activity/empty.png" /> -->

            <div
              v-for="(item, index) in videoList"
              :key="index"
              class="videoUser_item"
              @click="openVideo(item)"
            >
              <img :src="item.pic" class="videoUser_pic" />
              <div class="videoUser_video">
                <img src="../../../static/icon-play.png" />
              </div>

              <div>{{ item.name }}</div>
            </div>

            <!-- <div
                  v-for="(item, index) in videoList"
                  :key="index"
                  class="videoUser_item"
                  @click="openVideo(item)"
                >
                  <img :src="item.pic" class="videoUser_pic" />
                  <div class="videoUser_video">
                    <img src="../../../static/icon-play.png" />
                  </div>
                  <div>{{item.name}}</div>
                </div>


                <div
                  v-for="(item, index) in videoList"
                  :key="index"
                  class="videoUser_item"
                  @click="openVideo(item)"
                >
                  <img :src="item.pic" class="videoUser_pic" />
                  <div class="videoUser_video">
                    <img src="../../../static/icon-play.png" />
                  </div>
                  <div>{{item.name}}</div>
                </div> -->
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import { getHelpDetail } from '@/api/help';
import { getGameList, getZbList } from '@/api/index2.js';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      detailIndex: 0,
      index: 6,
      defaultActive: '306662',
      activeId: '306662',
      type: '',
      detailHtml: '',
      showzf: false,
      pageDate: [],
      videoList: [],
    };
  },
  watch: {
    '$route.query.id'(toVal, fromVal) {
      this.open();
    },
  },
  mounted() {
    this.open();
    getZbList({
      game: '逆水寒手游',
      // type: '独立主播',
      type: '合作主播',
    }).then((res) => {
      if (res.code === 200) {
        this.videoList = res.data || [];
      }
    });
  },
  methods: {
    showDetail(index) {
      this.detailIndex = index;
    },
    open() {
      //   this.handleOpen(this.activeId);
    },
    handleOpen(id) {
      if (id == '-1') {
        this.showzf = true;
        getGameList().then((res) => {
          if (res.code == 200) {
            this.pageDate = res.data;
          }
        });
      } else {
        this.showzf = false;
        getHelpDetail(id).then((res) => {
          this.detailHtml = res.data.detailHtml;
        });
      }
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 列表初始化
    chooseHelp(date) {
      this.activeId = date.id;
      this.initDetail();
    },
    openVideo(date) {
      const { url } = date;
      if (url.indexOf('http') == 0) {
        window.open(date.url, '_blank');
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pageDate {
  width: calc(100% - 140px);
  margin: 30px auto;
  min-height: 500px;
  .pic {
    width: 100%;
    object-fit: contain;
  }
  .empty {
    width: 400px;
    object-fit: contain;
  }
}
.expen_item {
  width: 48%;
  background: #fff;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.19);
  border-radius: 10px;
  margin-bottom: 20px;
  padding: 20px;
  cursor: pointer;
  flex-shrink: 0;
}
.expe_logo {
  width: 60px;
  height: 60px;
  margin-right: 20px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}
.expe_logo img {
  width: 100%;
}
.expe_intro {
  font-size: 16px;
  font-weight: 300;
  color: #5b5b5b;
  line-height: 24px;
}
.expe_heade {
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 15px;
}
.expe_foot {
  padding-top: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
}
.right_container {
  min-height: 100vh;
  width: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background: url(https://images2.kkzhw.com/mall/statics/site/%E7%9C%8B%E7%9C%8B%E7%9B%9B%E4%B8%96%E8%B5%9B%E4%BA%8B%E9%A1%B5%E9%9D%A2%E5%A4%B4%E5%9B%BEPC.jpg)
    no-repeat;
  background-size: contain;
  background-color: #fff;
  .btns {
    width: 800px;
    margin: 620px auto 0;
    height: 40px;
    .btn {
      cursor: pointer;
      height: 40px;
      width: 180px;
    }
  }
}
.help_tit {
  height: 55px;
  width: 100%;
  line-height: 55px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
  box-sizing: border-box;
  padding: 0 10px 0 30px;
  cursor: pointer;
  border-left: 4px solid transparent;
  transition: all 0.3s;
}
.help_tit:hover,
.help_tit.active {
  border-color: #ff6716;
  color: #ff6716;
  background: #fff4ee;
}

.gridBox {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  column-gap: 12px;
  row-gap: 12px;

  min-height: 0;
}
.videoUser_item {
  position: relative;
  width: 100%;
  height: 190px;
  background: #fff;
  border-radius: 8px;
  flex-shrink: 0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;

  color: #333;
  font-weight: 600;
  text-align: center;
}
.videoUser_pic {
  width: 100%;
  height: 150px;
  margin-bottom: 10px;
  transition: all 0.3s;
}
.videoUser_item:hover .videoUser_pic {
  transform: scale(1.1);
}
.videoUser_video {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  text-align: center;
  opacity: 0;
  transition: all 0.3s;
}
.videoUser_video > img {
  width: 80px;
  margin-top: 40px;
}
.videoUser_item:hover .videoUser_video {
  opacity: 1;
}
</style>
