.assureListContent {
  margin-bottom: 129px;
  border-radius: 30px;
  overflow: hidden;
  background: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  position: relative;
}
.assureListContent::before {
  content: '';
  position: absolute;
  background: linear-gradient(
    180deg,
    #fff 27.6%,
    rgba(255, 251, 247, 0.95) 98.76%
  );
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 3px;
  border-radius: 28px;
  z-index: 3px;
}
.header_custorm {
  padding: 40px 0px 0px 40px;
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  position: relative;
  z-index: 2;
}
.cus_icon {
  width: 24px;
  margin-right: 10px;
}
.cusAss_item {
  width: 232px;
  height: 328px;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  border-radius: 30px;
  box-sizing: border-box;
  text-align: center;
  transition: all 0.3s;
  margin-right: 40px;
  margin-bottom: 40px;
  border: 1px solid transparent;

  background: linear-gradient(to right, #fffcf9, #fffcf9),
    radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
}
.cusAss_item .title {
  color: #1b1b1b;
  text-align: center;
  height: 27.542px;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.cusAss_item .time {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.56px;
}
.cusAss_item:nth-child(5n + 5) {
  margin-right: 0;
}
/* .cusAss_item:hover,
.cusAss_item.active {
  border-color: #ff6716;
} */
.cusSss_item_pic {
  width: 160px;
  height: 160px;
  overflow: hidden;
  margin: 20px auto;
}
.cusGo_btn {
  background: var(--btn-background-gradient);
  border-radius: 60px;
  border: 1px solid #ffddbe;
  margin: 0 auto;
  width: 162px;
  height: 44.422px;
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
  cursor: pointer;
}
.cus_descWrap {
  width: 100%;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.8px;
  display: flex;
  align-items: center;
  height: 28px;
  border-top: 0.5px solid #ff7a00;
  padding-top: 40px;
  div {
    background: var(--btn-background-gradient);
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
    -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    margin: 8px 2.5px 0px 2.5px;
    font-size: 30px;
  }
}
.descWrap_tit {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin-top: 40px;
  text-indent: -12px;
}
.descWrap_body {
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  color: #1b1b1b;
  line-height: 24px;
  letter-spacing: 0.56px;
  padding-top: 12px;
}
