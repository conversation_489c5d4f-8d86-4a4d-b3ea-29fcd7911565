.homeBg_con {
  background-size: 100% auto;
  background: linear-gradient(
    180deg,
    #fdf5ed 36.61%,
    #fff 53.46%,
    #ffe1c3 200%
  );
}
.swiper_home {
  // width: 900px;
  width: 918px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.6);
}
.swiper_home /deep/ .el-carousel__indicator--horizontal {
  padding: 13px 3px;
}
.swiper_home /deep/ .el-carousel__indicator--horizontal .el-carousel__button {
  width: 9px;
  height: 9px;
  background: #fbf9f7;
  border-radius: 50%;
  opacity: 1;
}
.swiper_home
  /deep/
  .el-carousel__indicator--horizontal.is-active
  .el-carousel__button {
  width: 9px;
  height: 9px;
  background: #ff720c;
  border-radius: 50%;
  opacity: 1;
}
.swiper_home /deep/ .el-carousel__indicators {
  // 指示灯位置
  left: 50%;
  transform: translateX(-50%);
  bottom: 0px;
  // right: 2%;
}
.text_size_16 {
  font-size: 14px;
  color: #a3a3a3;
  text-align: right;
  font-family: PingFang SC;
  letter-spacing: 0.56px;
}
.text_size_14 {
  font-size: 16px;
  color: #a3a3a3;
  text-align: right;
  font-family: PingFang SC;
}
.safe_width {
  align-items: flex-start;
}

.newsell {
  width: 918px;
  height: 51px;
  padding: 0 20px;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 25.714px;
  border: 0.857px solid #fff;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.95) 44.5%,
    rgba(255, 255, 255, 0.76) 75%
  );
  box-shadow: 8.571px 8.571px 42.857px 0px rgba(255, 107, 0, 0.1);
  backdrop-filter: blur(42.85714340209961px);
  .newSell_pic {
    width: 141px;
    height: 39px;
    // margin-left: 20px;
  }
  .seaml {
    height: 100%;
    font-weight: 600;
    overflow: hidden;
    .seaml_item {
      // width: 595px;
      height: 51px;
      font-size: 14px;
      font-family: 'PingFang SC';
      color: #222;
      font-weight: 400;
      letter-spacing: 0.56px;
      .text_price_title {
        color: #1b1b1b;
        font-weight: 500;
      }
      .cate {
        font-style: italic;
        width: 110px;
      }
      .name {
        // width: 595px;
        flex: 1px;
        // width: 100%;
        margin: 0 6px;
      }
      .price {
        // color: #ffb74a;
        font-weight: 600;
        font-size: 16px;
        // margin-right: 34.28px;
        letter-spacing: -0.4px;
        font-family: Inter;
      }
    }
  }
}

.homeSwiper_right {
  width: 262px;
  // height: 500px;
  box-sizing: border-box;
  .top {
    padding: 19px 22.5px 21px 22.5px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 20px;
    height: 202px;

    .logo {
      width: 49.98px;
      height: 51px;
      margin: 0 auto;
      display: block;
    }
    .title {
      // color: #ff720c;
      color: transparent;
      background-clip: text;
      background-image: linear-gradient(180deg, rgba(255, 122, 0, 1), #ffb74a);
      text-align: center;
      font-weight: 600;
      padding-top: 3px;
      font-size: 14px;

      img {
        width: 174px;
      }
    }
    .count_box {
      text-align: center;
      font-size: 20.5px;
      color: #ffb74a !important;
      align-items: center;
      justify-content: space-between;
      padding-top: 11px;
      // .subtitle {
      //   font-size: 12px;
      //   color: rgba(0, 0, 0, 0.4);
      //   font-weight: 500;
      //   font-family: 'PingFang SC';
      // }
      .count {
        font-weight: 600;
        font-size: 22px;
        letter-spacing: -0.55px;
        font-family: Inter;
        line-height: 103%;
      }
      .left {
        width: 97px;
        // height: 94px;
        border-right: 1px solid rgba(255, 122, 0, 0.2);
        padding-right: 12px;
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        align-items: end;
        .subtitle {
          width: 90.85578px;
          line-height: 16px;
          // text-align: center;

          font-size: 12px;
          color: rgba(0, 0, 0, 0.4);
          font-weight: 500;
          font-family: 'PingFang SC';
          letter-spacing: 0.24px;
        }
        .count {
          width: 90.85578px;
          // height: 24.856px;
          text-align: center;

          // line-height: 24.856px;
        }
        // rgba(255, 199, 0, 0.2)
        .subbtn_box {
          // margin-top: 8px;

          display: flex;
          align-items: end;
          .subbtn {
            display: block;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            width: 97px;
            height: 32px;
            // padding: 10px 30px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 12px;
            line-height: 16px;
            font-family: 'PingFang SC';
            margin-top: 8px;
            letter-spacing: 0.24px;
            color: #fff;
          }
          .subbtn:hover {
            color: #fff !important;
          }
        }
      }
      .right {
        width: 97px;
        // height: 94px;
        text-align: left;
        // padding-left: 6.56px;
        .subtitle {
          width: 90.85578px;
          line-height: 16px;
          text-align: center;
          // margin-left: 4.2px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.4);
          font-weight: 500;
          font-family: 'PingFang SC';
          letter-spacing: 0.24px;
        }
        .count {
          width: 90.85578px;
          // height: 24.856px;
          text-align: center;

          // line-height: 24.856px;
        }
        .subbtn_box {
          margin-right: 34px;
          margin-top: 6.856px;
          .subbtn {
            display: block;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            margin-top: 8px;
            font-weight: 500;
            width: 97px;
            height: 32px;
            letter-spacing: 0.24px;
            // padding: 7px 30px;
            border-radius: 20px;
            font-size: 12px;
            border: none;
            font-family: 'PingFang SC';
            background: var(--btn-background-gradient);

            position: relative;
            span {
              background: var(--btn-background-gradient);
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
              position: relative;
              z-index: 3;
            }
            &::before {
              content: '';
              position: absolute;
              top: 0px;
              left: 0px;
              bottom: 0px;
              right: 0px;
              margin: 2px;
              border-radius: 20px;
              background: #fff;
            }
          }
        }
      }
    }
    .btn_box {
      padding-top: 10px;
      .subbtn {
        width: 130px;
        height: 40px;
        line-height: 40px;
        font-size: 18px;
        display: block;
        text-align: center;
        color: #fff;
        border-radius: 3px;
      }
      .left {
        .subbtn {
          background: linear-gradient(to right, #ff831e, #fe5b16);
        }
      }
      .right {
        .subbtn {
          background: linear-gradient(to right, #0cb0fa, #097af9);
        }
      }
    }
  }

  .bottom {
    margin-top: 20px;
    padding: 21px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 30px;
    height: 209px;
    background: url('../../../static/safe.png') no-repeat;
    background-size: cover;
    .title {
      line-height: 0px !important;
    }
    .search_input_box {
      width: 200px;
      height: 40px;
      margin: 0px auto;
      display: flex;
      align-items: center;
      margin-top: 8px;
      border-radius: 20px;
      padding: 0px 2.571px;

      // border: 2.571px solid transparent;
      .el-input__icon {
        font-size: 17px;
        // margin-right: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: rgba(0, 0, 0, 1);
      }
      ::v-deep input {
        height: 36px;
        font-family: 'PingFang SC';
        border-radius: 36px;
        font-size: 12px;
        border: none;
      }
    }
    .tab0::v-deep .tab-btn:nth-child(2) {
      border-radius: 16px 16px 0px 16px;
      margin-left: 0px;
      border-left: none;
      &::after {
        content: '';
        width: 14px;
        height: 14px;
        border: 1.714px solid #ffb74a;
        border: none;
        border-left: 1.714px solid #ffb74a;
        border-top: 1.5px solid #ffb74a;
        position: absolute;
        z-index: 100;
        left: -1px;
        top: -1.2px;
        border-radius: 20px 0px 0 0;
      }
    }
    .myTabLeft {
      // position: absolute;
      // top: 528px;
      // right: 253px;
      // width: 10px;
      // height: 14px;
      // background: #fff6eb;
      // z-index: 10;
      // border-radius: 27px;
      // border-left: 1.714px solid #ffb74a;
      // transform: rotate(40deg);
    }
    .tab0::v-deep .tab-btn:nth-child(3) {
      border-radius: 12px 12px 0px 0px;

      margin-left: -1px;
    }
    .tab1::v-deep .tab-btn:nth-child(1) {
      border-radius: 12px 12px 12px 0px !important;
      border-right: 0px;
      margin-right: 0px;

      &::after {
        content: '';
        width: 14px;
        height: 14px;
        border: 1.714px solid #ffb74a;
        border: none;
        border-right: 1.714px solid #ffb74a;
        border-top: 1.714px solid #ffb74a;
        position: absolute;
        z-index: 100;
        right: -1px;
        top: -1.2px;
        border-radius: 0 12px;
      }
    }
    .tab1::v-deep .tab-btn:nth-child(2) {
      .left::after {
        border-bottom-right-radius: 13px !important;
      }
      .right::after {
        border-bottom-left-radius: 13px !important;
      }
    }
    .tab1::v-deep .tab-btn:nth-child(3) {
      border-radius: 12px 12px 0px 12px !important;
      border-left: 0px;
      margin-left: 0px;
      &::after {
        content: '';
        width: 14px;
        height: 14px;
        border: 1.714px solid #ffb74a;
        border: none;
        border-left: 1.714px solid #ffb74a;
        border-top: 1.714px solid #ffb74a;
        position: absolute;
        z-index: 100;
        left: -1px;
        top: -1.2px;
        border-radius: 12px 0px 0 0;
      }
    }
    .tab2::v-deep .tab-btn:nth-child(1) {
      border-radius: 12px 12px 0px 0px !important;
      margin-right: -1px;
    }
    .tab2::v-deep .tab-btn:nth-child(2) {
      border-radius: 12px 12px 12px 0px !important;
      border-right: 0px;
      margin-right: 0px;
      &::after {
        content: '';
        width: 14px;
        height: 14px;
        border: 1.714px solid #ffb74a;
        border: none;
        border-right: 1.714px solid #ffb74a;
        border-top: 1.714px solid #ffb74a;
        position: absolute;
        z-index: 100;
        right: -1px;
        top: -1.2px;
        border-radius: 0 12px;
      }
    }
    .tab2::v-deep .tab-btn:nth-child(3) {
      .left::after {
        border-bottom-right-radius: 13px !important;
      }
    }

    ::v-deep .tab-btn {
      // flex: none !important;
      // width: 69px;
      font-size: 12px !important;
      padding: 0px;
      // border: none !important;

      border: 1.714px solid #ffb74a;
      border-bottom: 0px;
      // padding: 2.5px 0;
      height: 28px;

      font-family: 'PingFang SC';
      color: #ffb74a;
      background: rgba(255, 246, 235, 1);
      letter-spacing: 0.42px;
      &.selected {
        border-bottom-right-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }
      .right {
        &::before {
          // width: 0px;
          // left: 28px;
          // height: 17px !important;
          // background: red;
          background: #ffe0ca;
          height: 18px;
        }
        &::after {
          height: 17.5px;
          background: rgba(255, 122, 0, 0.6);
          content: '';
          position: absolute;
          right: -28px;
          bottom: 0.5px;
          width: 28px;
          border-bottom-left-radius: 15px !important;
        }
      }
      .left {
        &::before {
          // width: 0px;
          // left: 28px;
          height: 18px !important;
          // background: red;
          background: #ffe0ca;
        }
        &::after {
          height: 17.7px;
          background: rgba(255, 122, 0, 0.6);
          bottom: 0.5px;
          border-bottom-right-radius: 15px !important;
        }
        // display: none;
      }
    }
    ::v-deep .selected {
      height: 32px;
      padding: 0px;
      border: solid 1.714px rgba(255, 122, 0, 0.6);
      letter-spacing: 0.28px;
      // line-height: 14px;
      border-bottom: none;
      border-radius: 12px !important;
      // background: #ffe0ca;
      // border: 2px solid transparent;
      background: #ffe0ca;
      font-size: 14px;
      color: #222;
      font-family: 'PingFang SC';
      // position: relative;
      // z-index: 19;
      // background-clip: padding-box, border-box;

      // background-origin: padding-box, border-box;

      // background-image: rgba(255, 122, 0, 0.6)
    }
    ::v-deep .tab-body {
      height: 62.561px !important;
      min-height: 62.561px !important;
      border: solid 1.714px transparent;
      border-radius: 16px !important;
      background: #ffe0ca;
      background-clip: padding-box, border-box;

      background-origin: padding-box, border-box;

      background-image: linear-gradient(to right, #ffe0ca, #ffe0ca),
        linear-gradient(
          180deg,
          rgba(255, 122, 0, 0.6) 8%,
          rgba(255, 199, 0, 0.6) 20%
        ) !important;
      &.no-top-left {
        border-top-left-radius: 0 !important;

        &::before {
          border-top-left-radius: 0 !important;
        }
      }

      &.no-top-right {
        border-top-right-radius: 0 !important;
      }
    }
    .title {
      color: #ff720c;
      text-align: center;
      font-weight: 600;
      font-size: 14px;
      font-family: 'PingFang SC';
      font-weight: 500;
      line-height: 18px; /* 128.571% */
      letter-spacing: 0.56px;
      img {
        width: 115px;
      }
    }
    .search_box {
      margin-top: 10px;
      .search_ipt {
        height: 30px;
        line-height: 30px;
        font-size: 12px;
        color: #333;
        border: none;
        text-indent: 10px;
        font-weight: 500;
        width: 70%;
        outline: none;
        background: #f7f7f7;
      }
      .btn {
        cursor: pointer;
        border-radius: 3px;
        width: 60px;
        height: 30px;
        background: #0183ff;
        line-height: 30px;
        font-size: 14px;
        color: #fff;
        text-align: center;
      }
    }
    .btn_box {
      margin-top: 15px;
      font-size: 14px;
      text-align: center;
      color: #fff;
      .btn {
        cursor: pointer;
        border-radius: 5px;
        width: 32%;
        padding: 8px 0;
        background: #5d5c5a;
      }
      .btn:hover {
        background-color: #fe5a1e;
      }
      .btn.active {
        background-color: #fe5a1e;
      }
    }
  }
  .border_bottom_style {
    border: 1px solid transparent;
    background-clip: padding-box, border-box;

    background-origin: padding-box, border-box;

    background-image: linear-gradient(to right, #fff, #fff),
      linear-gradient(
        180deg,
        rgba(255, 255, 255, 1) 40%,
        rgba(255, 225, 195, 1) 100%
      );
  }
}

.paTopCom {
  padding-top: 20px;
}

.swip_check_wrap {
  width: 242px;
  height: 116px;
  background: #ffffff;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 0 20px;
}

.swip_item_show {
  width: 309px;
  height: 116px;
  border-radius: 6px;
  cursor: pointer;
  box-sizing: border-box;
  padding: 20px;
  display: block;
}
.swip_item_show.one {
  background: url(../../../static/h1.png) no-repeat center top;
  background-size: cover;
}
.swip_item_show.two {
  background: url(../../../static/h2.png) no-repeat center top;
  background-size: cover;
}
.swip_item_show.three {
  background: url(../../../static/h3.png) no-repeat center top;
  background-size: cover;
}
.s_tit_one {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  padding-top: 10px;
}
.s_tit_sub {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  padding-top: 8px;
}

.hot_news_wrap {
  width: 100%;
  box-sizing: border-box;
  background: #ffffff url(../../../static/laba.png) no-repeat 10px center;
  background-size: 26px;
  border: 1px solid #787878;
  border-radius: 25px;
  padding: 14px 30px 14px 42px;
  margin-top: 12px;
  font-size: 14px;
  color: #666666;
}

.hotGames_container {
  // width: 900px;
  // width: 1020px;
  width: 779px;
  // height: 464px;
  background: #ffffff;
  overflow: hidden;
}
.findback_box {
  margin-top: 20.5px;
  display: flex;
  align-items: flex-start;
  .findback_tit {
    height: 42px;
    margin-bottom: 16px;
  }
}
.findback {
  width: 505px;
  // height: 147.4px;
  border-radius: 10px;
  background-color: #fff;
  padding: 0px;
  .findhead {
    width: 117px;
    object-fit: contain;
  }
  .finditem {
    cursor: pointer;
    width: 158px;
    height: 86px;
    overflow: hidden;
    border-radius: 10px;
  }
  .finditem_pic {
    transition: all 0.3s;
    border-radius: 10px;
    width: 158px;
    height: 86px;
  }
  .finditem_pic:hover {
    transform: scale(1.1);
  }
}
.hotGamesBox {
  padding-bottom: 80px;
  margin-top: 20px;
  .hotGamesContent {
    border-radius: 30px;
    padding: 40px;
  }
  .hotGames_left {
    width: 819px;
    padding-right: 40px;
    border-right: 1px solid #ececec;
  }
}

.hotGames_head {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-size: 14px;
  height: 42px;
  font-weight: 400;
  color: #a3a3a3;
  font-family: 'PingFang SC';
  box-sizing: border-box;
  // padding: 40px 40px 21px 40px;
  // background: url(../../../static/hotBg.png) no-repeat center top;
  // background-size: cover;
}
.hotGames_head1 {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-size: 14px;
  font-weight: 400;
  height: 42px;
  color: #a3a3a3;
  box-sizing: border-box;
  margin-bottom: 17.14px;
  // margin-top: -2px;
  // padding: 14px;
  background: #fff;
  // background: url(../../../static/hotBg.png) no-repeat center top;
  // background-size: cover;
}
.records_container {
  width: 240px;
  height: 346px;
  background: #ffffff url(../../../static/recBg.png) no-repeat center top;
  background-size: 100% auto;
  border-radius: 10px;
}
.video_container {
  width: 215.883px;
  height: 149.124px;
  // background: #ffffff url(../../../static/recBg.png) no-repeat center top;
  background-size: 100% auto;
  border-radius: 10px;
}
.video_container_box {
  width: 207px;
  height: 86px;
  box-sizing: border-box;
  /* padding: 0 10px 10px; */
  background: #fff;
  /deep/ .el-carousel__arrow--right {
    display: block !important;
    width: 25px;
    height: 25px;
    right: 14px;
    background: rgba(255, 255, 255, 0.2);
  }
  /deep/.el-icon-arrow-left {
    &::before {
      color: #fff;
      font-size: 12.5px;
    }
  }
  /deep/ .el-carousel__arrow--left {
    display: block !important;
    width: 25px;
    height: 25px;
    left: 14px;
    background: rgba(255, 255, 255, 0.2);
  }
  /deep/.el-icon-arrow-right {
    &::before {
      color: #fff;
      font-size: 12.5px;
    }
  }
  .swiper_video {
    width: 100%;
    border-radius: 10px;
  }
}

.accounts_container {
  width: 100%;
  margin-top: 20px;
  // background: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(247, 247, 247, 0.5);
  border-radius: 30px;
  padding: 40px 39px 20px 39px;
  // filter: drop-shadow(10px 10px 50px rgba(255, 107, 0, 0.05));
}
.user_avter {
  width: 62px;
  height: 62px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}
.h_swi_tit {
  font-size: 14px;
  padding-top: 14px;
  padding-bottom: 20px;
  color: #222222;
  text-align: center;
}
.h_swi_btn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 16px;
  font-size: 14px;
  color: #ffffff;
  padding: 8px 20px;
  cursor: pointer;
}
.h_swi_wrap {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  // margin-top: 26px;
  // border-bottom: 1px solid #f1f1f1;
}
.swip_check_wrap .h_swi_wrap {
  margin-top: 20px;
  border-bottom: none;
}
.h_swi_wrap_itm {
  width: 153px;
  height: 46px;
  // padding: 8.57px 35.994px;
  // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  border: 1px solid rgba(255, 255, 255, 0.4);
  background: rgba(210, 210, 210, 0.4);
  border-radius: 60px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  position: relative;
  // padding-bottom: 14px;
  cursor: pointer;
  transition: all 0.3s;
  margin-right: 12px;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.64px;
}
.h_swi_wrap_itm:after {
  content: '';
  height: 2px;
  width: 50%;
  background: transparent;
  position: absolute;
  left: 25%;
  bottom: 0;
}

.topGmae_header {
  // background: url(../../../static/topBg.png) no-repeat center top;
  background-size: cover;
  padding-bottom: 20px;
}
.topGmae_header .h_swi_wrap {
  width: 320px;
  padding-right: 20px;
  border-bottom: none;
}

.checkQQ_wrap {
  margin-top: 10px;
  background: #f7f7f7;
}
.checkQQ_ipt {
  height: 40px;
  line-height: 40px;
  font-size: 12px;
  color: #333;
  border: none;
  text-indent: 10px;
  font-weight: 500;
  background: transparent;
  width: 70%;
  outline: none;
}
.checkQQ_ipt:focus {
  border: none;
}
.qq_sBtn {
  width: 60px;
  flex-shrink: 0;
  height: 40px;
  background: #ff6716;
  border-radius: 2px;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  color: #ffffff;
}
.gameAll_homeWrap {
  flex-wrap: wrap;
  // padding-top: 14px;
  // padding: 0px 40px 0px 40px;
  // height: 354px;
  padding: 0px;
  border-radius: 0px;
  padding-bottom: 4px;
  margin-top: 16px;
  border-bottom: 0.834px solid #ececec;
  display: flex;
  align-items: baseline;
}
.gameH_wrap {
  width: 84px;
  text-align: center;
  font-size: 14px;
  letter-spacing: 0.24px;
  // height: 144px;
  font-family: 'PingFang SC';
  font-weight: 500;
  line-height: 16px;
  color: #222222;
  margin-bottom: 16px;
  // margin-right: 8px;
  margin-left: 15.25px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  &:nth-child(1) {
    margin-left: 0px;
  }
  &:nth-child(9) {
    margin-left: 0px;
  }
}

.gameH_wrap:hover {
  transform: translateY(-5px);
}

.game_wrap_pic {
  margin: 0 auto;
  width: 84px;
  height: 84px;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5px;
}
.countNum {
  width: 100%;
  display: block;
  // height: 56px;
  font-size: 36px;
  padding-bottom: 8px;
  letter-spacing: -1.08px;
  margin-bottom: 8px;
  font-family: Inter;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid rgba(255, 122, 0, 0.2);

  //   background: url(../../../static/imgs/header_nav_bk.svg);
  // background-size: cover;
  // padding-top: 35px;
}
.coytWrap_subT {
  font-size: 14px;
  font-family: PingFang SC;
  color: #222222;
  text-align: center;
}
.count_footer {
  border-top: 1px solid #dcdcdc;
  padding-top: 30px;
  font-size: 14px;
  font-family: PingFang SC;
  color: #666666;
  margin-top: 40px;
}
.count_footerItem {
  text-align: center;
}
.count_footerItem_line {
  width: 2px;
  height: 70px;
  border: 1px dashed #ff6716;
}
.count_footerNum {
  font-size: 22px;
  font-family: Roboto;
  font-weight: 500;
  color: #ff6716;
  padding-top: 8px;
}
.topGame_item {
  width: 271px;
  height: 313px;
  background: #ffffff;
  box-shadow: 0.848px 1.697px 5.091px 0px rgba(0, 0, 0, 0.1);
  border-radius: 16.97px;
  transition: all 0.3s;
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  margin-bottom: 20px;
  margin-right: 12px;
}
.topGame_item:nth-child(4n + 4) {
  margin-right: 0;
}
.topGame_itemPic {
  width: 236px;
  height: 132px;
  border-radius: 10px;
  overflow: hidden;
  margin: 0 auto;
  margin-top: 16.97px;
  position: relative;
}
.topGame_item:hover  .home_goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
}
.topGame_itemBody {
  width: 238.246px;
  font-size: 14px;
  font-weight: 400;
  color: #aaaaaa;
  box-sizing: border-box;
  // padding: 10px;
  margin: 0 auto;
}
.topGame_name {
  font-family: 'PingFang SC';
  color: #1b1b1b;
  font-weight: 500;
  font-size: 14px;
  line-height: 20.296px;
  margin-top: 10.18px;
  margin-bottom: 16.97px;
  height: 54px;
  font-weight: 500;
  line-height: 18px; /* 128.571% */
  letter-spacing: 0.56px;
}
.topGame_price {
  color: #a3a3a3;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-bottom: 1px solid #f4f4f4;
  padding-bottom: 10.18px;
  letter-spacing: 0.24px;
  align-items: flex-end !important
  ;

  // padding: 15px 0 10px;
}
.topGame_priceNum {
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 103%;
  color: #ff720c;
  letter-spacing: -0.4px;
}
.topGame_item:nth-child(4n -1) {
  .topGame_content {
    left: -450px;
  }
}
.topGame_item:nth-child(4n) {
  .topGame_content {
    left: -450px;
  }
}
.topGame_item:nth-child(1),
.topGame_item:nth-child(2),
.topGame_item:nth-child(5),
.topGame_item:nth-child(6) {
  .topGame_content {
    left: 271px;
  }
}
.topGame_content {
  position: absolute;
  left: 280px;
  top: 0;
  background: #fff;
  width: 450px;
  min-height: 312px;
  padding: 32px 42px 33px 35px;
  z-index: 1000;
  box-shadow: 0px 0px 5px 0px rgba(128, 128, 128, 0.26);
  border-radius: 24px;
  box-sizing: border-box;
  padding: 20px;
  display: none;
}
.topGame_contName {
  color: #000;
  font-family: PingFang SC;
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.topGame_contText {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 26px;
  font-family: 'PingFang SC';
  height: 300px;
  overflow-y: auto;
}
.topTips_tit_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #000;
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  margin: 8px 0px;
}
.topGame_item:hover {
  box-shadow: 0px 0px 5px 0px rgba(128, 128, 128, 0.45);
  transform: translateY(-5px);
  position: relative;
  z-index: 10;
}
.topGame_item:hover > .topGame_content {
  display: block;
  z-index: 100;
}
.sRight_wrap {
  border-top: 1px solid #eaeaea;
  padding-top: 16px;
}
.sRight_pic {
  width: 45px;
  margin-bottom: 8px;
}
.sRight_item {
  display: block;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  color: #909090;
}
.wrap_page {
  height: 180px;
  font-size: 14px;
  color: #222222;
  line-height: 30px;
  padding: 14px 5px;
}
.wrap_page div {
  transition: all 0.3s;
}
.wrap_page div:hover {
  color: #ff6716;
}
.hotGame_pic {
  width: 119px;
  line-height: 38px;
}
.hotGame_pic1 {
  width: 117px;
}
.recor_pic {
  display: block;
  width: 190px;
  margin: 20px auto;
}
.topAcc_pic {
  width: 120px;
  margin-left: 20px;
  margin-top: 23px;
  margin-right: 50px;
}
.dingjiOne,
.dingjiTwo,
.dingjiFour,
.dingjiThree {
  // padding-left: 28px;
}
// .h_swi_wrap_itm.active,
// .h_swi_wrap_itm:hover {
//   color: #ff6716;
// }
// .h_swi_wrap_itm.active:after,
// .h_swi_wrap_itm:hover:after {
//   background: #ff6716;
// }
// .h_swi_wrap_itm.dingjiOne.active,
// .h_swi_wrap_itm.dingjiOne:hover {
// background: url(../../../static/home/<USER>
// background-size: 18px auto;
//   color: #ff6716;
// }
// .h_swi_wrap_itm.dingjiTwo.active,

// .h_swi_wrap_itm:hover,
// .h_swi_wrap_itm.dingjiOne:hover,
// .h_swi_wrap_itm.dingjiThree:hover
//   .h_swi_wrap_itm.dingjiFour:hover
//   .h_swi_wrap_itm.dingjiTwo:hover {
//   border: 0px solid #fff;
//   background: url(../../../static/imgs/header_nav_bk.svg) !important;
//   background-size: 100% 100% !important;
//   background-repeat: no-repeat !important;
//   color: #fff;
// }
.h_swi_wrapActive {
  background: url(../../../static/imgs/header_nav_bk.svg) !important;
  // background: url(../../../static/imgs/playDetail_qq_btn_bk.png) !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  color: #fff !important;
}
// .h_swi_wrap_itm.dingjiThree.active,
// .h_swi_wrap_itm.dingjiThree:hover {
// background: url(../../../static/home/<USER>
// background-size: 18px auto;
//   color: #ff6716;
// }
// .h_swi_wrap_itm.dingjiFour.active,
// .h_swi_wrap_itm.dingjiFour:hover {
// background: url(../../../static/home/<USER>
// background-size: 18px auto;
//   color: #ff6716;
// }
.tops_iconHome {
  position: fixed;
  left: 30px;
  top: 50%;
  width: 100px;
  height: auto;
  margin-top: -50px;
  z-index: 100;
  cursor: pointer;
}
.tops_iconHome > img {
  display: block;
  width: 100%;
}
.wrapBox_new {
  background: #f5e7d8;
  box-sizing: border-box;
  padding: 10px;
  border-radius: 10px;
  margin-top: 20px;
}
.hGround_item {
  background-color: #fff;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #999999;
  padding: 12px 0;
  width: 48%;
  text-align: center;
  flex-shrink: 0;
  cursor: pointer;
}
.hGround_pic {
  width: 60px;
  height: 60px;
}
.hGround_title {
  font-size: 16px;
  color: #333333;
  padding: 10px 0 8px;
}
.game_wrap_new {
  float: left;
  position: absolute;
  top: -0.4px;
  right: -3.2px;
  z-index: 100;
  padding-right: 0.2rem;
  img {
    width: 47px;
    height: 22px;
    object-fit: cover;
  }
}
.home_ding_wrap {
  width: 100%;
  background: #fff;
  border-radius: 10px;
  margin-top: 12px;
  box-sizing: border-box;
  padding: 12px;
  padding-bottom: 0;
  overflow: hidden;
}
.hotProductList {
  width: 256.243px;
  height: 466.29px;
  background: #fff;
  flex: 1;
  margin-left: 34.28px;
  font-size: 12px;
  border-radius: 12px;
  align-self: start;
  .warp {
    height: 444px;
    width: 256.243px;
    // margin: 44px auto 0;
    overflow: hidden;
    .hotProductTextBox {
      width: 158px;
      height: 57px;
      margin-left: 7px;
      .homeTextOneLine {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-family: PingFang SC;
        font-size: 12px;
        letter-spacing: 0.24px;
        font-weight: 500;
        height: 16px;
        line-height: 16px;
        color: #1b1b1b;
      }
      .hotProductPrice {
        color: #ff720c;
        font-weight: 600;
        font-family: Inter;
        letter-spacing: -0.4px;
        font-size: 16px;
        line-height: 103%;
      }
      .hotGameTime {
        color: #969696;
        font-weight: 400;
        font-size: 12px;
        font-family: 'PingFang SC';
        line-height: 16px;
        letter-spacing: 0.24px;
      }
    }

    .item {
      cursor: pointer;
      margin-bottom: 10px;
      position: relative;
      margin-top: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
      .soledpic {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 44px;
        height: 20px;
      }
      .pic {
        height: 57px;
        width: 87px;
        border-radius: 10px;
      }
    }
  }
}
.home_ding_leftIcon {
  width: 176px;
  // height: 41.93px;
  // object-fit: cover;
  float: left;
  height: 40px;
  // margin-top: 40px;
  margin-bottom: 20px;
  // margin-left: 20px;
}
.home_ding_box {
  width: 1144px;
  box-sizing: border-box;
  padding: 0 0 0 12px;
  cursor: pointer;
  float: left;
}
.seamless-warp2 {
  overflow: hidden;
  width: 1130px;
  float: right;
  padding-bottom: 15px;
}
@keyframes marquee {
  0% {
    transform: translateX(100%);
  } /* 初始位置在右侧外边界处 */
  100% {
    transform: translateX(-100%);
  } /* 结束位置在左侧外边界处 */
}
.scrollHorinal {
  display: flex;
  flex-wrap: nowrap;
  white-space: nowrap;
  padding-bottom: 12px;
  overflow-x: scroll;
}
.scrollHorinal::-webkit-scrollbar {
  height: 3px;
}
.scrollHorinal::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}
.accountDing_item {
  width: 212px;
  float: left;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-left: 10px;
  height: 175px;
}
.accountDing_item:first-child {
  margin-left: 0;
}
.accountDing_item_pic {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
}
/*.accountDing_item:hover{
    transform: translateY(-10px);
}*/
.pic_type {
  position: absolute;
  z-index: 4;
  left: 0;
  top: 0;
  height: 24px;
}
.soled_pic {
  position: absolute;
  z-index: 6;
  width: 100%;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
.accountDing_item_price {
  padding-top: 6px;
}
.accountDing_item_price span {
  color: #fe5a1e;
  font-weight: 600;
}
.border_bottom_style {
  border: 1px solid transparent;
  background-clip: padding-box, border-box;

  background-origin: padding-box, border-box;

  background-image: linear-gradient(to right, #fff, #fff),
    linear-gradient(
      180deg,
      rgba(255, 255, 255, 1) 40%,
      rgba(255, 225, 195, 1) 100%
    );
  box-shadow: 10px 10px 50px 0px rgba(255, 107, 0, 0.05);
}
.box_show_color {
  box-shadow: 10px 10px 50px -5px rgba(255, 107, 0, 0.05);
}
