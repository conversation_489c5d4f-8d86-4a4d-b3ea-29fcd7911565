<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <!-- style="background: linear-gradient(180deg, #fdf5ed 36.61%, #fff 53.46%)" -->
    <!-- style="background: linear-gradient(180deg, #fdf5ed 60.61%, #fff 100.46%)" -->

    <div class="homeBg_con">
      <headerKk />
      <div class="paTopCom">
        <div class="safe_width spaceBetween">
          <div>
            <el-carousel
              height="360px"
              class="swiper_home"
              @change="changeSwiper"
            >
              <el-carousel-item
                v-for="(item, index) in bannerList"
                :key="index"
              >
                <el-image
                  :src="item.pic"
                  style="width: 100%; height: 100%; cursor: pointer"
                  fit="cover"
                  @click="pageGo(item)"
                ></el-image>
              </el-carousel-item>
            </el-carousel>
            <div class="spaceAlignCenter newsell border_bottom_style">
              <img
                class="newSell_pic"
                fit="cover"
                src="../../../static/new.svg"
              />
              <vue-seamless-scroll
                :class-option="optionLeft2"
                :data="newSellProductList"
                class="seaml"
              >
                <div
                  v-for="(item, index) in newSellProductList"
                  :key="index"
                  class="spaceBetween seaml_item"
                >
                  <!-- <div class="spaceStart">
                    <div class="cate">
                      {{ item.productCategoryName }}
                    </div>
                    <div class="textOneLine name">{{ item.name }}</div>
                  </div> -->
                  <div class="textOneLine name">{{ item.name }}</div>
                  <div class="spaceEnd">
                    <div class="text_price_title">成交价：</div>
                    <div class="price text-gradient-color-style">
                      ¥{{ item.price }}
                    </div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
          <div class="homeSwiper_right">
            <div class="top border_bottom_style box_show_color">
              <img
                class="logo"
                src="../../../static/imgs/logo_icon.svg"
                fit="cover"
              />
              <div class="title">
                <!-- 保障您的游戏帐号交易安全 -->
                <img src="../../../static/imgs/home_account_text.svg" alt="" />
              </div>
              <div class="spaceAlignCenter count_box">
                <div class="left">
                  <div class="subtitle">当前在售号</div>
                  <div class="count">{{ totalSale.total_show || 0 }}</div>

                  <div class="subbtn_box">
                    <a href="/gameList" class="subbtn btnActiveNotBorder"
                      >我要买</a
                    >
                  </div>
                </div>
                <div class="right">
                  <div class="subtitle">近期成交量</div>
                  <div class="count">{{ totalSale.total_recent || 0 }}</div>

                  <div class="subbtn_box">
                    <a href="/allSell" class="subbtn">
                      <!-- 我要卖 border-gradient-color-style-->
                      <span>我要卖</span>
                      <!-- <img
                        style="width: 97px; height: 32px"
                        src="../../../static/imgs/home_sell.svg"
                        alt=""
                      /> -->
                    </a>
                  </div>
                </div>
              </div>
              <!-- <div class="spaceBetween btn_box"></div> -->
            </div>
            <div class="bottom border_bottom_style box_show_color">
              <div class="title">
                <!-- 玩家安全交易记录 -->
                <img src="../../../static/imgs/home_wanjiaanquan.svg" alt="" />
              </div>
              <CountTo
                :start-val="startVal"
                :end-val="totalSale.total_over"
                :duration="duration"
                class="countNum text-gradient-color-style"
              />
              <!-- <div class="btn_box spaceBetween">
                <div
                  :class="searchIndex === 0 ? 'active' : ''"
                  class="btn"
                  @click="changeSearch(0)"
                >
                  客服验证
                </div>
                <div
                  :class="searchIndex === 1 ? 'active' : ''"
                  class="btn"
                  @click="changeSearch(1)"
                >
                  黑号查询
                </div>
                <div
                  :class="searchIndex === 2 ? 'active' : ''"
                  class="btn"
                  @click="changeSearch(2)"
                >
                  账号查询
                </div>
              </div> -->
              <div class="myTabLeft"></div>
              <myTab
                :tabs="[
                  { title: '安全验证' },
                  { title: '黑号查询' },
                  { title: '账号查询' },
                ]"
                :class="`tab${searchIndex}`"
                border-class="tab-border"
                @click="changeSearch"
              >
                <template>
                  <div
                    class="search_input_box border-gradient-img-medium-style"
                  >
                    <el-input
                      :placeholder="searchPlace"
                      v-model="searchValue"
                      class="search_ipt"
                      @keyup.enter.native="doSearch"
                    >
                      <i
                        slot="suffix"
                        class="el-input__icon el-icon-search"
                        @click="doSearch"
                      ></i>
                    </el-input>
                  </div>
                </template>
              </myTab>
            </div>
          </div>
        </div>
      </div>
      <!-- filter: drop-shadow(10px 10px 50px rgba(255, 107, 0, 0.05)); -->
      <div class="safe_width hotGamesBox">
        <!-- hot games -->
        <div class="spaceBetweenNoAi border_bottom_style hotGamesContent">
          <div class="hotGames_left">
            <div class="hotGames_container">
              <div class="spaceBetween hotGames_head">
                <!-- <p
                  style="
                    font-family: YouSheBiaoTiHei;
                    font-size: 32px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 38px;
                  "
                >
                  热门游戏
                </p> -->
                <img class="hotGame_pic" src="../../../static/hotGame.svg" />
                <router-link class="text_size_16" to="/gameList"
                  >查看全部</router-link
                >
              </div>
              <div class="page_comStyle gameAll_homeWrap spaceStart">
                <div
                  v-for="item in hotGame"
                  :key="item.id"
                  class="gameH_wrap"
                  @click="playPage(item.id)"
                >
                  <!-- 新游戏角标 -->
                  <div v-if="item.productUnit === 'NEW'" class="game_wrap_new">
                    <img src="../../../static/imgs/newIcon.svg" />
                  </div>
                  <!-- 热门游戏角标 -->
                  <!-- <div v-if="item.productUnit === 'HOT'" class="game_wrap_new">
                    <img
                      src="https://images2.kkzhw.com/mall/images/20240618/hotC.jpg"
                    />
                  </div> -->
                  <div class="game_wrap_pic">
                    <el-image
                      :src="item.icon"
                      style="width: 100%; height: 100%"
                      fit="cover"
                    ></el-image>
                  </div>
                  <div>{{ item.name }}</div>
                </div>
              </div>
            </div>
            <div class="spaceAlignCenter findback_box">
              <div class="findback">
                <div class="spaceBetween findback_tit">
                  <!-- <p
                    style="
                      font-family: YouSheBiaoTiHei;
                      font-size: 32px;
                      font-style: normal;
                      font-weight: 400;
                      line-height: normal;
                    "
                  >
                    找回案例
                  </p> -->
                  <img
                    class="findhead"
                    fit="contain"
                    src="../../../static/zhal.svg"
                  />
                  <a
                    target="_blank"
                    href="https://space.bilibili.com/3546391202761172"
                    class="text_size_16"
                    >查看全部</a
                  >
                </div>
                <div class="spaceBetween">
                  <div class="finditem">
                    <img
                      class="finditem_pic"
                      fit="cover"
                      src="../../../static/imgs/newZh1.jpg"
                      @click="
                        openurl(
                          'https://www.bilibili.com/video/BV1oVkcY3EX6/?spm_id_from=333.999.0.0'
                        )
                      "
                    />
                  </div>
                  <div class="finditem">
                    <el-image
                      class="finditem_pic"
                      fit="cover"
                      src="https://images2.kkzhw.com/mall/images/20240708/eeizca_1720427074458.webp"
                      @click="
                        openurl(
                          'https://www.bilibili.com/video/BV17E421P731/?spm_id_from=333.337.search-card.all.click&vd_source=674d8125c63feb7dba88a32756ae51c2'
                        )
                      "
                    ></el-image>
                  </div>
                  <div class="finditem">
                    <el-image
                      class="finditem_pic"
                      fit="cover"
                      src="../../../static/zh0.webp"
                      @click="
                        openurl(
                          'https://www.bilibili.com/video/BV19x4y1J72q/?spm_id_from=333.337.search-card.all.click&vd_source=aa12c5eea5b4fd1afe91315f624a761e'
                        )
                      "
                    ></el-image>
                  </div>
                </div>
              </div>
              <div
                style="
                  background: #ececec;
                  width: 0.834px;
                  height: 143px;
                  margin: 0px 33.36px;
                "
              ></div>
              <!-- 合作主播 -->
              <div class="video_container">
                <div class="spaceBetween hotGames_head1">
                  <!-- <p
                    style="
                      font-family: YouSheBiaoTiHei;
                      font-size: 32px;
                      font-style: normal;
                      font-weight: 400;
                      line-height: 38px;
                    "
                  >
                    合作达人
                  </p> -->
                  <img class="hotGame_pic1" src="../../../static/video.svg" />
                  <router-link class="text_size_16" to="/videoList"
                    >查看全部</router-link
                  >
                </div>
                <div class="video_container_box">
                  <el-carousel
                    :interval="5000"
                    height="86px"
                    indicator-position="none"
                    class="swiper_video"
                    arrow="always"
                  >
                    <el-carousel-item
                      v-for="(item, index) in anchorLists"
                      :key="index"
                      class="swiper_video1"
                    >
                      <el-image
                        :src="item.pic"
                        style="width: 100%; height: 100%; cursor: pointer"
                        fit="cover"
                        @click="videoGo(item)"
                      ></el-image>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </div>
            </div>
          </div>
          <!-- <div style="width: 2px; background: #ececec; height: 664px"></div> -->
          <!-- 顶级成交 -->
          <div class="hotProductList" @click="palyPageDetailSeam($event)">
            <!-- <p
              style="
                font-family: YouSheBiaoTiHei;
                font-size: 32px;
                font-style: normal;
                font-weight: 400;
                line-height: 38px;
              "
            >
              顶级账号成交
            </p> -->
            <img
              class="home_ding_leftIcon"
              src="../../../static/homeDing.svg"
            />
            <vue-seamless-scroll
              :data="hotProductList"
              :class-option="optionLeft"
              class="warp"
            >
              <div
                v-for="(item, index) in hotProductList"
                :key="index"
                :data-id="item.productSn"
                class="item"
              >
                <div class="spaceAlignCenter">
                  <div style="position: relative">
                    <img :src="item.pic" class="pic" />
                    <img
                      class="soledpic"
                      src="../../../static/imgs/goods_soled.svg"
                    />
                  </div>
                  <div class="hotProductTextBox">
                    <div class="homeTextOneLine" style="text-indent: -7px">
                      【{{ item.productSn }}】
                    </div>
                    <div
                      class="homeTextOneLine"
                      style="
                        font-size: 14px;
                        font-family: PingFang SC;
                        color: #1b1b1b;
                        line-height: 16px;
                        font-weight: 400;
                        letter-spacing: 0.56px;
                      "
                    >
                      {{ item.productCategoryName }}
                    </div>
                    <div class="spaceBetween" style="margin-top: 8.517px">
                      <div class="hotGameTime">
                        {{ momentTime(item.updateTime) }}
                        <!-- 时间 -->
                      </div>
                      <div class="hotProductPrice">¥{{ item.price }}</div>
                      <!-- <div style="font-size:12px">{{ item.updateTime | formatTime }}</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </vue-seamless-scroll>
          </div>
        </div>

        <!-- game list -->
        <div class="accounts_container border_bottom_style">
          <div class="topGmae_header spaceStart">
            <!-- <div class="spaceStart">
              <img class="topAcc_pic" src="../../../static/top.png" />
            </div> -->

            <div class="spaceBetween" style="width: 100%">
              <div class="spaceStart h_swi_wrap" style="width: 740px">
                <div
                  v-for="(item, index) in goodsType"
                  :key="index"
                  :class="item.checked ? `h_swi_wrapActive` : `${item.clazz}`"
                  class="h_swi_wrap_itm spaceCenter"
                  @click="chooseAccount(item)"
                >
                  {{ item.title }}
                </div>
              </div>
              <!-- <div
                v-if="!isGuess"
                class="spaceEnd h_swi_wrap categamebox"
                style="width: 450px"
              >
                <div
                  v-for="(item, index) in subGame"
                  :key="index"
                  :class="item.checked ? 'active' : ''"
                  class="h_swi_wrap_itm"
                  @click="chooseSubGame(item, index)"
                >
                  {{ item.name }}
                </div>
              </div> -->
            </div>
          </div>
          <div class="spaceStart" style="flex-wrap: wrap">
            <div
              v-for="(item, index) in accountShopList"
              :key="index"
              class="topGame_item"
              @click="palyPageDetail(item)"
            >
              <div class="topGame_itemPic">
                <el-image
                  :src="item.pic"
                  style="width: 100%; height: 100%"
                  fit="cover"
                ></el-image>
                <div @mouseover="palyDivImg(item)" @click.stop="showImagePriview(item)"
                          class="home_goodsItem_pic_img_box">预览</div>
              </div>
              <div class="topGame_itemBody">
                <div class="topGame_name text_linThree fontFamilg">
                  {{ tedianFilter(item.subTitle, item) }}
                </div>
                <div class="spaceBetween topGame_price">
                  <div class="spaceBetween">
                    <div class="spaceStart">
                      <!-- <img
                        class="icon_typeS"
                        src="../../../static/home/<USER>"
                      /> -->
                      <IconFont
                        :size="14"
                        icon="hot"
                        style="margin-right: 5px"
                        color="#FF720C"
                      />
                      <div>{{ item.gameSysinfoReadcount || 0 }}</div>
                      &nbsp;&nbsp;
                    </div>
                    <div class="spaceStart">
                      <!-- <img
                        class="icon_typeS"
                        src="../../../static/home/<USER>"
                      /> -->
                      <IconFont
                        :size="13"
                        style="margin-right: 5px"
                        icon="focus"
                        color="#FF720C"
                      />
                      <div>{{ item.gameSysinfoCollectcount || 0 }}</div>
                    </div>
                  </div>
                  <div class="topGame_priceNum">¥ {{ item.price }}</div>
                </div>
                <div class="spaceBetween">
                  <div class="spaceStart left_box">
                    <div class="gamename">
                      {{ item.productCategoryName
                      }}<span class="gamenameLeft">|</span>
                    </div>
                    <div>{{ item.gameAccountQufu }}</div>
                  </div>
                  <!-- <div>{{ item.updateTime | formatTime }}</div> -->
                </div>
              </div>
              <!-- <div class="topGame_content">
                <div class="topGame_contName">商品详情</div>
                <div class="topTips_tit_content">
                  <span>{{ item.productSn }}</span>
                  <span>{{ item.gameAccountQufu }}</span>
                </div>
                <div class="topGame_contText fontFamilg">
                  {{ tedianFilter(item.subTitle, item) }}
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <borderDialog
      :value="searchValue"
      :search-index="searchIndex"
      :search-code="searchCode"
      :visible="visible"
      type="home"
      @dialogClone="dialogClone"
    />
    <navigation-fixed @goPageTop="backTopPage" />
    <swperImagePriview :tableData="tableData" :productSn="productSn" v-if="showViewer" :z-index="10000" :initial-index="imgViewer" :on-close="closeViewer"
    :url-list="arrDtPicForShow" :tableDataFlag="true" :product="productObj" :gameSysinfoReadcount="gameSysinfoReadcount" :gameSysinfoCollectcount="gameSysinfoCollectcount" :price="price"/>
    <!-- <div v-if="dialogTitVisible" @click="dialogTitClone" class="pracockBox">
     <div class="box">
      <i class="el-icon-close parcockClose"></i>
      <img  style="width: 800px;border-radius: 20px;margin-top: 10px;" src="../../../static/imgs/peacock.jpg" alt="">
     </div>
    </div> -->
    
    
  </div>
</template>

<script>
import moment from 'moment';
import SwiperItem from '@/components/swiper.vue';
import swperImagePriview from '@/components/imagePriview.vue'
import vueSeamlessScroll from 'vue-seamless-scroll';
import dialogTit from '../../components/borderDialog/index2.vue';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import myTab from '@/components/myTab/index';
import CountTo from 'vue-count-to';
import {
  kfCheck,
  checkBU,
  checkSK,
  getMemberHisKFList,
  m2kfTalk,
} from '@/api/kf';
import borderDialog from '@/components/borderDialog/index';
// import { saleTotalApi } from '@/api/index';
import defaultImage from '../../../static/imgs/sl_img.png';
import {
  getHomeContentAll,
  getSubjectProductList,
  newProduct,
  getZbList,
  getZbDetail,
  getGuess,
  detectProduct,
  topProduct,
} from '@/api/index2';
import { searchProductList2 } from '@/api/search.js';
import { getDetailByCode } from '@/api/playDetail';
import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import util from '@/utils/index';
const CLAZZMAP = {
  '顶级账号': 'dingjiFour',
  '实时上新': 'dingjiTwo',
  '捡漏专区': 'dingjiThree',
};

export default {
  name: 'Home',
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    CountTo,
    vueSeamlessScroll,
    SwiperItem,
    myTab,
    borderDialog,
    swperImagePriview,
    dialogTit
  },
  data() {
    return {
      dialogTitVisible:false,
      visible: false,
      isGuess: false,
      classOption: {
        step: 0.2,
      },
      searchCode: '',
      hotProductList: [],
      newSellProductList: [],
      subGame: [],
      searchIndex: 0,
      searchPlace: '请输入客服微信号/电话',
      searchValue: '',
      bannerList: '',
      hotGame: '',
      topDealList: '',
      accountShopList: '',
      goodsType: '',
      chooseTypeId: '',
      anchorLists: [],
      startVal: 0,
      background: '',
      listLoading: false,
      endVal: 187200,
      duration: 3000,
      placerName: '客服微信号/电话', // 查询-输入框默认提示
      sIndex: 0, // 轮播右侧数据
      hIndex: 1, // 顶级账号
      typeListDate: {},
      hotHelpListDate: [], // 常见问题
      // flag_id: 3, // 默认游戏id
      checkIndex: 0, // 查询下标
      keywords: '', // 账号查询内容
      totalSale: {
        total_over: 0,
        total_recent: 0,
        total_show: 0,
      },
      dingDate: [],
      tableData: [],
      playListTimer: null,
      showViewer: false,
      arrDtPicForShow: [],
      imgViewer: 0,
      productSn:'',
      gameSysinfoReadcount:'',
      gameSysinfoCollectcount:'',
      price:'',
      productObj:{},
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    optionLeft() {
      return {
        direction: 1,
        limitMoveNum: 2,
        step: 0.3, // 数值越大速度滚动越快
      };
    },
    optionLeft2() {
      return {
        singleHeight: 51,
        waitTime: 5000,
      };
    },
    optionSwitch1() {
      return {
        navigation: true,
        autoPlay: false,
        switchSingleStep: 140,
      };
    },
    leftShowWith() {
      let num = this.dingDate.length * 223 - 12;
      return num + 'px';
    },
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search);
    const paramValue = urlParams.get('from');
    const oprtFrom=localStorage.getItem('oprtFrom')
    if(paramValue&&!oprtFrom){
      localStorage.setItem('oprtFrom',paramValue)
    }
    if(localStorage.getItem('isPeacock')=='true'){
      this.dialogTitVisible=true
    }
   
    if (isLogin()) {
      this.$store.dispatch('getUserInfoStore');
      // todo
      //   getAccountTxApi().then((response) => {
      //     if (response.code == 200) {
      //       this.txImcode = response.data.imCode;
      //     }
      //   });
    }
    this.getHomeContent();
    this.getZbList();
  },
  methods: {
    dialogTitClone(){
      
      localStorage.setItem('isPeacock',false)
      this.dialogTitVisible=false
    },
    mergeOptions(productAttributeList, productAttributeValueList) {
      productAttributeList.sort((a, b) => {
        return a.type - b.type;
      });
      productAttributeList.sort((a, b) => {
        return b.sort - a.sort;
      });
      let tempList = [];
      productAttributeList.forEach((ele) => {
        if (ele.name == '营地ID') {
          const findIt = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          this.wzryId = findIt && findIt.value;
        }
        if (ele.type === 1 || ele.type === 2) {
          const findV = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          if (findV && findV.value) {
            tempList.push({
              name:
                ele.selectType == 2
                  ? `【${ele.name}】${this.formatValue(findV.value)}`
                  : ele.name,
              label: ele.name,
              value: this.formatValue(findV.value),
              sort: ele.sort,
              selectType: ele.selectType,
            });
          }
        }
      });
      return tempList;
    },
    formatValue(value) {
      return value
        .replace(/[,]/g, '，')
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },
    closeViewer() {
      this.showViewer = false;
    },
    palyDivImg(item) {
      // this.palyTitleMouseenter2(item)
    },
    showImagePriview(item) {
      getDetailByCode({ productSn: item.productSn }).then((res) => {


        const product = res.data.product
        let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : [];
        let arr = []
        if (product.albumPics) {
          arr = product.albumPics.split(',').filter(item => item.trim() !== '');
        } else {
          albumPicsJson = JSON.parse(albumPicsJson)
          albumPicsJson.forEach(item => {
            if (arr.length < 10&&item.url) {
              arr.push(item.url)
            }
          })
        }
        this.productObj=item
        this.productSn=item.productSn
        this.gameSysinfoReadcount=item.gameSysinfoReadcount
        this.gameSysinfoCollectcount=item.gameSysinfoCollectcount
        this.price=item.price
        let oldArr = this.mergeOptions(
          res.data.productAttributeList,
          res.data.productAttributeValueList
        );
        let newArr = [];
        let newArr2 = [];
        oldArr.forEach((item) => {
          if (item.selectType == 2) {
            newArr2.push(item);
          } else {
            newArr.push(item);
          }
        });

        newArr.sort((a, b) => {
          return b.type - a.type;
        });
        newArr2.sort((a, b) => {
          return b.sort - a.sort;
        });
        if (res.data.product.description) {
          newArr2.push({
            name: `【卖家说】${res.data.product.description}`,
            value: '',
            sort: 11,
            selectType: 2,
          });
        }
        let allArr = newArr.concat(newArr2);
        // console.log(JSON.stringify(allArr),8989)
        // console.log(searchArr,7777)
        // this.tableData = newArr.concat(newArr2);
        this.tableData = allArr
        console.log(this.tableData)
        this.playTableLoading = false
        arr.unshift(defaultImage);
        this.arrDtPicForShow = arr
        this.showViewer = true
      })
      // this.arrDtPicForShow = arr
      // this.showViewer = true
    },
    palyTitleMouseenter2(e) {
        getDetailByCode({ productSn: e.productSn }).then((res) => {
          this.productSn=e.productSn
          this.gameSysinfoReadcount=e.gameSysinfoReadcount
          this.gameSysinfoCollectcount=e.gameSysinfoCollectcount
          this.price=e.price
          let oldArr = this.mergeOptions(
            res.data.productAttributeList,
            res.data.productAttributeValueList
          );
          let newArr = [];
          let newArr2 = [];
          oldArr.forEach((item) => {
            if (item.selectType == 2) {
              newArr2.push(item);
            } else {
              newArr.push(item);
            }
          });

          newArr.sort((a, b) => {
            return b.type - a.type;
          });
          newArr2.sort((a, b) => {
            return b.sort - a.sort;
          });
          if (res.data.product.description) {
            newArr2.push({
              name: `【卖家说】${res.data.product.description}`,
              value: '',
              sort: 11,
              selectType: 2,
            });
          }
          this.tableData = newArr.concat(newArr2);
          this.playTableLoading=false
        });
    },
    dialogClone() {
      this.visible = false;
    },
    momentTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    openurl(url) {
      window.open(url);
    },
    doSearch() {
      if (this.searchIndex === 1) {
        checkBU({
          value: this.searchValue,
        }).then((res) => {
          this.visible = true;
          if (res.data) {
            this.searchCode = 'error';
            // this.$message.error('该账号查询的确是黑号!');
          } else {
            this.searchCode = 'success';
            // this.$message.success('经检测，此号码未受过拉黑处理!');
          }
        });
      } else if (this.searchIndex === 2) {
        checkSK({
          value: this.searchValue,
        }).then((res) => {
          this.visible = true;
          if (res.data) {
            // this.$message.success('此收款账户是官方收款账户~');
            this.searchCode = 'success';
          } else {
            // this.$message.error('此收款账户为非官方收款账户~');
            this.searchCode = 'error';
          }
        });
      } else {
        kfCheck({
          value: this.searchValue,
        }).then((res) => {
          this.visible = true;
          if (res.code === 200) {
            if (res.data) {
              // this.$message.success('当前账号为看看账号网官方客服!');
              this.searchCode = 'success';
            } else {
              // this.$message.error('当前账号非看看账号网官方客服!');
              this.searchCode = 'error';
            }
          }
        });
      }
    },
    changeSearch(index) {
      this.searchIndex = index;
      if (this.searchIndex === 1) {
        this.searchPlace = '请输入手机号';
      } else if (this.searchIndex === 2) {
        this.searchPlace = '请输入银行卡号';
      } else {
        this.searchPlace = '请输入客服微信号/电话';
      }
    },
    getZbList() {
      getZbList({
        // game: '逆水寒手游',
        type: '合作主播',
      }).then((res) => {
        if (res.code == 200) {
          this.anchorLists = res.data;
          this.anchorLists.sort((a, b) => {
            return  b.sort - a.sort
          });
        }
      });
    },
    generateUUID() {
      return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16)
      })
    },
    getHomeContent() {
      const cashedFlag=util.cachedFunFlag('contentData')
      if(cashedFlag){
        this.homeContentDataDealwith(cashedFlag.data)
      }else{
        Promise.all([getHomeContentAll({})]).then((values) => {
          const response = values[0];
          util.cachedFunFlag('contentData',response)
          this.homeContentDataDealwith(response)
        });
      }
    },
    homeContentDataDealwith (response){
      if (response.code == 200) {
        const kkuuid=localStorage.getItem('kkuuid')
        if(!kkuuid){
          const uuid = this.generateUUID();
          localStorage.setItem('kkuuid',uuid)
        }
        const { data } = response;
        const {
          advertiseList = [],
          hotProductCategoryList = [],
          subjectList = [],
          statics,
          topDealProductList = [],
          newSellProductList = [],
        } = data;
        this.hotProductList = topDealProductList;
        this.bannerList = advertiseList;
        // 如果16个游戏 就把最后一个替换为更多游戏
        if (hotProductCategoryList && hotProductCategoryList.length >= 16) {
          const newArr = hotProductCategoryList.slice(0, 15);
          newArr.push({
            id: -1,
            name: '更多游戏',
            url: '/gameList',
            icon: require('@static/imgs/home_more_gams.png'),
            // '../../../static/imgs/home_more_gams.png',
          });
          this.hotGame = newArr;
        } else {
          this.hotGame = hotProductCategoryList;
        }
        // this.hotGame = hotProductCategoryList;

        this.newSellProductList = newSellProductList;
        this.goodsType = subjectList;

        this.goodsType.forEach((ele) => {
          ele.clazz = CLAZZMAP[ele.title];
        });

        this.goodsType[0].checked = true;
        this.chooseTypeId = this.goodsType[0].id;
        this.totalSale = {
          total_over: parseInt(statics.totalOrderCount, 10),
          total_show: parseInt(statics.goodsCount, 10),
          total_recent: parseInt(statics.lastTimeOrderCount, 10),
        };
        this.getGuess();
      }
    },
    getGuess() {
      // const guessProductId = localStorage.getItem('guessProductId') || 0;
      let data = {};
      if (this.userInfo.id) {
        data.memberId = this.userInfo.id;
      }
      getGuess(data)
        .then((res) => {
          if (res.code === 200) {
            if (res.data && res.data.list && res.data.list.length) {
              let item = {
                type: 'guess',
                title: '猜你喜欢',
                list: res.data.list,
                clazz: 'dingjiOne',
              };
              this.goodsType.unshift(item);
              this.goodsType.forEach((ele, index) => {
                this.$set(ele, 'checked', false);
              });
              this.$set(item, 'checked', true);
              this.isGuess = true;
              this.accountShopList = res.data.list || [];
              // this.chooseAccount(item);
            }
          }
        })
        .finally(() => {
          if (!this.accountShopList.length) {
            // 获取顶级账号
            this.getShopListFun4();
          }
        });
    },
    videoGo(date) {
      const { url } = date;
      if (url.indexOf('http') == 0) {
        window.open(date.url, '_blank');
      } else {
        this.$router.push({
          path: `/gd/${url}`,
        });
      }
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 帮助跳转
    helpGo(id) {
      this.$router.push({
        path: '/helpCenter?id=' + id,
      });
    },
    // getParamsFromUrl(url) {
    //   const params = {};
    //   const urlParts = url.split('?');
    //   if (urlParts.length > 1) {
    //     const paramString = urlParts[1];
    //     const paramPairs = paramString.split('&');
    //     paramPairs.forEach((pair) => {
    //       const [key, value] = pair.split('=');
    //       params[key] = decodeURIComponent(value);
    //     });
    //   }
    //   return params;
    // },
    // 轮播跳转
    pageGo(date) {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
      } else {
        if (date.url && !date.url.includes('kkzhw.com/Im')) {
          window.open(date.url);
        } else {
          util.goImPage(date.url);
        }
      }
    },
    // 轮播背景色变化
    changeSwiper(e) {
      this.bannerList.forEach((v, i) => {
        if (e == i) {
          this.background = v.color;
        }
      });
    },
    // 顶级账号-右侧筛选
    chooseAccount(item) {
      // if( this.hIndex == 0) this.accountThree()
      //   if (this.hIndex == 1) this.accountOne();
      //   if (this.hIndex == 2) this.accountTwo();
      //   if (this.hIndex == 3) this.accountFour();
      this.goodsType.forEach((ele, index) => {
        this.$set(ele, 'checked', false);
      });
      this.$set(item, 'checked', true);
      if (item.type == 'guess') {
        this.isGuess = true;
        let data = {};
        if (this.userInfo.id) {
          data.memberId = this.userInfo.id;
        }
        getGuess(data).then((res) => {
          if (res.code === 200) {
            if (res.data && res.data.list && res.data.list.length) {
              this.accountShopList = res.data.list || [];
            }
          }
        });
      } else if (item.categoryName == '实时上新') {
        this.isGuess = false;
        this.chooseTypeId = item.id;
        this.getShopListFun2();
      } else if (item.categoryName == '捡漏专区') {
        this.isGuess = false;
        this.getShopListFun3();
      } else if (item.categoryName == '顶级账号') {
        this.isGuess = false;
        this.getShopListFun4();
      } else {
        this.isGuess = false;
        this.chooseTypeId = item.id;
        this.getShopListFun();
      }
    },
    getShopListFun4() {
      topProduct().then((res) => {
        if (res.code === 200) {
          const { data } = res;
          this.accountShopList = data.list || [];
        }
      });
    },
    getShopListFun3() {
      detectProduct().then((res) => {
        if (res.code === 200) {
          const { data } = res;
          this.accountShopList = data.list || [];
        }
      });
    },
    // chooseSubGame(item, index) {
    //   this.subGame.forEach((ele, index) => {
    //     this.$set(ele, 'checked', false);
    //   });
    //   this.$set(item, 'checked', true);
    //   this.accountShopList = this.subGame[index].list;
    // },
    getShopListFun() {
      getSubjectProductList({
        subjectId: this.chooseTypeId,
      }).then((res) => {
        if (res.code === 200) {
          const { data } = res;
          // this.subGame = Object.keys(data).map((ele, index) => {
          //   const checked = index === 0;
          //   return {
          //     checked,
          //     name: ele,
          //     list: data[ele],
          //   };
          // });
          // this.accountShopList = this.subGame[0].list;
          this.accountShopList = [];
          Object.keys(data).forEach((key) => {
            this.accountShopList = this.accountShopList.concat(data[key]);
          });
        }
      });
    },
    getShopListFun2() {
      newProduct(75).then((res) => {
        if (res.code === 200) {
          const { data } = res;
          this.accountShopList = data.list || [];
        }
      });
    },
    // 游戏账号列表
    playPage(num) {
      if (num == -1) {
        this.$router.push({
          path: '/gameList',
        });
        return;
      }
      this.$router.push({
        path: '/playList?productCategoryId=' + num,
      });
    },
    palyPageDetailSeam(event) {
      const productSn = this.findParentWithDataName(
        event.target,
        'id',
        event.currentTarget
      );
      if (productSn) {
        let routeUrl = this.$router.resolve({
          path: `/gd/${productSn}`,
        });
        window.open(routeUrl.href, '_blank');
      }
    },
    findParentWithDataName(el, dataName, stopAt) {
      while (el && el !== stopAt && el.parentNode) {
        el = el.parentNode;
        if (el.dataset && el.dataset[dataName]) {
          return el.dataset[dataName]; // 可以选择返回找到的节点或者进行其他处理
        }
      }
      return null;
    },
    // 账号详情
    palyPageDetail(date) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${date.productSn}`,
      });
      window.open(routeUrl.href, '_blank');
    },
    // 我要买
    buyFun() {
      this.$router.push({
        path: '/gameList',
      });
    },
    // 我要卖
    sellerFun() {
      this.$router.push({
        path: '/allSell',
      });
    },
    // 个人中心
    centerGo() {
      this.$router.push({
        path: '/account/center',
      });
    },
    // 退出登录
    loginOutHome() {
      localStorage.removeItem('token');
      localStorage.removeItem('yximtoken');
      location.reload();
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './home.scss';
.imgBox {
  width: 100%;
  img {
    width: 100%;
  }
}
.gamename {
  color: #ffb74a;
}
.gamenameLeft {
  margin: 0 5px;
}
.left_box {
  margin-top: 10.18px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  // line-height: 0%;
  color: #969696;
}
.home_goodsItem_pic_img_box{
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
display: none;
  top: 0px;
  left: 0px;
}
.pracockBox{
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0%;
  left: 0%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 99999999;
  .box{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
  }
  .parcockClose{
    position: absolute;
    top: -20px;
    right: -40px;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
  }
}
</style>
