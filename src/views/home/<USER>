<template>
  <div class="task-canvas-container">
    <canvas
      ref="taskCanvas"
      :width="canvasWidth"
      :height="canvasHeight"
      @click="handleCanvasClick"
      @mousemove="handleMouseMove"
    ></canvas>
  </div>
</template>

<script>
export default {
  name: 'TaskCanvas',
  data() {
    return {
      canvasWidth: 1400,
      canvasHeight: 700,
      ctx: null,
      tasks: [
        {
          id: 1,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: false,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
        {
          id: 2,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: true,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
        {
          id: 3,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: false,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
        {
          id: 4,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: true,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
        {
          id: 5,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: true,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
        {
          id: 6,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: false,
          items: [
            {
              id: 1,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 2,
              name: '《思想政治、思想政治》',
              type: '视频',
              progress: '80%',
            },
            {
              id: 3,
              name: '《理论理解要求与认识》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 4,
              name: '《理论要求与认识》',
              type: '视频',
              progress: '点击播放',
            },
            {
              id: 5,
              name: '《理论认识理解的方法》',
              type: '视频',
              progress: '0%',
            },
            {
              id: 6,
              name: '人学习目标材料要求学习',
              type: '视频',
              progress: '点击播放',
            },
          ],
        },
      ],
      cardWidth: 320,
      cardHeight: 200,
      cardSpacing: 40,
      hoveredCard: null,
      clickableAreas: [],
    };
  },
  mounted() {
    this.initCanvas();
    this.drawTasks();
  },
  methods: {
    initCanvas() {
      const canvas = this.$refs.taskCanvas;
      this.ctx = canvas.getContext('2d');
      this.ctx.font = '14px Arial';
    },

    drawTasks() {
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      this.clickableAreas = [];

      // 绘制连接线
      this.drawConnectingLines();

      // 绘制任务卡片 - 2行3列布局
      this.tasks.forEach((task, index) => {
        const row = Math.floor(index / 3);
        const col = index % 3;
        const x = 50 + col * (this.cardWidth + this.cardSpacing);
        const y = 50 + row * (this.cardHeight + this.cardSpacing);

        this.drawTaskCard(task, x, y, index);
      });
    },

    drawConnectingLines() {
      // 绘制粗的蓝色连接带
      this.ctx.fillStyle = '#7BA7E7';
      this.ctx.strokeStyle = '#7BA7E7';

      // 计算关键点位置
      const firstRowY = 50 + this.cardHeight / 2;
      const secondRowY =
        50 + this.cardHeight + this.cardSpacing + this.cardHeight / 2;

      // 第一行最后一个卡片右侧
      const startX =
        50 + 2 * (this.cardWidth + this.cardSpacing) + this.cardWidth;

      // 第二行卡片位置
      const row2Card1X = 50;
      const row2Card3X = 50 + 2 * (this.cardWidth + this.cardSpacing);
      const endX = row2Card3X + this.cardWidth;

      // 连接带的宽度
      const bandWidth = 40;
      const halfBand = bandWidth / 2;

      // 绘制连接带路径
      this.ctx.beginPath();

      // 从第一行最后一个卡片开始
      this.ctx.moveTo(startX, firstRowY - halfBand);

      // 向右延伸
      const rightExtend = startX + 120;
      this.ctx.lineTo(rightExtend, firstRowY - halfBand);

      // 右上角圆弧
      this.ctx.quadraticCurveTo(
        rightExtend + 60,
        firstRowY - halfBand,
        rightExtend + 60,
        firstRowY + 20
      );

      // 向下延伸
      this.ctx.lineTo(rightExtend + 60, secondRowY - 60);

      // 右下角圆弧
      this.ctx.quadraticCurveTo(
        rightExtend + 60,
        secondRowY - halfBand,
        rightExtend,
        secondRowY - halfBand
      );

      // 向左延伸到第二行
      this.ctx.lineTo(row2Card1X, secondRowY - halfBand);

      // 沿第二行向右
      this.ctx.lineTo(endX + 60, secondRowY - halfBand);

      // 箭头右上角
      this.ctx.lineTo(endX + 80, secondRowY - 20);
      this.ctx.lineTo(endX + 100, secondRowY);
      this.ctx.lineTo(endX + 80, secondRowY + 20);

      // 箭头右下角
      this.ctx.lineTo(endX + 60, secondRowY + halfBand);

      // 沿第二行向左（下边）
      this.ctx.lineTo(row2Card1X, secondRowY + halfBand);

      // 左下角圆弧
      this.ctx.quadraticCurveTo(
        rightExtend + 60,
        secondRowY + halfBand,
        rightExtend + 60,
        secondRowY + 60
      );

      // 向上延伸
      this.ctx.lineTo(rightExtend + 60, firstRowY - 20);

      // 左上角圆弧
      this.ctx.quadraticCurveTo(
        rightExtend + 60,
        firstRowY + halfBand,
        rightExtend,
        firstRowY + halfBand
      );

      // 向左回到起点
      this.ctx.lineTo(startX, firstRowY + halfBand);

      this.ctx.closePath();
      this.ctx.fill();
    },

    drawTaskCard(task, x, y, index) {
      const isHovered = this.hoveredCard === index;

      // 卡片背景 - 白色背景，灰色边框
      this.ctx.fillStyle = isHovered ? '#f8f9fa' : '#ffffff';
      this.ctx.strokeStyle = '#e0e0e0';
      this.ctx.lineWidth = 1;
      this.roundRect(x, y, this.cardWidth, this.cardHeight, 8);
      this.ctx.fill();
      this.ctx.stroke();

      // 绘制蓝色圆点和标题
      this.ctx.fillStyle = '#4A90E2';
      this.ctx.beginPath();
      this.ctx.arc(x + 15, y + 20, 4, 0, 2 * Math.PI);
      this.ctx.fill();

      // 标题文字
      this.ctx.fillStyle = '#333';
      this.ctx.font = 'bold 14px Arial';
      this.ctx.fillText(task.title, x + 30, y + 25);

      // 锁定图标
      if (task.isLocked) {
        this.drawLockIcon(x + this.cardWidth - 25, y + 12);
      }

      // 学习日期
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#666';
      this.ctx.fillText(`学习日期：${task.dateRange}`, x + 15, y + 45);

      // 表格表头背景
      this.ctx.fillStyle = '#f8f9fa';
      this.ctx.fillRect(x + 10, y + 55, this.cardWidth - 20, 20);

      // 表头文字
      this.ctx.fillStyle = '#333';
      this.ctx.font = '11px Arial';
      this.ctx.fillText('序号', x + 15, y + 68);
      this.ctx.fillText('学习目标名称', x + 45, y + 68);
      this.ctx.fillText('学习方式', x + 160, y + 68);
      this.ctx.fillText('完成度', x + 220, y + 68);

      // 绘制表格分割线
      this.ctx.strokeStyle = '#e0e0e0';
      this.ctx.lineWidth = 0.5;
      this.ctx.beginPath();
      this.ctx.moveTo(x + 10, y + 75);
      this.ctx.lineTo(x + this.cardWidth - 10, y + 75);
      this.ctx.stroke();

      // 任务列表
      task.items.forEach((item, itemIndex) => {
        const itemY = y + 90 + itemIndex * 16;

        this.ctx.fillStyle = '#333';
        this.ctx.font = '11px Arial';
        this.ctx.fillText(item.id.toString(), x + 18, itemY);

        // 截断长文本
        const maxNameLength = 10;
        const displayName =
          item.name.length > maxNameLength
            ? item.name.substring(0, maxNameLength) + '...'
            : item.name;
        this.ctx.fillText(displayName, x + 45, itemY);

        this.ctx.fillText(item.type, x + 165, itemY);

        // 进度显示
        if (item.progress === '点击播放') {
          this.ctx.fillStyle = '#4A90E2';
          this.ctx.fillText(item.progress, x + 225, itemY);

          // 记录可点击区域
          this.clickableAreas.push({
            x: x + 225,
            y: itemY - 8,
            width: 50,
            height: 12,
            action: 'play',
            taskId: task.id,
            itemId: item.id,
          });
        } else {
          this.ctx.fillStyle = '#333';
          this.ctx.fillText(item.progress, x + 225, itemY);
        }
      });

      // 重置阴影
      this.ctx.shadowColor = 'transparent';
      this.ctx.shadowBlur = 0;
      this.ctx.shadowOffsetX = 0;
      this.ctx.shadowOffsetY = 0;
    },

    drawLockIcon(x, y) {
      this.ctx.fillStyle = '#999';
      this.ctx.strokeStyle = '#999';
      this.ctx.lineWidth = 1.5;

      // 锁身 - 更小的锁图标
      this.ctx.fillRect(x, y + 6, 12, 8);

      // 锁环
      this.ctx.beginPath();
      this.ctx.arc(x + 6, y + 4, 3, Math.PI, 0, false);
      this.ctx.stroke();

      // 锁孔
      this.ctx.fillStyle = '#fff';
      this.ctx.beginPath();
      this.ctx.arc(x + 6, y + 9, 1, 0, 2 * Math.PI);
      this.ctx.fill();
    },

    roundRect(
      x,
      y,
      width,
      height,
      radius,
      topOnly = false,
      bottomOnly = false
    ) {
      this.ctx.beginPath();

      if (topOnly) {
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height);
        this.ctx.lineTo(x, y + height);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
      } else if (bottomOnly) {
        this.ctx.moveTo(x, y);
        this.ctx.lineTo(x + width, y);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(
          x + width,
          y + height,
          x + width - radius,
          y + height
        );
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y);
      } else {
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(
          x + width,
          y + height,
          x + width - radius,
          y + height
        );
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
      }

      this.ctx.closePath();
    },

    handleCanvasClick(event) {
      const rect = this.$refs.taskCanvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // 检查点击的可点击区域
      for (const area of this.clickableAreas) {
        if (
          x >= area.x &&
          x <= area.x + area.width &&
          y >= area.y &&
          y <= area.y + area.height
        ) {
          this.handleAction(area);
          break;
        }
      }
    },

    handleMouseMove(event) {
      const rect = this.$refs.taskCanvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      let hoveredCard = null;

      // 检查鼠标是否在某个卡片上
      this.tasks.forEach((_, index) => {
        const row = Math.floor(index / 3);
        const col = index % 3;
        const cardX = 50 + col * (this.cardWidth + this.cardSpacing);
        const cardY = 50 + row * (this.cardHeight + this.cardSpacing);

        if (
          x >= cardX &&
          x <= cardX + this.cardWidth &&
          y >= cardY &&
          y <= cardY + this.cardHeight
        ) {
          hoveredCard = index;
        }
      });

      // 检查是否在可点击区域上
      let isOverClickable = false;
      for (const area of this.clickableAreas) {
        if (
          x >= area.x &&
          x <= area.x + area.width &&
          y >= area.y &&
          y <= area.y + area.height
        ) {
          isOverClickable = true;
          break;
        }
      }

      // 设置鼠标样式
      this.$refs.taskCanvas.style.cursor = isOverClickable
        ? 'pointer'
        : 'default';

      if (this.hoveredCard !== hoveredCard) {
        this.hoveredCard = hoveredCard;
        this.drawTasks();
      }
    },

    handleAction(area) {
      if (area.action === 'play') {
        this.$emit('play-item', {
          taskId: area.taskId,
          itemId: area.itemId,
        });
        console.log(`播放任务 ${area.taskId} 的项目 ${area.itemId}`);
      }
    },
  },
};
</script>

<style scoped>
.task-canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
}

canvas {
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
}
</style>
