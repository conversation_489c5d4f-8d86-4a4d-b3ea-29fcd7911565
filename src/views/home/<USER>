<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <headerKk :active-index="index" />

    <div class="safe_width" style="margin-bottom: 80px">
      <el-breadcrumb
        style="padding: 20px 0px"
        separator-class="el-icon-arrow-right"
        class="pdTopBottom"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>合作主播</el-breadcrumb-item>
      </el-breadcrumb>

      <div class="page_cusWrap">
        <div style="margin-bottom: 34.28px">
          <span class="videoList_title">合作达人</span>
        </div>
        <div class="spaceStart" style="flex-wrap: wrap">
          <div
            v-for="(item, index) in anchorLists"
            :key="index"
            class="videoUser_item"
            @click="openVideo(item)"
          >
            <img :src="item.pic" class="videoUser_pic" />
            <div class="videoUser_video">
              <img src="../../../static/icon-play.png" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import { getZbList } from '@/api/index2';

import { anchorListsApi } from '@/api/index';

export default {
  name: 'Home',
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      listLoading: false,
      index: 0,
      anchorLists: [],
    };
  },
  created() {},
  mounted() {
    this.fetchData();
  },
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 初始化
    fetchData() {
      getZbList({
        // game: '逆水寒手游',
        // type: '独立主播',
        type:'合作主播'
      }).then((response) => {
        if (response.code == 200) {
          this.anchorLists = response.data;
          this.anchorLists.sort((a, b) => {
            return b.sort - a.sort;
          });
        }
      });
    },
    openVideo(date) {
      const { url } = date;
      if (url.indexOf('http') == 0) {
        window.open(date.url, '_blank');
      } else {
        this.$router.push({
          path: `/gd/${url}`,
        });
      }
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>
<style type="text/css">
.page_cusWrap {
  width: 100%;
  background: #fff;
  padding: 45px 40px 53px 40px;
  border-radius: 24px;
}
.videoUser_item {
  position: relative;
  width: 274.24px;
  height: 113.981px;
  background: #fff;
  border-radius: 12px;
  flex-shrink: 0;
  overflow: hidden;
  cursor: pointer;
  margin-bottom: 20px;
  margin-right: 7px;
  transition: all 0.3s;
}
.videoUser_item:nth-child(4n) {
  margin-right: 0;
}
.videoUser_pic {
  width: 274.24px;
  height: 113.981px;
  transition: all 0.3s;
}
.videoUser_item:hover .videoUser_pic {
  transform: scale(1.1);
}
.videoUser_video {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  text-align: center;
  opacity: 0;
  transition: all 0.3s;
}
.videoUser_video > img {
  width: 80px;
  margin-top: 20px;
}
.videoUser_item:hover .videoUser_video {
  opacity: 1;
}
.videoList_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px; /* 100% */
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
</style>
