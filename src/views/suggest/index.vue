<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <headerKk />
    <div class="safe_width" style="margin-bottom: 80px">
      <el-breadcrumb
        style="padding: 20px 0px"
        separator-class="el-icon-arrow-right"
        class="pdTopBottom my-bread"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>投诉建议</el-breadcrumb-item>
      </el-breadcrumb>
      <div
        v-if="pageStep == 1"
        class="spaceBetweenNoAi"
        style="align-items: flex-start"
      >
        <div class="page_comStyle left_box_content">
          <div class="left_box_content_title">投诉建议</div>
          <div class="left_box_content_title_tab">
            <!-- <el-button
              :class="tab == '1' ? 'el-button--primary' : ''"
              @click="tab = 1"
            >
              订单投诉
            </el-button> -->
            <div
              :class="tab == '1' ? 'active' : 'notActive'"
              class="text"
              style="margin-left: 40px"
              @click="tab = 1"
            >
              订单投诉
            </div>
            <div class="dividerLine"></div>
            <div
              :class="tab == '2' ? 'active' : 'notActive'"
              class="text"
              @click="tab = 2"
            >
              网站建议
            </div>
            <!-- <el-button
              :class="tab == '2' ? 'el-button--primary' : ''"
              @click="tab = 2"
            >
              网站建议
            </el-button> -->
          </div>
          <div
            class="spaceBetween"
            style="align-items: baseline; padding: 40px 0px 63px 37px"
          >
            <el-form
              v-show="tab == 1"
              ref="ruleForm"
              :model="ruleForm"
              :rules="rules"
              label-width="90px"
              class="ruleForm suggestForm"
            >
              <el-form-item label="投诉订单" prop="orderId">
                <el-select
                  v-model="ruleForm.orderId"
                  class="suggestFormSelect"
                  placeholder="请选择订单"
                >
                  <el-option
                    v-for="item in orderList"
                    :key="item"
                    :label="item"
                    :value="item"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="boxItem" label="反馈内容" prop="desc">
                <el-input
                  v-model="ruleForm.desc"
                  style="width: 567px"
                  type="textarea"
                  rows="5"
                  placeholder="请填写您的反馈与建议"
                ></el-input>
              </el-form-item>
              <el-form-item class="boxItem" label="相关截图">
                <uploadList
                  :url-pic="ruleForm.pics"
                  :need-water="false"
                  name-key="pics"
                  @upSuccsessList="picUpLoadListSuc"
                  @deletPicList="deletPic"
                />
              </el-form-item>
              <el-button
                class="suggestSubmit"
                type="primary"
                @click="submitForm('ruleForm')"
                >提交</el-button
              >
            </el-form>
            <el-form
              v-show="tab == 2"
              ref="ruleForm2"
              :model="ruleForm2"
              label-width="90px"
              class="ruleForm suggestForm"
            >
              <el-form-item class="boxItem" label="反馈内容" prop="desc">
                <div style="width: 600px; flex-wrap: wrap" class="spaceStart">
                  <el-button
                    v-for="(item, index) in suggestList"
                    :key="index"
                    :class="
                      item == ruleForm2.type ? 'suggestListBtnActive' : ''
                    "
                    class="suggestListBtn"
                    style="margin: 0 12px 20px 0"
                    @click="changeType(item)"
                  >
                    {{ item }}
                  </el-button>
                </div>
                <el-input
                  v-model="ruleForm2.desc"
                  rows="5"
                  style="margin: 20px 0"
                  type="textarea"
                  placeholder="请填写您的反馈与建议"
                ></el-input>
              </el-form-item>
              <el-form-item class="boxItem" label="相关截图">
                <uploadList
                  :url-pic="ruleForm2.pics"
                  :need-water="false"
                  name-key="pics"
                  @upSuccsessList="picUpLoadListSuc2"
                  @deletPicList="deletPic2"
                />
              </el-form-item>
              <el-button
                class="suggestSubmit"
                type="primary"
                @click="submitForm2('ruleForm2')"
                >提交</el-button
              >
            </el-form>
          </div>
        </div>
        <div class="borderSuggest" style="width: 326px; border-radius: 24px">
          <div class="spaceAround" style="flex-wrap: wrap">
            <div class="custorm_itemWx">
              <img
                style="
                  width: 240px;
                  height: 240px;
                  margin-top: 20px;
                  border: 2.4px solid #ffb74a;
                "
                src="https://images2.kkzhw.com/mall/images/20240723/ev2r3f_1721728403429.webp"
              />
              <div class="right_sug">
                <div>投诉微信：<span style="color: #ff720c">kkzhwsh</span></div>
                <div>
                  投诉电话：<span style="color: #ff720c">13208028882</span>
                </div>
                <!-- <div>工作时间：09:30-00:30</div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="pageStep == 2" class="pageStep2_box">
        <img
          style="width: 297.405px; height: 76px"
          src="../../../static/imgs/suggest_step2_img.png"
          alt=""
        />
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
  </div>
</template>

<script>
import uploadList from '@/components/uploadList/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';

import { myOrderList, mySellerList } from '@/api/confirmOrder.js';
import navigationFixed from '@/components/navigationFixed/index';
import { reportAdd } from '@/api/kf.js';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    uploadList,
  },
  data() {
    return {
      pageStep: 1,
      suggestList: [
        '体验问题',
        '功能问题',
        'BUG',
        '游戏问题',
        '交易流程',
        '账号资料',
      ],
      tab: '1',
      loading: false,
      tabIndex: 0,
      orderList: [],
      ruleForm: {
        pics: [],
        orderId: '',
        desc: '',
      },
      ruleForm2: {
        pics: [],
        type: '',
        desc: '',
      },
      rules: {
        order: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
        desc: [{ required: true, message: '请输入投诉内容', trigger: 'blur' }],
      },
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      myOrderList({
        status: -1,
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        if (res.code == 200) {
          let list = res.data.list || [];
          list = list.map((ele) => ele.orderSn);
          this.orderList = this.orderList.concat(list);
        }
      });
      mySellerList({
        status: -1,
        pageNum: 1,
        pageSize: 999,
      }).then((res) => {
        if (res.code == 200) {
          let list = res.data.list || [];
          list = list.map((ele) => ele.orderSn);
          this.orderList = this.orderList.concat(list);
        }
      });
    },
    changeType(item) {
      this.ruleForm2.type = item;
    },
    picUpLoadListSuc(url) {
      this.ruleForm.pics.push(url);
    },
    deletPic(index) {
      this.ruleForm.pics.splice(index, 1);
    },
    picUpLoadListSuc2(url) {
      this.ruleForm2.pics.push(url);
    },
    deletPic2(index) {
      this.ruleForm2.pics.splice(index, 1);
    },
    submitForm(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let data = {
            reportType: 6,
            reportObject: this.ruleForm.orderId,
            note: this.ruleForm.desc,
            pics: this.ruleForm.pics.join(','),
          };
          reportAdd(data).then((res) => {
            if (res.code == 200) {
              this.pageStep = 2;
              // this.$message.success('感谢反馈，我们会马上进行处理');
              // this.$router.go(-1);
            }
          });
        }
      });
    },
    submitForm2(name) {
      if (!this.ruleForm2.desc) {
        this.$message.error('请输入您的投诉与建议');
        return;
      }
      this.$refs[name].validate((valid) => {
        if (valid) {
          let typeMap = {
            '体验问题': 0,
            '功能问题': 1,
            'BUG': 2,
            '游戏问题': 3,
            '交易流程': 4,
            '账号资料': 5,
          };
          let data = {
            reportType: typeMap[this.ruleForm2.type] || 0,
            note: this.ruleForm2.desc,
            pics: this.ruleForm2.pics.join(','),
          };
          reportAdd(data).then((res) => {
            if (res.code == 200) {
              this.pageStep = 2;
              // this.$message.success('感谢反馈，我们会马上进行处理');
              // this.$router.go(-1);
            }
          });
        }
      });
    },
  },
};
</script>

<style></style>

<style rel="stylesheet/scss" lang="scss" scoped="">
.page_comStyle {
  width: 800px;
}
.active {
  color: #ff6917;
  border-color: #ff6917;
  background-color: #fff4ef;
}
.ruleForm {
  width: 560px;
  /deep/ .el-input {
    width: 460px;
  }
}
.tabs {
  width: 600px;
  display: flex;
  justify-content: space-evenly;
  margin-bottom: 30px;
}
.sug_tit {
  font-size: 33px;
  font-weight: 800;
  color: #ff6716;
  letter-spacing: 4px;
}
.sug_subTit {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  padding-top: 15px;
}
.suggest_type {
  font-size: 15px;
  font-weight: 500;
  color: #000000;
}
.suggest_typeItem {
  width: 50%;
  text-align: center;
  padding: 30px 0;
  background-color: #f3f3f3;
  transition: all 0.3s;
  cursor: pointer;
}
.suggest_typeItem.active {
  background-color: #fef0df;
  font-weight: 600;
}
.custorm_itemWx {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  padding: 0px 0 20px;
}
.custorm_wxCode {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  background-color: #f2f2f2;
  padding: 14px 26px;
  border-radius: 50px;
  margin: 20px 0;
  text-align: center;
}
.copy_title {
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #ff6716;
}
.borderSuggest {
  text-align: center;
  background: #fff;
}
.right_sug {
  margin-top: 20px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 32px; /* 160% */
  letter-spacing: 0.8px;
  text-align: left;
}
.left_box_content {
  padding: 0px;
  width: 834px;
  border-radius: 24px;
}
.left_box_content_title {
  width: 118px;
  margin: 40px 0px 0px 37px;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.left_box_content_title_tab {
  width: 100%;
  margin-top: 20px;
  height: 90px;
  padding: 20px 0px;
  display: flex;
  align-items: center;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  .text {
    font-family: YouSheBiaoTiHei;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
  }
  .dividerLine {
    width: 0.5px;
    height: 7.5px;
    background: rgba(0, 0, 0, 0.4);
    margin: 0px 40px 0px 20px;
  }
  .notActive {
    color: rgba(0, 0, 0, 0.4);
    font-family: YouSheBiaoTiHei;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .active {
    background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
    -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
  }
}
.suggestForm {
  /deep/ .picUpload_wrapSmall {
    border-radius: 22.186px;
    border: 0.924px dashed #969696;
    background: #f7f7f7;
    width: 101.688px;
    height: 101.688px;
    margin-right: 16.64px;
  }
  /deep/ .picUpload_wrapSmall_img_list {
    border-radius: 10.909px !important;
  }
  /deep/ .el-form-item__label {
    color: #1b1b1b;
    text-align: right;
    /* 新/文本/中号 */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px; /* 187.5% */
    letter-spacing: 0.64px;
    margin-top: 11px;
    padding: 0px;
    &:before {
      background: var(--btn-background-gradient);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
      -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    }
  }
  .boxItem {
    /deep/ .el-form-item__label {
      margin-top: 0px !important;
    }
  }
  /deep/ .el-form-item {
    margin-bottom: 30px;
  }
  /deep/ .el-form-item__content {
    margin-left: 130px !important;
  }
  /deep/ .el-textarea__inner {
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding: 15px 20px 0px 20px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    color: #1b1b1b;
    resize: none;
    &::placeholder {
      color: #969696;
    }
  }
}
.suggestFormSelect {
  /deep/ .el-input {
    width: 358px;
    height: 52px;
  }
  /deep/ .el-input__inner {
    height: 52px;
    border-radius: 50px;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
}
.suggestListBtn {
  display: flex;
  height: 36px;
  padding: 0px 22px;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  border-radius: 20px;
  background: #f6f6f6;
  border: none;
}
.suggestListBtn:hover {
  background: #ff7a00;
  color: #fff;
}
.suggestListBtnActive {
  background: #ff7a00;
  color: #fff;
}
.suggestSubmit {
  height: 50px;
  padding: 0px 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 60px;
  border: 1px solid #ffddbe;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  margin-left: 123px;
}
.suggestSubmit.active,
.suggestSubmit:hover {
  background: var(--btn-background-gradient);
}
.pageStep2_box {
  width: 1200px;
  height: 189px;
  border-radius: 24px;
  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
