<template>
  <div class="g-bd">
    <el-dialog
      v-if="recordDialog"
      :visible.sync="recordDialog"
      :append-to-body="true"
      class="custom-image-viewer"
      title="购买记录"
    >
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="name" label="购买日期" width="200">
          <template slot-scope="scope">
            {{ util.formatTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="购买类型">
          <template slot-scope="scope">
            {{ scope.row.method == 1 ? '支付宝' : '微信' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="卡密" width="300">
          <template slot-scope="scope">
            {{ scope.row.activationCode }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="付款金额" width="130">
          <template slot-scope="scope">
            {{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="操作" width="130">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.activationCode"
              @click="createCode(scope.row, scope.$index)"
              >生成卡密</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      v-loading.fullscreen.lock="loading"
      v-if="showLogin"
      :visible.sync="showLogin"
      :append-to-body="true"
      title="短信验证码登录"
      width="400px"
    >
      <el-form :model="loginForm">
        <el-form-item class="formItm">
          <el-input v-model="loginForm.username" placeholder="请输入手机号">
          </el-input>
        </el-form-item>
        <el-form-item class="formItm">
          <div class="spaceBetween">
            <el-input
              v-model="loginForm.validcode"
              placeholder="请输入短信验证码"
            ></el-input>
            <el-button class="smsBtn" type="primary" @click="onSendSms">
              {{ codeMsg }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div>
        <el-button class="loginBtn" type="primary" @click="onLogin"
          >立即登录</el-button
        >
      </div>
    </el-dialog>
    <div class="safe_width_payCus">
      <div class="spaceBetween" style="padding: 20px 0px">
        <img class="logo" src="https://images.kkzhw.com/frontimg/pc_logo.png" />
        <div style="font-size: 16px" class="spaceStart">
          <div>
            <div v-if="userInfo.phone" class="spaceStart">
              <div class="logout" @click="loginOut">退出登录</div>
            </div>
            <div v-else class="login" @click="doShowLogin">登录</div>
          </div>
          <div class="record" @click="showRecord">购买记录</div>
        </div>
      </div>
      <div>
        <img
          src="https://images2.kkzhw.com/mall/images/20240909/mxtero_1725876083929.jpg"
        />
        <div class="downloadBox">
          <img
            class="downloadBtn"
            src="../../assets/download.png"
            @click="doDownload"
          />
        </div>
      </div>
      <div class="priceBox">
        <img class="linefoot" src="../../assets/linefoot.png" alt="" />
        <div class="spaceAround">
          <div
            v-for="(item, index) in itemList"
            :class="index == activeIndex ? 'item active' : 'item'"
            @click="chooseType(item, index)"
          >
            <div class="spaceStart">
              <img class="zs" src="../../assets/zs.png" />
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="price">
              <span>{{ item.price }}</span>
              <span class="unit">元</span>
            </div>
          </div>
        </div>
      </div>
      <div class="spaceAround payBox">
        <div>
          <div
            :class="
              payIndex == 2 ? 'spaceStart payItem active' : 'spaceStart payItem'
            "
            @click="choosePay(2)"
          >
            <img
              v-show="payIndex == 2"
              class="selectedImg"
              src="../../assets/selected.png"
            />
            <img class="payImg" src="../../assets/zfb.png" />
            <div>支付宝支付</div>
          </div>
          <div
            :class="
              payIndex == 1 ? 'spaceStart payItem active' : 'spaceStart payItem'
            "
            style="margin-bottom: 0"
            @click="choosePay(1)"
          >
            <img
              v-show="payIndex == 1"
              class="selectedImg"
              src="../../assets/selected.png"
            />
            <img class="payImg" src="../../assets/wx.png" />
            <div>微信支付</div>
          </div>
        </div>
        <div>
          <canvas
            v-show="showQrCode"
            id="QRCode_header"
            style="width: 260px; height: 260px"
          ></canvas>
          <div v-show="!showQrCode" class="btn" @click="createPay">
            立即支付
          </div>
        </div>
      </div>
      <div class="totalBox">
        <div>
          扫码支付：<span class="total">{{ total }}</span
          >元
        </div>
      </div>
    </div>

    <el-dialog
      v-loading.fullscreen.lock="loading"
      v-if="showSuc"
      :visible.sync="showSuc"
      :append-to-body="true"
      title="购买成功"
      width="400px"
    >
      <div class="buySuc">
        <div class="tit">支付成功</div>
        <div class="note">您已经成功购买KK卡刀宏</div>
        <div class="code">卡密为：{{ activationCode }}</div>
        <div class="tips">如首次使用请您下载软件后打开注册进行使用</div>
        <!-- <div v-if="cardId == 3 || cardId == 4" class="wx">
          一对一调试联系作者微信：18777777763
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { loginCodenumApi, sendPhoneCode, logout } from '@/api/login';
import util from '@/utils/index';
import isLogin from '@/utils/isLogin';
import { mapState } from 'vuex';
import {
  getList,
  payMacro,
  payCheck,
  getMemberMacroOrderList,
  orderDetail,
  createCode,
} from '@/api/kkquse';
import QRCode from 'qrcode';
export default {
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  data() {
    return {
      util,
      loading2: true,
      showSuc: false,
      loading: false,
      loginForm: {
        username: '',
        validcode: '',
      },
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
      captchaIns: null,
      showLogin: false,
      tableData: [],
      recordDialog: false,
      showQrCode: false,
      downloadLink: '',
      orderId: '',
      cardId: '1',
      total: '',
      activeIndex: 0,
      payIndex: 1,
      seller_id: '',
      itemList: [
        {
          name: '周卡',
          price: '6.6',
        },
        {
          name: '月卡',
          price: '18.8',
        },
        {
          name: '半年卡',
          price: '88',
        },
        {
          name: '尊享卡',
          price: '188',
        },
      ],
    };
  },
  mounted() {
    if (!isLogin()) {
      this.showLogin = true;
      this.initCaptcha();
    }
    this.getList();
  },
  methods: {
    createCode(row, index) {
      const orderId = row.id;
      createCode({
        orderId,
      }).then((res) => {
        if (res.code == 200 && res.data && res.data.activationCode) {
          this.$set(
            this.tableData[index],
            'activationCode',
            res.data.activationCode
          );
          this.$message.success('卡密生成成功');
        }
      });
    },
    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.loginForm.username,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    loginOut() {
      logout().then((res) => {
        if (res.code == 200) {
          localStorage.removeItem('token');
          localStorage.removeItem('yximtoken');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('userInfoExpire');
          localStorage.removeItem('contentData');
          localStorage.removeItem('gameListData');
          location.reload();
        }
      });
    },
    doShowLogin() {
      this.showLogin = true;
    },
    onSendSms() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      if (this.loginForm.username == '') {
        this.$message.error('请输入手机号码');
        return;
      }
      if (!myreg.test(this.loginForm.username)) {
        this.$message.error('请输入正确的手机号码');
        return false;
      }
      this.captchaIns && this.captchaIns.verify();
    },
    onLogin() {
      if (!this.loginForm.validcode) {
        this.$message.error('请输入验证码');
        return false;
      }
      this.loading = true;
      loginCodenumApi({
        username: this.loginForm.username,
        authCode: this.loginForm.validcode,
      }).then((response) => {
        this.loading = false;
        if (response.code == 200) {
          this.loginSuc(response);
        }
      });
    },
    loginSuc(response) {
      var date = response.data;
      localStorage.setItem('token', date.token);
      this.$store.dispatch('SetToken', date.token);
      location.reload();
    },
    initCaptcha() {
      window.initNECaptchaWithFallback(
        {
          captchaId: '3455bd8a6484410ea146980a113839aa',
          width: '320px',
          mode: 'popup',
          apiVersion: 2,
          onVerify: (err, data) => {
            if (err) return;
            this.doSendSmsCode(data);
          },
        },
        (instance) => {
          this.captchaIns = instance;
        }
      );
    },
    showRecord() {
      if (isLogin()) {
        getMemberMacroOrderList().then((res) => {
          if (res.code == 200) {
            this.tableData = res.data;
            this.recordDialog = true;
          }
        });
      } else {
        this.showLogin = true;
      }
    },
    doDownload() {
      window.open(this.downloadLink);
    },
    getSellerIdFromSearch() {
      const params = new URLSearchParams(location.search);
      const sellerId = params.get('seller_id');
      return sellerId || 0;
    },
    getList() {
      this.seller_id = this.getSellerIdFromSearch();
      getList(this.seller_id).then((res) => {
        if (res.code == 200) {
          this.downloadLink = res.data.link;
          let tempList = res.data.payment_amount || [];
          this.itemList.forEach((ele, index) => {
            this.itemList[index].price = tempList[`amount${index + 1}`];
            this.itemList[index].cardId = index + 1;
          });
          this.cardId = this.itemList[0].cardId;
          this.total = this.itemList[0].price;
        }
      });
    },
    countDown() {
      this.code -= 1;
      if (this.code <= 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var interval = setTimeout(() => {
        this.countDown();
      }, 1000);
    },
    resetPay() {
      this.showQrCode = false;
      this.stl = clearInterval(this.stl);
    },
    choosePay(index) {
      this.payIndex = index;
      this.resetPay();
    },
    chooseType(item, index) {
      this.resetPay();
      this.activeIndex = index;
      this.total = item.price;
      this.cardId = item.cardId;
    },
    startCheck() {
      this.stl = setInterval(() => {
        this.doCheck();
      }, 2000);
    },
    getOrderDetail() {
      orderDetail({
        orderId: this.orderId,
      }).then((res) => {
        if (res.code == 200) {
          this.resetPay();
          this.activationCode = res.data.activationCode;
          this.showSuc = true;
        }
      });
    },
    doCheck() {
      let data = {
        orderId: this.orderId,
      };
      payCheck(data).then((res) => {
        if (res.code == 200) {
          if (res.data == true) {
            this.getOrderDetail();
          }
        }
      });
    },
    createPay() {
      let data = {};
      data.cardId = this.cardId;
      data.method = this.payIndex;
      data.sellerId = this.seller_id;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      payMacro(data)
        .then((res) => {
          if (res.code == 200) {
            this.orderId = res.data.orderId;
            this.creatQrCode(res.data.url);
            this.startCheck();
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    creatQrCode(picUrl) {
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 2, //二维码留白边距
        width: 260, //宽
        height: 260, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
      this.showQrCode = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.buySuc {
  font-size: 16px;
  line-height: 30px;
  .tit {
  }
  .code {
  }
}
.logout {
  margin-left: 10px;
  cursor: pointer;
}
.login {
  cursor: pointer;
}
.loginBtn {
  width: 100%;
}
.formItm {
  margin-bottom: 10px;
}
.smsBtn {
  margin-left: 20px;
}
.g-bd {
  background-color: #000;
  color: #fff;
  min-height: 100%;
}
.safe_width_payCus {
  width: 900px;
  margin: 0 auto;
  .logo {
    width: 400px;
    height: 90px;
  }
}
.downloadBox {
  margin-top: -114px;
}
.downloadBtn {
  width: 242px;
  height: 60px;
  margin: 0 auto;
  display: block;
  cursor: pointer;
}
.item {
  cursor: pointer;
  width: 210px;
  height: 133px;
  background: url('../../assets/cs.png') no-repeat;
  padding: 20px;
}
.item:hover,
.item.active {
  width: 210px;
  height: 133px;
  background: url('../../assets/cs_a.png') no-repeat;
  padding: 20px;
}
.linefoot {
  width: 525px;
  margin: 0 auto;
  padding: 50px 0 20px;
  display: block;
}
.price {
  font-weight: 600;
  font-size: 36px;
  padding-right: 6px;
}
.unit {
  font-size: 18px;
}
.name {
  padding-left: 8px;
  font-size: 24px;
}
.priceBox {
  padding-bottom: 50px;
}
.payBox {
  font-size: 20px;
  align-items: flex-end;
  .btn {
    font-size: 18px;
    color: #fff;
    background: #f8ab27;
    padding: 14px 40px;
    border-radius: 12px;
    cursor: pointer;
  }
  .payItem {
    font-size: 22px;
    color: #fff;
    margin-bottom: 30px;
    border-radius: 14px;
    border: 1px solid #fff;
    padding: 16px 36px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
    position: relative;
    .payImg {
      width: 34px;
      height: 34px;
      margin-right: 10px;
    }
    .selectedImg {
      position: absolute;
      top: -15px;
      right: -15px;
      width: 30px;
    }
  }
  .payItem.active {
    border-color: #56a741;
  }
}
.totalBox {
  padding: 20px 0 80px 130px;
  color: #fff;
  font-size: 26px;
  .total {
    color: rgb(251, 172, 53);
    font-weight: 600;
    font-size: 42px;
  }
}
.record {
  cursor: pointer;
  padding-left: 15px;
  color: rgb(248, 171, 39);
  font-weight: 600;
}
</style>
