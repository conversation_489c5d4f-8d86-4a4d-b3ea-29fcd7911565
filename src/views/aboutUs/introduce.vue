<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="pdTopBottom">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>关于我们</el-breadcrumb-item>
      </el-breadcrumb>

      <div class="page_comStyle" style="margin-bottom: 60px">
        <img src="../../../static/about_us.png" style="width: 100%" />
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

export default {
  name: 'Home',
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      index: 7,
      activeNames: ['1'],
    };
  },
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss"></style>
