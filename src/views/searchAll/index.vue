<template>
  <div
    ref="mianscroll"
    class="dark_container scrollBody listContentBk"
    style="width: 100%; height: 100vh; overflow-y: scroll"
  >
    <div class="playListBk">
      <headerKk :active-index="index" @doSearch="doInit" />
      <div :style="backgroundStr" class="paTopCom">
        <div class="safe_width">
          <el-breadcrumb
            separator-class="el-icon-arrow-right"
            class="pdTopBottom"
            style="padding: 34.28px 0 20.56px 0px"
          >
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/gameList' }"
              >全部游戏</el-breadcrumb-item
            >
          </el-breadcrumb>
          <div
            ref="offsetHeightNeed"
            class="page_comStyle"
            style="border-radius: 24px; padding: 40px"
          >
            <!-- 顶部搜索 -->
            <div
              v-if="accountShopList && accountShopList.length"
              class="spaceStart findcates"
            >
              <div>为您找到：</div>
              <div
                v-for="(item, index) in productCategoryNames"
                :key="index"
                class="productCategoryName"
                @click="goProductCategoryName(index)"
              >
                {{ item }}
              </div>
            </div>
            <!-- sort -->
            <div
              id="sort_container"
              ref="piediv"
              class="sort_container spaceBetween"
            >
              <div class="spaceStart">
                <div class="spaceStart" style="margin-right: 10px">
                  <el-select
                    v-model="stockValue"
                    class="zhpxbox"
                    style="width: 80px"
                    @change="stockValueChange"
                  >
                    <el-option
                      v-for="(item, index) in stockOpts"
                      :label="item.name"
                      :value="item.id"
                      :key="index"
                      >{{ item.name }}</el-option
                    >
                  </el-select>
                </div>
                <div class="spaceStart">
                  <div
                    v-for="(item, index) in comprehensiveData"
                    :class="item.selected != '' ? 'active' : ''"
                    :key="index"
                    class="spaceStart sort_item"
                    @click="sortChos(item)"
                  >
                    <div>{{ item.sortName }}</div>
                    <!-- <img
                    v-if="item.selected == '' && item.value != ''"
                    class="sortArr_pic"
                    src="../../../static/horn.png"
                  />
                  <img
                    v-if="item.selected == 'asc' && item.value != ''"
                    class="sortArr_pic"
                    src="../../../static/up.png"
                  />
                  <img
                    v-if="item.selected == 'desc' && item.value != ''"
                    class="sortArr_pic"
                    src="../../../static/down.png"
                  /> -->
                    <IconFont
                      v-if="item.selected == '' && item.value != ''"
                      :size="15"
                      style="margin: 0 0 0 4px"
                      icon="sort"
                    />

                    <IconFont
                      v-if="item.selected == 'asc' && item.value != ''"
                      :size="15"
                      style="margin: 0 0 0 4px"
                      icon="asc"
                    />
                    <IconFont
                      v-if="item.selected == 'desc' && item.value != ''"
                      :size="15"
                      style="margin: 0 0 0 4px"
                      icon="desc"
                    />
                  </div>
                </div>
              </div>

              <div
                style="color: #2d2d2d; cursor: pointer"
                @click="changeListStyle"
              >
                {{ lietStyleName }}
                <!-- <i
                class="el-icon-sort"
                style="transform: rotate(90deg); font-weight: 600"
              ></i> -->
                <IconFont :size="24" style="margin-left: 3px" icon="switch" />
              </div>
            </div>
          </div>
        </div>
        <!-- 列表 -->
        <div class="safe_width" style="margin-top: 34.28px">
          <div class="search_all_page_comStyle" style="padding: 0px">
            <!-- <div
            v-if="!accountShopList || !accountShopList.length"
            style="text-align: center; color: #666666; padding: 40px"
          >
            抱歉，暂时没有搜索到您要的账号，搜索商品编号请输入完整编号或编号数字
          </div> -->
            <div
              v-if="!accountShopList || !accountShopList.length"
              class="sorry"
            >
              <img
                style="width: 54px; height: 56px"
                src="../../../static/imgs/null.png"
                alt=""
              />
              <div style="margin-left: 15.85px">
                <!-- <div class="sorry_title">抱歉..</div> -->
                <img
                  style="width: 63px; height: 36px"
                  src="../../../static/imgs/sorry_text.svg"
                  alt=""
                />
                <div class="sorry_text">
                  暂时没有搜索到您要的商品，搜索商品编号请输入完整编号或编号数字
                </div>
              </div>
              <!-- 抱歉，暂时没有搜索到您要的账号 -->
            </div>
            <div
              v-loading.lock="listLoading"
              element-loading-text="加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
              customClass="fixeIndex"
              v-infinite-scroll="load" 
              infinite-scroll-delay="200" 
              infinite-scroll-disabled="isScrollDisabled"
            >
              <div
                v-for="(item, index) in accountShopList"
                :key="index"
                :class="isNoPic ? 'single_word' : ''"
                class="goodsList_item goodsItem_pic_img"
                @click="palyPage(item)"
              >
                <div class="spaceStartNotAi " style="width: 100%">
                  <div class="goodsItem_pic ">
                    <el-image
                      :src="item.pic"
                      style="width: 100%; height: 100%; border-radius: 12px"
                      fit="cover"
                       @click.stop="showImagePriview(item)"
                    ></el-image>
                    <el-image
                      v-if="item.stock == 0 || item.stock == 1"
                      class="soled_pic"
                      style="width: 100%; height: 100%; border-radius: 12px"
                      src="../../../static/soled.jpg"
                      fit="cover"
                       @click.stop="showImagePriview(item)"
                    ></el-image>
                    <div @mouseover="palyDivImg(item)" @click.stop="showImagePriview(item)"
                    class="goodsItem_pic_img_box">预览</div>
                    <!-- <div v-if="item.tssnum" class="tssnum">
                      <span class="innernum">
                        <img
                          style="width: 20px; height: 20px"
                          src="../../../static/imgs/tssnum_icon.svg"
                          alt=""
                        />
                        {{ item.tssnum }}天赏</span
                      >
                    </div> -->
                    <!-- :class="getTssClazz(item)" -->
                    <div
                      v-if="item.tssnum"
                      :class="getTssClazz(item)"
                      class="tssnum"
                    >
                      <span class="innernum">{{ item.tssnum }}天赏</span>
                    </div>
                    <div v-if="item.zxtssnum"  :class="getZxtssClazz(item)"
                        class="zxtssnum">
                        <span class="zxinnernum">{{ item.zxtssnum }}女娲石</span>
                        </div>
                    <div
                      v-else-if="item.tagsKKList && item.tagsKKList.length"
                      class="hot_pic"
                    >
                      {{ item.tagsKKList[0] }}
                    </div>
                  </div>
                  <div
                    :class="[
                      isNoPic
                        ? 'goodsItem_center_is_pic'
                        : 'goodsItem_center_is_not_pic',
                    ]"
                    class="goodsItem_center"
                  >
                    <div>
                      <!-- <div
                        class="goodsItem_center_title"
                        style="padding-bottom: 12px"
                      >
                        【{{ item.productSn }}】
                        <span v-if="item.accountType"
                          >【{{ item.accountType }}】</span
                        >
                        <span v-if="item.careers">【{{ item.careers }}】</span>
                      </div> -->

                      <!-- <el-tooltip
                        v-if="isNoPic == false"
                        :visible-arrow="false"
                        popper-class="tooltip_list"
                        class="item"
                        :open-delay="550"
                        effect="dark"
                        placement="bottom"
                         @mouseleave="palyTitleMouseleave"
                      >
                      <div slot="content">
                              <div
                                class="topTips_tit topTips_tit_play spaceBetween"
                              >
                                <span>商品详情 ｜ {{ item.productSn }}</span>
                                <a
                                  style="font-size: 14px;color: #ff720c;"
                                  :href="`/gd/${item.productSn}`"
                                  target="_blank"
                                 
                             
                                  >查看详情</a
                                >
                              </div>
                              <div
                                class="topTips_con light topTips_content playListTopTips_table"
                          
                               
                              >
                                <el-table
                                  style="margin-top: 10px;"
                                  :data="tableData"
                                   :span-method="arraySpanMethod"
                                  border
                                 
                                >
                                  <el-table-column prop="name" width="130">
                                  </el-table-column>
                                  <el-table-column prop="value">
                                  </el-table-column>
                                  <div  
                                  v-loading="playTableLoading"
                                  element-loading-text="加载中"
                                  element-loading-spinner="el-icon-loading"
                                  element-loading-background="rgba(255, 255, 255, 0.8)" slot="empty">
                        
                                  </div>
                                </el-table>
                              </div>
                            </div> -->
                            <div
                              class="topTips_con text_linThree list_infoWord"
                              v-if="isNoPic == false"
                            >
                              <div
                                class="light goodsItem_center_content"
                                style="width: 794px"
                                v-html="tedianFilter(`${item.subTitle}`, item)"
                              ></div>
                            </div>
                      <!-- </el-tooltip> -->
                      <div v-else class="topTips_con">
                        <div
                          class="light content_is_pic"
                          style="width: 1030px"
                          v-html="tedianFilter(item.subTitle, item)"
                        ></div>
                      </div>

                      <!-- <div
                    class="goodsItem_center_address textOneLine"
                    style="font-family: PingFang SC"
                  > -->
                      <!-- <span v-for="(itemS, indexS) in item.details" :key="indexS"
                        >{{ itemS.lable }}:{{ itemS.value }}；</span
                      > -->
                      <!-- {{ `${item.productCategoryName}|${item.gameAccountQufu}` }}
                  </div> -->
                      <div class="attrValueListText">
                        {{ getTdTxt(item) }}
                      </div>
                      <div class="districtServer">
                        {{ item.gameAccountQufu }}
                      </div>
                      <div
                        class="spaceStart goodsItem_center_address"
                        style="font-family: PingFang SC"
                      >
                        <div
                          class="spaceStart"
                          style="
                            margin-right: 38px;
                            font-size: 14px;
                            color: #9a9a9a;
                          "
                        >
                          <div style="color: #969696">发布时间：</div>
                          <div>{{ item.publishTime | formatTime }}</div>
                        </div>
                        <div class="spaceStart">
                          <div class="spaceStart">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoReadcount || 0 }}</div>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                          </div>
                          <div class="spaceStart">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoCollectcount || 0 }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="spaceEnd"
                      style="justify-content: space-between; margin-top: 9px"
                    >
                      <div>
                        <img
                          v-if="item.topAccount == '顶级账号'"
                          style="width: 90px; height: 30.38px"
                          src="../../../static/imgs/playList_topAccount.svg"
                          alt=""
                        />
                      </div>
                      <div
                        v-if="item.price && item.price != 0"
                        class="goodsItem_price"
                      >
                        ¥ {{ item.price }}
                        <div v-if="getJJPrice(item)" class="jjPrice">
                          <img
                            src="../../../static/imgs/reducePrice.png"
                            alt=""
                            style="width: 18px"
                          />已降价¥ {{ getJJPrice(item) }}
                        </div>
                      </div>
                      <!-- <div v-else class="goodsItem_price">联系客服</div>

                    <div class="goodsItem_btn">查看详情</div> -->
                    </div>
                  </div>
                </div>
                <!-- <div
                  v-if="index === accountShopList.length - 1"
                  style="
                    text-align: center;
                    padding: 36px 0px 15px 0px;
                    position: relative;
                    z-index: 99;
                  "
                  @click.stop
                >
                  <el-pagination
                    :total="totalPage"
                    style="padding: 0"
                    layout=" pager,jumper"
                    class="playList_search_page_pagination"
                    @current-change="pageFun"
                  >
                  </el-pagination>
                </div> -->
                <!-- <img
                style="
                  width: 1200px;
                  height: 1px;
                  position: absolute;
                  bottom: 0px;
                  left: 0px;
                "
                class="playListDavie"
                src="../../../static/imgs/divider.png"
                alt=""
              /> -->
                <!-- <div v-if="item.price && item.price != 0" class="goodsItem_price">
                ¥ {{ item.price }}
              </div>
              <div v-else class="goodsItem_price">联系客服</div>

              <div class="goodsItem_btn">查看详情</div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed
      :product-category-id="productCategoryId"
      @goPageTop="backTopPage"
    />
    <swperImagePriview
      v-if="showViewer"
      :z-index="10000"
      :initial-index="imgViewer"
      :on-close="closeViewer"
      :url-list="arrDtPicForShow"
      :tableData="tableData"
      :productSn="productSn"
      :tableDataFlag="true"
      :gameSysinfoReadcount="gameSysinfoReadcount" :gameSysinfoCollectcount="gameSysinfoCollectcount"
      :price="price"
      :product="productObj"
    />
  </div>
</template>

<script>

const STOCKQUERYALL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 9,
    },
  ],
};
const STOCKQUERYOVER = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 0,
    },
  ],
};

const STOCKQUERYOL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 2,
      max: 9,
    },
  ],
};

const STOCKQRYMAP = {
  'all': STOCKQUERYALL,
  'ol': STOCKQUERYOL,
  'over': STOCKQUERYOVER,
};

const DEFSTOCKOPTS = [
  {
    name: '全部',
    id: 'all',
  },
  {
    name: '在售',
    id: 'ol',
  },
  {
    name: '已售',
    id: 'over',
  },
];
import _ from 'lodash';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import swperImagePriview from '@/components/imagePriview.vue'
import {
  searchProductList2,
  getProductAttribute,
  searchRelate,
} from '@/api/search.js';
import { getDetailByCode } from '@/api/playDetail';
import util from '@/utils/index';
import { mapState } from 'vuex';
import defaultImage from '../../../static/imgs/sl_img.png';
export default {
  metaInfo: {
    title: '看看账号网',
    titleTemplate: null,
  },
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    swperImagePriview
  },
  data() {
    return {
      isScrollDisabled:true,
      stockOpts: DEFSTOCKOPTS,
      productCategoryIds: [],
      productCategoryNames: [],
      saveCateList: [],
      cateList: [],
      //   dataParams: {},
      ename: '',
      comprehensiveDataSort: '',
      comprehensiveDataOrder: '',
      singleCate: [],
      comprehensiveData: [],
      //   multipleCate: [],
      inputCate: [],
      tinptCate: [],
      lietStyleName: '切换文字版',
      isNoPic: false, // 是否不展示图片模式
      activeNames: ['1'],
      background: '',
      backgroundImg: '',
      index: 1,
      jsonGame: {}, // 游戏展示
      totalPage: 10,
      productCategoryId: '',
      keyword: '', // 账号搜索
      clas_id: '', // 专区id
      serverDateSub: [], // nsh 服务器
      accountShopList: [], // 账号数据
      classDate: [],
      GameBannerList: [],
      listLoading: false,
      searchParam: {
        productCategoryId: this.$route.query.productCategoryId,
        pageNum: 1,
        pageSize: 10,
      },
      stockValue: 'ol',
      tableData: [],
      playListTimer:null,
      playTableLoading:true,
      showViewer:false,
      arrDtPicForShow:[],
      imgViewer:0,
      productSn:'',
      gameSysinfoReadcount:'',
      gameSysinfoCollectcount:'',
      price:'',
      productObj:{}
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    backgroundStr: function () {
      if (this.backgroundImg) {
        return (
          'background: url(' +
          this.backgroundImg +
          ') no-repeat center top / 100% auto;'
        );
      } else if (this.background) {
        return (
          'background: linear-gradient(' +
          this.background +
          ' 0%, rgb(255, 255, 255, 0) 50%);'
        );
      }
    },
    stockQuery() {
      return STOCKQRYMAP[this.stockValue];
    },
  },
  created() {},
  mounted() {
    this.doInit();
  },
  methods: {
    palyDivImg(item) {
      // this.palyTitleMouseenter2(item)
    },
    showImagePriview(item){
      getDetailByCode({ productSn: item.productSn }).then((res) => {
        const product=res.data.product
        let albumPicsJson = product.albumPicsJson ? product.albumPicsJson:[];
        let arr=[]
        if(product.albumPics){
          arr = product.albumPics.split(',').filter(item => item.trim() !== '');
        }else{
          albumPicsJson = JSON.parse(albumPicsJson)
          albumPicsJson.forEach(item=>{
          if(arr.length<10&&item.url){
            arr.push(item.url)
          }
        })
        }
        this.productObj=item
        this.productSn=item.productSn
        this.gameSysinfoReadcount=item.gameSysinfoReadcount
        this.gameSysinfoCollectcount=item.gameSysinfoCollectcount
        this.price=item.price
        let oldArr = this.mergeOptions(
          res.data.productAttributeList,
          res.data.productAttributeValueList
        );
        let newArr = [];
        let newArr2 = [];
        oldArr.forEach((item) => {
          if (item.selectType == 2) {
            newArr2.push(item);
          } else {
            newArr.push(item);
          }
        });

        newArr.sort((a, b) => {
          return b.type - a.type;
        });
        newArr2.sort((a, b) => {
          return b.sort - a.sort;
        });
        if (res.data.product.description) {
          newArr2.push({
            name: `【卖家说】${res.data.product.description}`,
            value: '',
            sort: 11,
            selectType: 2,
          });
        }
        let allArr = newArr.concat(newArr2);
        // console.log(JSON.stringify(allArr),8989)
        // console.log(searchArr,7777)
        // this.tableData = newArr.concat(newArr2);
        this.tableData = allArr
        console.log(this.tableData)
        this.playTableLoading = false
        arr.unshift(defaultImage);
        this.arrDtPicForShow=arr
        this.showViewer = true
      })
    },
    closeViewer() {
      this.showViewer = false;
    },
    mergeOptions(productAttributeList, productAttributeValueList) {
      productAttributeList.sort((a, b) => {
        return a.type - b.type;
      });
      productAttributeList.sort((a, b) => {
        return b.sort - a.sort;
      });
      let tempList = [];
      productAttributeList.forEach((ele) => {
        if (ele.name == '营地ID') {
          const findIt = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          this.wzryId = findIt && findIt.value;
        }
        if (ele.type === 1 || ele.type === 2) {
          const findV = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          if (findV && findV.value) {
            tempList.push({
              name:
                ele.selectType == 2
                  ? `【${ele.name}】${this.formatValue(findV.value)}`
                  : ele.name,
              value: this.formatValue(findV.value),
              sort: ele.sort,
              selectType: ele.selectType,
            });
          }
        }
      });
      return tempList;
    },
    formatValue(value) {
      return value
        .replace(/[,]/g, '，')
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.selectType === 2) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },
    palyTitleMouseleave() {
      
      setTimeout(()=>{
        this.playTableLoading=true
      this.tableData=[]
      },300)
    },
    palyTitleMouseleave2(){
      clearTimeout(this.playListTimer);
    },
    palyTitleMouseenter(e) {
      this.playListTimer = setTimeout(() => {
        getDetailByCode({ productSn: e.productSn }).then((res) => {
          let oldArr = this.mergeOptions(
            res.data.productAttributeList,
            res.data.productAttributeValueList
          );
          let newArr = [];
          let newArr2 = [];
          oldArr.forEach((item) => {
            if (item.selectType == 2) {
              newArr2.push(item);
            } else {
              newArr.push(item);
            }
          });

          newArr.sort((a, b) => {
            return b.type - a.type;
          });
          newArr2.sort((a, b) => {
            return b.sort - a.sort;
          });
          if (res.data.product.description) {
            newArr2.push({
              name: `【卖家说】${res.data.product.description}`,
              value: '',
              sort: 11,
              selectType: 2,
            });
          }
          this.tableData = newArr.concat(newArr2);
          this.playTableLoading=false
        });
      }, 500);
    },
    palyTitleMouseenter2(e) {
        getDetailByCode({ productSn: e.productSn }).then((res) => {
          this.productSn=e.productSn
          this.gameSysinfoReadcount=e.gameSysinfoReadcount
          this.gameSysinfoCollectcount=e.gameSysinfoCollectcount
          this.price=e.price
          let oldArr = this.mergeOptions(
            res.data.productAttributeList,
            res.data.productAttributeValueList
          );
          let newArr = [];
          let newArr2 = [];
          oldArr.forEach((item) => {
            if (item.selectType == 2) {
              newArr2.push(item);
            } else {
              newArr.push(item);
            }
          });

          newArr.sort((a, b) => {
            return b.type - a.type;
          });
          newArr2.sort((a, b) => {
            return b.sort - a.sort;
          });
          if (res.data.product.description) {
            newArr2.push({
              name: `【卖家说】${res.data.product.description}`,
              value: '',
              sort: 11,
              selectType: 2,
            });
          }
          this.tableData = newArr.concat(newArr2);
          this.playTableLoading=false
        });
    },
    getJJPrice(item) {
      const data = JSON.parse(item.priceHistory || '[]');
      data.sort((a, b) => a.changeTime - b.changeTime);
      if (!data.length) {
        return '';
      }
      const priceDiff = data[0].price - data[data.length - 1].price;
      // 添加判断，如果价格差是负数，返回空字符串
      if (priceDiff < 0) {
        return '';
      }

      return priceDiff;
    },
    getTdTxt(item) {
      if (item.productCategoryId === 75) {
        const str1 = item.attrValueList
          .filter((ele) =>
            ['稀有外观', '天赏祥瑞', '天赏发型'].includes(ele.name)
          )
          .map((ele) => ele.value)
          .join(' ');
        const str2 = item.attrValueList
          .filter(
            (ele) =>
              ['灵韵数量', '天霓染'].includes(ele.name) &&
              ele.value &&
              ele.value !== '0'
          )
          .map((ele) => ele.name + ele.value)
          .join(' ');
          return item.description?item.description:(str1 + ' ' + str2).replace('数量', '').replace(/,/g, ' ');
      }
      console.log(item.productCategoryId, '执行了');
      if (item.productCategoryId != 75) {
        return item.description;
      }
    },
    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    stockValueChange(value) {
      this.doSearch();
    },
    goProductCategoryName(index) {
      const productCategoryId = this.productCategoryIds[index];
      this.$router.push({
        path: `/playList?productCategoryId=${productCategoryId}&keyword=${this.keyword}`,
      });
    },
    doInit() {
      if (this.$route.query.keyword) {
        this.keyword = this.$route.query.keyword;
        // 如果搜索为编号，则产品状态stock为全部
        if (/^[0-9a-zA-Z]+$/.test(this.keyword)) {
          this.stockValue = 'all';
        } else {
          this.stockValue = 'ol';
        }
      }
      this.formatClasData();
      this.doSearch();
      searchRelate({
        keyword: this.keyword,
      }).then((res) => {
        if (res.code === 200) {
          this.productCategoryNames = res.data.productCategoryNames || [];
          this.productCategoryIds = res.data.productCategoryIds || [];
        }
      });
    },
    checkSn(list) {
      return false;
      let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      if (
        reg.test(this.keyword) &&
        list.length === 1 &&
        this.searchParam.pageNum === 1
      ) {
        const { productCategoryId, id, productSn } = list[0];
        this.$router.replace({
          path: `/gd/${productSn}`,
        });
      }
    },
    load(){
      this.searchParam.pageNum++
      this.doSearch(1)
    },
    doSearch(isPageNum) {
      this.listLoading = true;
      if (isPageNum != 1) {
        this.searchParam.pageNum = 1;
        this.totalPage = 0;
      }
      let data = {
        keyword: this.keyword,
        sort: this.comprehensiveDataSort || '',
        order: this.comprehensiveDataOrder || '',
      };
      if (this.stockQuery) {
        let queryIntParams = data.queryIntParams;
        if (queryIntParams) {
          data.queryIntParams = data.queryIntParams.concat(
            this.stockQuery.queryIntParams
          );
        } else {
          data.queryIntParams = this.stockQuery.queryIntParams;
        }
      }
      let searchParam = _.cloneDeep(this.searchParam);
      if (this.userInfo.id) {
        // data.memberId = this.userInfo.id;
        searchParam.memberId = this.userInfo.id;
      }
      searchProductList2(searchParam, data).then((response) => {
        this.listLoading = false;
        if (response.code === 200) {
          let list = response.data.list || [];
          this.checkSn(list);
          if(list.length<10){
            this.isScrollDisabled=true
          }else{
            this.isScrollDisabled=false
          }
          this.totalPage = response.data.total;
          if(isPageNum==1){
            this.accountShopList.push(...list)
          }else{
            this.accountShopList = list;
          }
          this.accountShopList.forEach((ele) => {
            const findtss = ele.attrValueList.find((item) => {
              return item.name === '已使用天赏石';
            });
            const findtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用天赏石';
            });
            const findtss3 = ele.attrValueList.find((item) => {
              return item.name === '账号专区';
            });
            const findtss4 = ele.attrValueList.find((item) => {
              return item.name === '账号类型';
            });
            const findtss5 = ele.attrValueList.find((item) => {
              return item.name === '职业';
            });
            const zxfindtss = ele.attrValueList.find((item) => {
              return item.name === '已使用女娲石数量';
            });
            const zxfindtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用女娲石数量';
            });
            ele.zxtssnum = 0;
            ele.tssnum = 0;
            ele.topAccount = '';
            ele.accountType = '';
            ele.careers = '';
            if(zxfindtss && zxfindtss.intValue !== -1){
              ele.zxtssnum = zxfindtss.intValue;
            }
            if(zxfindtss2 && zxfindtss2.intValue !== -1){
              if(zxfindtss2.intValue === -1) {
                zxfindtss2.intValue = 0;
              }
              if(ele.zxtssnum === -1) {
                ele.zxtssnum = 0;
              }
              ele.zxtssnum = ele.zxtssnum + zxfindtss2.intValue;
            }
            if (findtss) {
              ele.tssnum = findtss.intValue;
            }
            if (findtss2) {
              ele.tssnum = ele.tssnum + findtss2.intValue;
            }
            if (findtss3) {
              ele.topAccount = findtss3.value;
            }
            if (findtss4) {
              ele.accountType = findtss4.value;
            }
            if (findtss5) {
              ele.careers = findtss5.value;
            }
          });
        }
      });
    },
    formatClasData() {
      this.comprehensiveData = [];
      // this.comprehensiveData.push({
      //   sortName: '综合排序',
      //   selected: '',
      //   searchSort: 'score',
      //   value: '',
      // });
      this.comprehensiveData.push({
        sortName: '最多人看',
        selected: '',
        searchSort: 'gameSysinfoReadcount',
      });
      this.comprehensiveData.push({
        sortName: '时间',
        selected: '',
        searchSort: 'publishTime',
      });
      this.comprehensiveData.push({
        sortName: '价格',
        selected: '',
        searchSort: 'price',
      });
    },
    //分割线
    changeListStyle() {
      if (this.isNoPic == false) {
        this.isNoPic = true;
        this.lietStyleName = '切换图文版';
      } else {
        this.isNoPic = false;
        this.lietStyleName = '切换文字版';
      }
    },
    // 滚动到顶部
    backTopPage() {
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 滚动回到固定位置
    scrollTop() {
      var heightNum = this.$refs.offsetHeightNeed.offsetHeight + 230;
      // var topDistance = this.$refs.piediv.getBoundingClientRect().top;
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: heightNum,
        behavior: 'smooth',
      });
    },
    // 顶部搜索框 搜索游戏ID变化
    // 筛选排序
    sortChos(date) {
      if (date.sortName == '综合排序') {
        this.comprehensiveDataOrder = '';
        this.comprehensiveDataSort = '';
        this.comprehensiveData.forEach((item, index) => {
          if (index == 0) {
            item.selected = '综合排序';
          } else {
            item.selected = '';
          }
        });
      } else {
        this.comprehensiveData.forEach((item, index) => {
          if (item.sortName == date.sortName) {
            if (date.selected == '') {
              date.selected = 'desc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'desc';
            } else if (date.selected == 'desc') {
              date.selected = 'asc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'asc';
            } else if (date.selected == 'asc') {
              date.selected = '';
              this.comprehensiveDataOrder = '';
              this.comprehensiveDataSort = '';
            }
          } else {
            item.selected = '';
          }
        });
      }
      this.doSearch();
    },
    getTssClazz(item) {
      let l = item.tssnum.toString().length;
      return `tssnum${l}`;
    },
    getZxtssClazz(item) {
      let l = item.zxtssnum.toString().length;
      return `zxtssnum${l}`;
    },
    // 分页
    pageFun(val) {
      this.searchParam.pageNum = `${val}`;
      this.doSearch(1);
      this.scrollTop();
    },
    // 账号详情
    palyPage(date) {
      let routeUrl = this.$router.resolve({
        path: '/gd/' + date.productSn,
      });
      window.open(routeUrl.href, '_blank');
    },
  },
};
</script>

<style type="text/css">
.tooltip_list {
  width: 600px;
  flex-shrink: 0;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 0px 0px 20px 0px;
  opacity: 1 !important;
}
.productCategoryName {
  margin-left: 10px;
  /* color: #2196f3; */
  color: #ff720c;
  cursor: pointer;
  font-family: 'PingFang SC';
  line-height: normal;
  letter-spacing: 0.8px;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
.light span {
  color: #fe5a1e;
}
.playListTopTips_table{
  padding: 0px 10px;
}
.playListTopTips_table .el-loading-mask {
  position: relative
}
.playListTopTips_table .el-table__empty-text {
  line-height: 20px;
}
.playListTopTips_table thead {
  display: none;
}
.playListTopTips_table .el-table__cell {
  padding: 5px 0px;
}
.topSearch_clas {
  margin-right: 20px;
  border-radius: 30px;
}
.el-select.topSearch_clas .el-input__inner {
  border-radius: 10px;
  border-color: #ff6716;
  text-align: center;
  width: 150px;
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-left: 10px !important;
  margin-top: 11px !important;
}
.el-collapse-item__wrap {
  overflow: visible !important;
}
</style>
<style lang="scss" scoped>
/deep/.playList_search_page_pagination {
  position: relative;
  .el-pagination__jump {
    position: absolute;
    right: 0px;
    color: #2d2d2d;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    .el-input__inner {
      height: 20px;
      border-radius: 20px;
      border: none;
      background: #f6f6f6;
    }
  }
}
.playList_search_page_pagination /deep/ ul {
  li {
    color: rgba(0, 0, 0, 0.4);
    min-width: 24px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;

    letter-spacing: 0.64px;
    padding: 0px 7px;
  }
  .active {
    color: #2d2d2d !important;
  }
}
.topTips_tit {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  line-height: 0px;
}
.topTips_tit_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #000;
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  margin: 8px 0px;
}
.topTips_content {
  color: rgba(0, 0, 0, 0.6);
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  max-height: 400px;
  overflow-y: auto;
}
/deep/ .zhpxbox {
  .el-input__inner {
    border: 0;
    background: #f4f4f4;
    font-size: 16px;
    color: #222;
  }
}
.stock_box {
  padding: 20px 0;
  border-bottom: 1px solid #dcdcdc;
}

.findcates {
  font-size: 16px;
  font-family: 'PingFang SC';
  padding-bottom: 40px;
  border-bottom: 0.5px solid #ff7a00;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
  color: #2d2d2d;
  flex-wrap: wrap;
}
.scrollBody::-webkit-scrollbar {
  width: 2px;
}
.scrollBody::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}
.playHead_wrap {
  border-bottom: 1px solid #dcdcdc;
  padding-bottom: 25px;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 100px;
  font-size: 14px;
  font-weight: 400;
  color: #222222;
}
.topTips_tit_play {
  position: relative;
  z-index: 99;
  padding: 20px 10px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08); /* 添加下阴影 */
}
.playSearch_wrap {
  padding-top: 8px;
  align-items: baseline;
}
.choose_dl {
  display: flex;
  .more-btn {
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    color: #999;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    margin-bottom: 20px;
    user-select: none;
    width: 90px;
  }
}
.choose_dl_close {
  height: 44px;
  overflow: hidden;
  .choose_label_checkbox {
    height: 44px;
  }
}
.choose_dl_open {
  height: inherit;
  .choose_label_checkbox {
    height: inherit;
  }
}
.choose_label_box {
  display: flex;
  align-items: baseline;
}
.choose_label_checkbox {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.playSearch_item {
  cursor: pointer;
  padding: 0px 25px;
  background: #ffffff;
  border: 1px solid #dcdcdc;
  border-radius: 8px;
  font-size: 14px;
  color: #909090;
  margin-right: 13px;
  transition: all 0.3s;
  margin-top: 8px;
  line-height: 32px !important;
  height: 32px !important;
  flex-shrink: 0;
}
.playSearch_item.active {
  color: #ff6716;
  background: #fff4ee;
  border: 1px solid #ff4f11;
}
.sort_container {
  // padding: 9px 30px;
  box-sizing: border-box;
  width: 100%;
  // background: #f4f4f4;
  margin-top: 40px;
  font-size: 16px;
  font-weight: 500;
  color: #2d2d2d;
  font-family: 'PingFang SC';
}
.sort_item {
  margin-right: 40px;
  cursor: pointer;
  transition: all 0.3s;
}
.clearSearch {
  transition: all 0.3s;
}
// .sort_item.active,
// .sort_item:hover,
// .clearSearch:hover {
//   color: #ff6716;
// }
.sortArr_pic {
  width: 10px;
  height: auto;
  margin-left: 8px;
}
.goodsList_item {
  box-sizing: border-box;
  min-height: 219px;
  padding: 24px 35px 24px 24px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
  border-radius: 24px;
  margin-bottom: 12px;
}
.goodsList_item:last-child {
  border-bottom: none;
}
// .goodsList_item:hover {
//   background: #fff;
//   box-shadow: 0px 5px 10px -5px rgba(179, 179, 179, 0.36);
//   transform: scale(1.0177);
//   border-radius: 20.56px;
//   margin-top: 2px;
//   border: none;
//   .playListDavie {
//     display: none;
//   }
// }
.goodsItem_pic {
  width: 274px;
  height: 171px;
  position: relative;
}
.soled_pic {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
}
.tssnum {
  position: absolute;
  z-index: 10;
  top: 0px;
  left: 0px;
  min-width: 150px;
  height: 40px;
  line-height: 29px;
  display: flex;
  align-items: center;
  .innernum {
    color: #000;
    text-align: center;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: -5px;
    margin-left: 0px;
  }
}
.tssnum1 {
  background: url(../../../static/imgs/newT1.png) no-repeat top;
  background-size: 100% 100%;

  .innernum {
    margin-left: 51px;
  }
}
.tssnum2 {
  background: url(../../../static/imgs/newT2.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 50px;
  }
}
.tssnum3 {
  background: url(../../../static/imgs/newT2.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 50px;
  }
}
.tssnum4 {
  background: url(../../../static/imgs/newT3.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 51px;
  }
}
.hot_pic {
  position: absolute;
  top: 5px;
  left: 5px;
  height: 20px;
  z-index: 50;
  font-size: 14px;
  color: #f56c6c;
  border: 1px solid #f56c6c;
  padding: 0 4px;
  line-height: 18px;
  background-color: #f4f0ea;
}
.goodsItem_center_is_not_pic {
  margin-left: 28.281px;
}
.goodsItem_center_is_pic {
  margin-left: 0px;
}
.goodsItem_center {
  flex: 1;
  font-size: 16px;
  color: #222222;

  .goodsItem_center_title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    line-height: normal;
    font-weight: 500;
    text-indent: -12px;
    letter-spacing: 0.8px;
  }
  .goodsItem_center_content {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 128.571% */
    letter-spacing: 0.56px;
    height: 60px !important;
  }
}

.content_is_pic {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}
.goodsItem_price {
  font-size: 20px;
  font-family: 'PingFang SC';
  font-weight: 600;
  line-height: normal;
  color: #ff720c;
  display: flex;
  align-items: center;
}
.goodsItem_btn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 16px;
  font-family: 'PingFang SC';
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
  height: 40px;
  line-height: 40px;
  padding: 0 26px;
  border-radius: 20px;
}
.goodsItem_center_address {
  // margin-top: 12px;
  margin-right: 40px;
  font-size: 14px;
  color: #9a9a9a;
  font-family: 'PingFang SC';
  font-weight: 400;
}
.chooseSelect {
  margin-right: 15px;
  margin-top: 15px;
  width: 240px;
}

.playList_icon {
  position: absolute;
  left: -160px;
  top: -20px;
}
.tagPic_acc {
  position: absolute;
  z-index: 10;
  left: 0;
  top: 0;
  width: 110px;
  height: auto;
}
.hotPic_list {
  position: absolute;
  width: 64px;
  z-index: 10;
  right: -10px;
  top: -35px;
}
.list_infoWord {
  height: 60px;
}
.text_linThree {
  -webkit-line-clamp: 3 !important;
  line-clamp: 3 !important;
}
/*********************************************  列表模式切换  *******************************************************/
.single_word {
  min-height: 100px;
}
.single_word .goodsItem_pic {
  width: 0;
  display: none;
}
.single_word .goodsItem_center {
  // width: 770px;
}
.search_all_page_comStyle {
  // background: #fff;
  box-sizing: border-box;
  border-radius: 24px;
  .sorry {
    // text-align: center;
    font-size: 18px;
    display: flex;
    // align-items: center;
    justify-content: center;
    text-align: left;
    padding: 64px 0px;
    background: #fff;
    border-radius: 24px;
    .sorry_title {
      color: #ff720c;
      font-family: YouSheBiaoTiHei;
      font-size: 27.42px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .sorry_text {
      color: #969696;
      /* 小字段落 */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 0%;
      margin-top: 5px;
    }
  }
}
.attrValueListText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 794px;
  font-family: 'PingFang SC';
  font-size: 14px;
  margin-top: 12px;
  color: #ff720c;
}
.districtServer {
  font-family: 'PingFang SC';
  font-size: 14px;
  color: #9a9a9a;
  font-weight: 400;
}
.jjPrice {
  background: #fff2e6;
  font-size: 14px;
  font-family: 'PingFang SC';
  color: #ff720c;
  padding: 3px 6px;
  border-radius: 50px;
  margin-left: 10px;
}
.goodsItem_pic_img_box{
  width: 274px;
  height: 171px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
display: none;
  top: 0px;
  left: 0px;
}
.goodsItem_pic_img:hover  .goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
