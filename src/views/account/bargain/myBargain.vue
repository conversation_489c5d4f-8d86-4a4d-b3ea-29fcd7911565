<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="padding: 0px 0px 39px 0px; border-radius: 24px"
  >
    <div class="acountList_header">
      <div
        v-for="(item, index) in bargainList"
        :key="index"
        class="acountList_header_box"
      >
        <div
          :class="index === bargainIndex ? 'active' : ''"
          class="title"
          @click="searchList(item, index)"
        >
          {{ item.name }}
        </div>
        <div class="divider"></div>
      </div>
      <!-- <div class="title active">查看全部</div>
      <div class="divider"></div>
      <div class="title">议价中</div>
      <div class="divider"></div>
      <div class="title">议价成功</div>
      <div class="divider"></div>
      <div class="title">议价失败</div> -->
    </div>
    <div class="accountList_orderSearch_cont">
      <el-form
        :inline="true"
        :model="formInline"
        style="display: flex; align-items: center"
        class="demo-form-inline"
        @submit.native.prevent="onSubmit()"
      >
        <el-form-item label="商品编号">
          <el-input v-model="formInline.cname" placeholder="请输入商品编号">
            <iconFont
              slot="suffix"
              :size="24"
              icon="web_search"
              color="#000000"
              style="margin-right: 14.16px; margin-top: 0px; cursor: pointer"
              @click="onSubmit"
          /></el-input>
        </el-form-item>
        <el-form-item>
          <!-- <el-button type="primary" round class="btnSe_con" @click="onSubmit"
            >搜索</el-button
          > -->
          <el-button
            type="primary"
            plain
            round
            class="reprovision_btn reprovision_reset_btn"
            @click="resetSearch"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div v-if="initDate && initDate.length" style="padding: 0 40px">
      <!-- table start -->
      <div style="margin-top: 27px" class="orderTable_head spaceBetween">
        <div class="widthOne">商品信息</div>
        <div class="widthTwo">单价</div>
        <div class="widthThree">议价</div>
        <div class="widthFour">状态</div>
        <!-- <div class="widthFive spaceEnd">操作</div> -->
      </div>

      <div
        v-for="(item, index) in initDate"
        :key="index"
        class="orderTable_body"
      >
        <div class="tableBody_head spaceBetween">
          <div>
            <span class="orderTable_left_title">商品编号：</span
            >{{ item.productSn }}
            <IconFont
              :size="14"
              icon="copy"
              class="accountList_copy"
              @click="copyVal(item.productSn)"
            />
          </div>
          <!-- <div>
            <el-link v-if="item.status == 4" :underline="false" type="success"
              >已支付</el-link
            >
            <el-link v-if="item.status == 5" :underline="false" type="primary"
              >已退款</el-link
            >
            <el-link v-if="item.status == 6" :underline="false" type="info"
              >退款失败</el-link
            >
            <el-link v-if="item.status == 9" :underline="false" type="success"
              >已完成</el-link
            >
            <el-link v-if="item.status == 11" :underline="false" type="danger"
              >已取消</el-link
            >
          </div> -->
          <div>
            <span class="orderTable_left_title">提交时间：</span
            >{{ item.createTime | formatTimetoSS }}
          </div>
        </div>
        <div class="spaceBetween tableBody_box">
          <div class="spaceStart widthOne cursor" @click="palyPageDetail(item)">
            <div class="orderShop_pic">
              <el-image
                :src="item.productPic"
                style="width: 100%; height: 100%"
                fit="cover"
              ></el-image>
            </div>
            <!-- <div>
              <div class="orderShop_tit">{{ item.productSn }}</div>
              <div class="orderShop_subT">{{ item.productCategoryName }}</div>
            </div> -->
            <!-- @click="goSellDetail(item)" -->
            <div class="order_goods_box" style="cursor: pointer">
              <div>
                <!-- <div class="order_produceSn">【{{ item.productSn }}】</div> -->
                <div style="margin: 0" class="orderShop_tit text_linTwo">
                  {{ item.productName }}
                </div>
              </div>
              <!-- <div class="orderShop_subT">
                游戏区服：{{ item.gameAccountQufu }}
              </div> -->
              <div class="order_goods_name">
                {{ item.productCategoryName }}
              </div>
            </div>
          </div>
          <div class="widthTwo order_price">¥ {{ item.originPrice }}</div>
          <div class="widthThree order_price">¥ {{ item.offerPrice }}</div>
          <div class="widthFour goods_status">
            <div v-if="item.status == -1" style="color: #505050">待支付</div>
            <div v-if="item.status == 0">待卖家回复</div>
            <div v-if="item.status == 1" style="color: #28d386">
              议价成功,待付款
            </div>
            <div v-if="item.status == 8" style="color: #28d386">议价成功</div>
            <div
              v-if="item.status == 2 && !item.sellerOfferPrice"
              style="color: #ff002e"
            >
              卖家拒绝
            </div>
            <div v-if="item.status == 2 && item.sellerOfferPrice">
              卖家已还价
            </div>
            <div v-if="item.status == 3" style="color: #505050">已取消</div>
            <div v-if="item.status == 4" style="color: #28d386">
              买家已支付全款
            </div>
            <div v-if="item.status == 5" style="color: #ff002e">
              卖家拒绝,退款中
            </div>
            <div
              v-if="item.status == 6 && item.endOrderId"
              style="color: #28d386"
            >
              议价成功
            </div>
            <div v-else-if="item.status == 6" style="color: #505050">
              已退款
            </div>
            <div v-if="item.status == 7">退款中</div>
            <!-- <el-link v-if="item.status == -1" :underline="false" type="info"
              >待支付</el-link
            >
            <el-link v-if="item.status == 0" :underline="false" type="primary"
              >待卖家回复</el-link
            >
            <el-link v-if="item.status == 1" :underline="false" type="success"
              >议价成功,待付款</el-link
            >
            <el-link v-if="item.status == 8" :underline="false" type="success"
              >议价成功</el-link
            >
            <el-link
              v-if="item.status == 2 && !item.sellerOfferPrice"
              :underline="false"
              type="danger"
              >卖家拒绝</el-link
            >
            <el-link
              v-if="item.status == 2 && item.sellerOfferPrice"
              :underline="false"
              type="danger"
              >卖家已还价</el-link
            >
            <el-link v-if="item.status == 3" :underline="false" type="info"
              >已取消</el-link
            >
            <el-link v-if="item.status == 4" :underline="false" type="success"
              >买家已支付全款</el-link
            >
            <el-link v-if="item.status == 5" :underline="false" type="danger"
              >卖家拒绝,退款中</el-link
            >
            <el-link
              v-if="item.status == 6 && item.endOrderId"
              :underline="false"
              type="success"
              >议价成功</el-link
            >
            <el-link v-else-if="item.status == 6" :underline="false" type="info"
              >已退款</el-link
            >
            <el-link v-if="item.status == 7" :underline="false" type="info"
              >退款中</el-link
            > -->
          </div>
        </div>
        <div
          class="spaceEnd"
          style="margin-right: 41.13px; margin-bottom: 20.56px"
        >
          <div
            v-if="canDel(item)"
            class="plain cancel_favorites"
            @click="delOrder(item)"
          >
            删除
          </div>
          <div
            v-if="canRefoundBack(item)"
            class="orderTable_btn solid accountOrderBtn"
            @click="refoundBack(item)"
          >
            撤回议价
          </div>
          <el-button
            v-if="
              item.status == 1 || (item.status == 2 && item.sellerOfferPrice)
            "
            class="reprovision_btn plain accountOrderBtn"
            @click="palyPage(item)"
          >
            立即购买
          </el-button>
          <div
            v-if="item.status == -1"
            class="orderTable_btn solid accountOrderBtn"
            @click="payNowOrder(item)"
          >
            立即支付
          </div>
          <!-- <div
              v-if="item.status == -1"
              class="orderTable_btn plain"
              @click="cancelNow(item)"
            >
              取消支付
            </div> -->
          <div
            v-if="item.status == 2 && item.sellerOfferPrice"
            class="orderTable_btn solid accountOrderBtn"
            @click="huanjiaFun(item)"
          >
            还价
          </div>

          <!-- <div class="orderTable_btn solid accountOrderBtn" @click="goChat(item)">联系客服</div> -->
          <div class="orderTable_btn solid accountOrderBtn" v-if="item.zxkfer && util.isNumber(item.zxkfer)" @click="goNegoChat(item)">议价群</div>
        </div>

        <!-- <div
          v-if="item.dicker_log && item.dicker_log.length > 0"
          class="tableBody_head"
          style="padding: 6px 20px"
        >
          <div class="topTips_tit yijia_tit">议价记录</div>
          <div class="topTips_con">
            <div v-for="(itemS, indexS) in item.dicker_log" :key="indexS">
              <span v-if="itemS.role == 'seller'">卖家</span>
              <span v-else>买家</span>
              于 {{ itemS.created_at }} 新增议价：￥{{
                itemS.counteroffer_price
              }}；
            </div>
          </div>
        </div> -->
        <div
          v-if="item.sellerOfferPrice"
          class="tableBody_head spaceAlignCenter"
        >
          <!-- <el-tooltip
            :disabled="item.logs.length == 0"
            class="item"
            effect="dark"
            placement="top-start"
          >
            <div slot="content">
              <div v-for="ele in item.logs">{{ ele }}</div>
            </div> -->
          <div>
            <span class="orderTable_left_title">卖家最近还价：</span>¥{{
              item.sellerOfferPrice
            }}
          </div>
          <!-- </el-tooltip> -->
        </div>
      </div>
      <!-- tabler end -->

      <div class="tabler_footer_pagination">
        <el-pagination
          :total="totalPage"
          layout="pager,jumper"
          class="search_page_pagination"
          @current-change="pageFun"
        >
        </el-pagination>
      </div>
    </div>

    <div v-else class="list_null_sorry" style="margin-top: 3px">
      <img
        style="width: 54px; height: 56px"
        src="../../../../static/imgs/null.png"
        alt=""
      />
      <div style="margin-left: 15.85px">
        <img
          style="width: 63px; height: 36px"
          src="../../../../static/imgs/sorry_text.svg"
          alt=""
        />
        <div class="sorry_text">暂时无相关数据</div>
      </div>
    </div>

    <!-- 
            is_counteroffer：是否存在还价 1是 0否
            is_pay 是否存在支付 1是 0否
        -->
    <!-- 讨价还价 -->
    <!-- <el-dialog
      :visible.sync="dialogVisibleInden"
      width="30%"
      center
      title="还价"
    >
      <div class="priceSureNote">如卖家同意还价金额，不能无责取消</div>
      <el-input
        v-model="counteroffer_price"
        style="color: #333"
        placeholder="请输入还价金额"
      />
      <div class="spaceCenter">
        <div
          class="spaceCenter plDt_btn"
          style="margin-bottom: 20px; width: 90%"
          @click="counterPriceSure"
        >
          确认还价
        </div>
      </div>
    </el-dialog> -->
    <myBargainDialog
      :visible="dialogVisibleInden"
      :dialog-width="'523px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="myBargainDialog_right_title">还价</span>
      </template>
      <template slot="content">
        <div style="width: 322px; margin: 0 auto; text-align: center">
          <el-input
            v-model="counteroffer_price"
            class="myBargainDialog_input"
            style="margin-top: 36px; width: 322px"
            placeholder="请输入还价金额"
          />
          <div class="myBargainDialog_priceSureNote">
            如卖家同意还价金额，不能无责取消
          </div>
          <!-- <div class="spaceCenter">
            <div
              class="spaceCenter plDt_btn"
              style="margin-bottom: 20px; width: 90%"
              @click="counterPriceSure"
            >
              确认还价
            </div>
          </div> -->
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="dialogVisibleInden = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="counterPriceSure"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
    <tipDialog
      :visible="deleteVisible"
      :right_title="true"
      @dialogClone="deleteTipDialogClone"
      @dialogSubmit="deleteDialogSubmitClone"
    >
      <template slot="content"> 您确定要删除吗？删除后不可恢复 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
    <tipDialog
      :visible="refoundBackVisible"
      :right_title="true"
      class="refoundBackDialog"
      @dialogClone="refoundBackDialogClone"
      @dialogSubmit="refoundBackSubmitClone"
    >
      <template slot="content">
        您确定要撤回议价吗？确定后不可撤销。如已支付意向金，将在30分钟内原路退回
      </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
import {
  getBuyerList,
  negotiaCancel,
  offerPrice,
  negotiaDelete,
} from '@/api/myAssess.js';
import { cancelUserOrder, getOrderTeam } from '@/api/confirmOrder.js';
import { getKfList, getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import isLogin from '@/utils/isLogin';
import myBargainDialog from '@/components/borderDialog/index2.vue';
import util from '@/utils/index';
import tipDialog from '@/components/borderDialog/index3.vue';
export default {
  name: 'Home',
  components: {
    myBargainDialog,
    tipDialog,
  },
  data() {
    return {
      util,
      deleteVisible: false,
      refoundBackVisible: false,
      refoundBackId: '',
      deleteId: '',
      formInline: {
        cname: '',
        productCategoryId: '',
      },
      myBargainDialogLogo: '../../../static/imgs/text_Logwo.svg',
      dialogVisibleInden: false, // 还价弹窗
      counteroffer_price: '', // 还价
      dicker_id: '', // 还价的id
      page: 1,
      totalPage: 10,
      bargainIndex: 0,
      initDate: [],
      listLoading: false,
      negoStatus: '',
      bargainList: [
        {
          name: '查看全部',
          value: '',
        },
        {
          name: '议价中',
          value: 'NEGOTIATING',
        },
        {
          name: '议价成功',
          value: 'NEGOTIATION_SUCCESS',
        },
        {
          name: '议价失败/取消',
          value: 'NEGOTIATION_FAILED_OR_CANCELED ',
        },
      ],
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.fetchData();
  },
  methods: {
    // 搜索
    onSubmit(event) {
      this.page = 1;
      const cname = this.formInline.cname.trim();
      this.fetchData({ negoStatus: this.negoStatus, productSn: cname });
    },
    // 重置搜索
    resetSearch() {
      this.formInline.cname = '';
      this.page = 1;
      this.totalPage = 0;
      this.fetchData({ negoStatus: this.negoStatus });
    },
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    searchList(item, index) {
      this.bargainIndex = index;
      this.negoStatus = item.value;
      this.page = 1;
      // const cname = this.formInline.cname.trim();

      // if (cname) {
      //   this.fetchData({ negoStatus: item.value, productSn: cname });
      // } else {
      //   this.fetchData({ negoStatus: item.value });
      // }
      this.fetchData({ negoStatus: item.value });
    },
    goNegoChat(item){
      const { nim, store } = window.__xkit_store__;
      store.sessionStore.insertSessionActive('team', item.zxkfer);
      this.$store.dispatch('ToggleIM', true);
    },
    goChat(item) {
      // getOrderTeam({
      //   orderId: item.orderId,
      // }).then((res) => {
        getMemberHisKFList({
            cateId: item.productCategoryId,
            productId: item.productId,
          }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', item.orderId);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    canDel(item) {
      return item.status == 3 || item.status == 6;
    },
    delOrder(item) {
      (this.deleteVisible = true), (this.deleteId = item.id);
      // this.$confirm('您确定要删除吗？删除后不可恢复', '二次确认', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确认',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   negotiaDelete(item.id).then((res) => {
      //     if (res.code == 200) {
      //       this.fetchData();
      //       this.$message.success(res.message);
      //     }
      //   });
      // });
    },
    deleteTipDialogClone() {
      this.deleteVisible = false;
    },
    deleteDialogSubmitClone() {
      negotiaDelete(this.deleteId).then((res) => {
        if (res.code == 200) {
          this.fetchData();
          this.deleteVisible = false;
          this.$message.success(res.message);
        }
      });
    },
    canRefoundBack(item) {
      return (
        item.status == -1 ||
        item.status == 0 ||
        item.status == 2 ||
        item.status == 5
      );
    },
    // 确认还价
    counterPriceSure() {
      if (!this.counteroffer_price) {
        this.$message.error('请输入还价金额');
        return;
      }
      if (this.counteroffer_price.length > 9) {
        this.$message.error('最多输入9位数字');
        return;
      }
      this.listLoading = true;
      offerPrice({
        negoId: this.dicker_id,
        price: this.counteroffer_price,
      }).then((res) => {
        this.dialogVisibleInden = false;
        this.listLoading = false;
        if (res.code == 200) {
          this.$message.success('议价回复成功！');
          this.fetchData();
        }
      });
      //   counterofferApi({
      //     dicker_id: this.dicker_id,
      //     counteroffer_price: this.counteroffer_price,
      //   }).then((response) => {
      //     this.listLoading = false;
      //     if (response.code == 200) {
      //       this.dialogVisibleInden = false;
      //       this.$message.success('议价回复成功！');
      //       this.fetchData();
      //     }
      //   });
    },
    dialogClone() {
      this.dialogVisibleInden = false;
    },
    cancelNow(item) {
      this.$confirm('您确定要取消支付？确定后不可撤销', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const { orderId } = item;
        cancelUserOrder(
          {},
          {
            orderId,
          }
        ).then((res) => {
          if (res.code == 200) {
            this.fetchData();
          }
        });
      });
    },
    // 还价弹窗出来
    huanjiaFun(date) {
      this.dicker_id = date.id;
      this.dialogVisibleInden = true;
    },
    // 初始化
    fetchData(item) {
      this.listLoading = true;
      getBuyerList({
        pageNum: this.page,
        pageSize: 10,
        ...item,
      })
        .then((res) => {
          if (res.code == 200) {
            this.totalPage = res.data.total;
            this.initDate = res.data.list;
            this.initDate.forEach((ele) => {
              if (ele.logs) {
                ele.logs = JSON.parse(ele.logs);
              }
            });
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页
    pageFun(val) {
      this.page = `${val}`;
      this.fetchData();
    },
    // 撤回议价
    refoundBack(date) {
      this.refoundBackVisible = true;
      this.refoundBackId = date.id;
      // this.$confirm(
      //   '您确定要撤回议价吗？确定后不可撤销。如已支付意向金，将在30分钟内原路退回',
      //   '提示',
      //   {
      //     closeOnClickModal: false,
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning',
      //   }
      // ).then(() => {
      //   this.listLoading = true;
      //   negotiaCancel({
      //     negoId: date.id,
      //   }).then((res) => {
      //     this.listLoading = false;
      //     if (res.code == 200) {
      //       this.$message({
      //         type: 'success',
      //         message: '操作成功!',
      //       });
      //       this.fetchData();
      //     }
      //   });

      // dickerRevokeApi({
      //   id: date.id,
      //   is_agree: 3,
      // }).then((response) => {
      //   this.listLoading = false;
      //   if (response.code == 200) {
      //     this.$message({
      //       type: 'success',
      //       message: '操作成功!',
      //     });
      //     this.callBackRefound(date.id);
      //   }
      // });
      // });
    },
    refoundBackSubmitClone() {
      negotiaCancel({
        negoId: this.refoundBackId,
      }).then((res) => {
        this.listLoading = false;
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.fetchData();
          this.refoundBackVisible = false;
        }
      });
    },
    refoundBackDialogClone() {
      this.refoundBackVisible = false;
    },
    // callBackRefound(id) {
    //   this.listLoading = true;
    //   dickerQueryRefundApi({
    //     id: id,
    //   }).then((response) => {
    //     this.listLoading = false;
    //     this.fetchData();
    //   });
    // },
    // 删除
    // deletOrder(id) {
    //   this.$confirm('您将删除此数据, 是否继续?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning',
    //   }).then(() => {
    //     this.listLoading = true;
    //     delDickerApi({
    //       id: id,
    //     }).then((response) => {
    //       this.listLoading = false;
    //       if (response.code == 200) {
    //         this.$message({
    //           type: 'success',
    //           message: '操作成功!',
    //         });
    //         this.fetchData();
    //       }
    //     });
    //   });
    // },
    // 账号详情
    payNowOrder(item) {
      this.$router.push({
        path: '/payOrder?orderId=' + item.orderId,
      });
    },
    palyPageDetail(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
    // 跳转支付
    palyPage(date) {
      this.$router.push({
        path: `/confirmOrder?productId=${date.productId}&from=myAssess&negoId=${date.id}&productCategoryId=${date.productCategoryId}`,
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>
<style lang="scss" scoped>
.widthFour {
  /deep/ .el-link {
    cursor: default;
  }
}
.myBargainDialog_priceSureNote {
  margin-top: 12px;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.24px;
}
.myBargainDialog_input {
  background: var(--btn-background-gradient);
  width: 322px;
  height: 52px;
  border-radius: 50px;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    z-index: 0;
    margin: 3px;
    position: absolute;
    z-index: 1;
  }
  /deep/ .el-input__inner {
    border: none;
    width: 318px;
    height: 48px;
    margin-top: 2px;
    border-radius: 50px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    position: relative;
    z-index: 3;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
}
.plDt_btn {
  margin-left: 20px;
  margin-top: 30px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.plDt_btn i {
  margin-right: 4px;
}
.yijia_tit {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}
.widthFive {
  width: 180px;
}

.myBargainDialog_right_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.myBargainDialog {
  /deep/ .el-dialog__body {
    min-height: 300px;
  }
  /deep/ .doalog2_left_logo {
    width: 135px;
    height: 40px;
  }
  /deep/ .el-dialog__body {
    padding: 38px 44px 38px 44px;
  }
  /deep/ .dialogBk {
    width: 237.173px;
    height: 249.494px;
    top: 25px;
    right: 142.83px;
  }
}
.myBargainDialogContent_dialog-footer {
  margin-top: 22px;
  display: flex;
  justify-content: center;
  .btn {
    width: 129px;
    height: 46px;
    border-radius: 60px;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.64px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /deep/ .el-button {
    border: none;
  }
  .btnCancel {
    position: relative;
    z-index: 1;
    background: var(--btn-background-gradient);

    &::before {
      content: '';
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0px;
      bottom: 0px;
      background: #fff;
      margin: 3px;
      border-radius: 60px;
    }
    /deep/ span {
      position: relative;
      z-index: 3;
      background: var(--btn-background-gradient);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
      -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    }
  }
  .btnSubmit {
    background: var(--btn-background-gradient);
    border: 1px solid #ffddbe;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    margin-left: 16px;
  }
}
.refoundBackDialog /deep/ .dialog3_content {
  text-align: left;
}
.refoundBackDialog /deep/ .el-dialog__body {
  min-height: 279px !important;
  height: auto;
}
</style>
