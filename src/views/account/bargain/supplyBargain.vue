<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle account_table_box"
    style="padding: 0px"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <!-- <div
      style="margin-left: 40px; padding-top: 40px"
      class="spaceStart colect_head"
    >
      <div>降价请求</div>
    </div> -->
    <div class="acountList_header">
      <div
        v-for="(item, index) in bargainList"
        :key="index"
        class="acountList_header_box"
      >
        <div
          :class="index === supplyIndex ? 'active' : ''"
          class="title"
          @click="searchList(item, index)"
        >
          {{ item.name }}
        </div>
        <div class="divider"></div>
      </div>
      <!-- <div class="title active">查看全部</div>
      <div class="divider"></div>
      <div class="title">议价中</div>
      <div class="divider"></div>
      <div class="title">议价成功</div>
      <div class="divider"></div>
      <div class="title">议价失败</div> -->
    </div>
    <div
      v-if="initDate && initDate.length"
      style="padding: 0 40px 40px 40px; margin-top: 27px"
    >
      <!-- table start -->
      <div class="orderTable_head spaceBetween">
        <div class="widthOne">商品信息</div>
        <div class="widthTwo">单价</div>
        <div class="widthThree">议价</div>
        <div class="widthFour">状态</div>
        <!-- <div class="widthFive spaceEnd">状态/操作</div> -->
      </div>

      <div
        v-for="(item, index) in initDate"
        :key="index"
        class="orderTable_body"
      >
        <div class="tableBody_head spaceBetween">
          <div>
            <span style="color: #969696">商品编号：</span>{{ item.productSn }}
            <IconFont
              :size="14"
              icon="copy"
              class="accountList_copy"
              @click="copyVal(item.productSn)"
            />
          </div>
          <div>
            <span class="orderTable_left_title">提交时间：</span
            >{{ item.createTime | formatTimetoSS }}
          </div>
        </div>
        <div class="spaceBetween tableBody_box">
          <div class="spaceStart widthOne cursor" @click="palyPage(item)">
            <div class="orderShop_pic">
              <el-image
                :src="item.productPic"
                style="width: 100%; height: 100%"
                fit="cover"
              ></el-image>
            </div>
            <div class="order_goods_box">
              <div>
                <!-- <div class="order_produceSn">【{{ item.productSn }}】</div> -->
                <div style="margin: 0px" class="orderShop_tit text_linTwo">
                  {{ item.productName }}
                </div>
              </div>
              <!-- <div class="orderShop_subT">
                游戏区服：{{ item.gameAccountQufu }}
              </div> -->
              <div class="order_goods_name">
                {{ item.productCategoryName }}
              </div>
            </div>
            <!-- <div>
              <div class="orderShop_tit">{{ item.productSn }}</div>
              <div class="orderShop_subT">{{ item.productCategoryName }}</div>
            </div> -->
          </div>
          <div class="widthTwo order_price">¥ {{ item.originPrice }}</div>
          <div class="widthThree order_price">¥ {{ item.offerPrice }}</div>
          <div class="widthFour goods_status">
            <div v-if="item.status == 0">待回复</div>
            <div v-if="item.status == 1">已接受,待付款</div>
            <div v-if="item.status == 8">议价成功</div>
            <div v-if="item.status == 2 && !item.sellerOfferPrice">已拒绝</div>
            <div v-if="item.status == 2 && item.sellerOfferPrice > 0">
              已还价
            </div>
            <div v-if="item.status == 3">已取消</div>
            <div v-if="item.status == 4">买家已支付全款</div>
            <div v-if="item.status == 5">已拉黑</div>

            <div v-if="item.status == 6 && item.endOrderId">议价成功</div>
            <div v-else-if="item.status == 6">已退款</div>

            <div v-if="item.status == 7">退款中</div>
            <!-- <el-link v-if="item.status == 0" :underline="false" type="primary"
              >待回复</el-link
            >
            <el-link v-if="item.status == 1" :underline="false" type="success"
              >已接受,待付款</el-link
            >
            <el-link v-if="item.status == 8" :underline="false" type="success"
              >议价成功</el-link
            >
            <el-link
              v-if="item.status == 2 && !item.sellerOfferPrice"
              :underline="false"
              type="danger"
              >已拒绝</el-link
            >
            <el-link
              v-if="item.status == 2 && item.sellerOfferPrice > 0"
              :underline="false"
              type="danger"
              >已还价</el-link
            >
            <el-link v-if="item.status == 3" :underline="false" type="info"
              >已取消</el-link
            >
            <el-link v-if="item.status == 4" :underline="false" type="success"
              >买家已支付全款</el-link
            >
            <el-link v-if="item.status == 5" :underline="false" type="danger"
              >已拉黑</el-link
            >

            <el-link
              v-if="item.status == 6 && item.endOrderId"
              :underline="false"
              type="success"
              >议价成功</el-link
            >
            <el-link v-else-if="item.status == 6" :underline="false" type="info"
              >已退款</el-link
            >

            <el-link v-if="item.status == 7" :underline="false" type="info"
              >退款中</el-link
            > -->
          </div>
        </div>
        <div
          class="spaceEnd"
          style="margin-right: 41.13px; margin-bottom: 20.56px"
        >
          <div
            v-if="canDel(item)"
            class="plain cancel_favorites"
            @click="delOrder(item)"
          >
            删除
          </div>
          <div
            v-if="canCancel(item)"
            class="plain cancel_favorites"
            @click="doCancel(item)"
            style="margin-left: 20px;"
          >
            取消
          </div>
          <div
             v-if="item.status == 0"
            class="orderTable_btn solid accountOrderBtn"
            @click="agreeAssessFun(item)"
          >
            同意
          </div>
          <div
            v-if="item.status == 0"
            class="orderTable_btn solid accountOrderBtn"
            @click="huanjiaFun(item)"
          >
            还价
          </div>
          <el-button
            v-if="item.status == 0"
            class="reprovision_btn plain accountOrderBtn"
            @click="disAgreeAssessFun(item)"
          >
            拒绝
          </el-button>
          <!-- <div
              v-if="item.status == 0"
              class="orderTable_btn plain"
              @click="disAgreeAssessFun2(item)"
            >
              拉黑
            </div> -->

          <!-- <div class="orderTable_btn solid accountOrderBtn" @click="goChat(item)">联系客服</div> -->
          <div class="orderTable_btn solid accountOrderBtn" v-if="item.zxkfer && util.isNumber(item.zxkfer)" @click="goNegoChat(item)">议价群</div>
        </div>
        <!-- <div
          v-if="item.dicker_log && item.dicker_log.length > 0"
          class="tableBody_head"
          style="padding: 6px 20px"
        >
          <div class="topTips_tit yijia_tit">议价记录</div>
          <div class="topTips_con">
            <div v-for="(itemS, indexS) in item.dicker_log" :key="indexS">
              <span v-if="itemS.role == 'seller'">卖家</span>
              <span v-else>买家</span>
              于 {{ itemS.created_at }} 新增议价：￥{{
                itemS.counteroffer_price
              }}；
            </div>
          </div>
        </div> -->
        <div v-if="item.offerPrice" class="tableBody_head spaceAlignCenter">
          <!-- <el-tooltip
            :disabled="item.logs.length == 0"
            class="item"
            effect="dark"
            placement="top-start"
          >
            <div slot="content">
              <div v-for="ele in item.logs">{{ ele }}</div>
            </div> -->
          <div>
            <span class="orderTable_left_title">买家</span
            ><span class="buyerUsername">{{ item.buyerUsername }}</span
            ><span class="orderTable_left_title">最近报价：</span>¥{{
              item.offerPrice
            }}
          </div>
          <div v-if="item.sellerOfferPrice" style="margin-left: 10px">
            <span class="orderTable_left_title">卖家</span
            ><span class="" style="margin-right: 10px">{{
              item.sellerUsername
            }}</span
            ><span class="orderTable_left_title">最近报价：</span>¥{{
              item.sellerOfferPrice
            }}
          </div>
          <!-- </el-tooltip> -->
        </div>
      </div>
      <!-- tabler end -->

      <div class="tabler_footer_pagination">
        <el-pagination
          :total="totalPage"
          layout="pager,jumper"
          class="search_page_pagination"
          @current-change="pageFun"
        >
        </el-pagination>
      </div>
    </div>

    <div v-else class="list_null_sorry" style="margin-top: 3px">
      <img
        style="width: 54px; height: 56px"
        src="../../../../static/imgs/null.png"
        alt=""
      />
      <div style="margin-left: 15.85px">
        <img
          style="width: 63px; height: 36px"
          src="../../../../static/imgs/sorry_text.svg"
          alt=""
        />
        <div class="sorry_text">暂时无相关数据</div>
      </div>
    </div>
    <!-- 讨价还价 -->
    <!-- <el-dialog
      :visible.sync="dialogVisibleInden"
      width="30%"
      center
      title="还价"
    >
      <div class="priceSureNote">如买家同意还价金额，不能无责取消</div>
      <el-input
        v-model="counteroffer_price"
        style="color: #333"
        placeholder="请输入还价金额"
      />
      <div class="spaceCenter">
        <div
          class="spaceCenter plDt_btn"
          style="margin-bottom: 20px; width: 90%"
          @click="counterPriceSure"
        >
          确认还价
        </div>
      </div>
    </el-dialog> -->
    <myBargainDialog
      :visible="dialogVisibleInden"
      :dialog-width="'523px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="myBargainDialog_right_title">还价</span>
      </template>
      <template slot="content">
        <div style="width: 322px; margin: 0 auto; text-align: center">
          <el-input
            v-model="counteroffer_price"
            class="myBargainDialog_input"
            style="margin-top: 36px; width: 322px"
            placeholder="请输入还价金额"
          />
          <div class="myBargainDialog_priceSureNote">
            如卖家同意还价金额，不能无责取消
          </div>
          <div class="counteroffer_price_box">
            <div>
              <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{counteroffer_price}}?
            </div>
            <div class="agreeWithOfferRadioPriceBox">
              <el-radio v-model="counterofferRadio" label="1"><span>是的，我要同步</span><el-tag style="margin-left: 5px;" size="mini" type="danger"
              >更快出售</el-tag
            ></el-radio>
              <el-radio style="margin-top: 5px;" v-model="counterofferRadio" label="0"><span>暂不同步</span></el-radio>
            </div>
          </div>
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="dialogVisibleInden = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="counterPriceSure"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
    <myBargainDialog
      :visible="agreeWithVisible"
      :dialog-width="'523px'"
      :logo="myBargainDialogLogo"
      class="myBargainDialog"
      @dialogClone="agreeWithVisible=false"
    >
      <template slot="right_title">
        <span style="position: relative;z-index: 10;" class="myBargainDialog_right_title">同意报价</span>
      </template>
      <template slot="content">
        <div style="padding-top: 20px;">
          <div style="width: 100%;color: #000;font-size: 16px;" class="orderShop_tit text_linTwo">{{agreeWithVisibleDate && agreeWithVisibleDate.productName}}</div>
          <div style="margin: 10px 0px 10px 0px;color: #9a9a9a;">{{agreeWithVisibleDate && agreeWithVisibleDate.productCategoryName}}</div>
          <div class="agreeWithOfferPriceBox">
            <div class="title">买家当前报价:</div>
            <div class="offerPrice">¥{{agreeWithVisibleDate.offerPrice}}</div>
          </div>
          <div style="margin-top: 20px;font-size: 16px;">
            <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{agreeWithVisibleDate.offerPrice}}?
            <div class="agreeWithOfferRadioBox">
              <el-radio v-model="radio" label="1"><span>是的，我要同步</span><el-tag style="margin-left: 5px;" size="mini" type="danger"
              >更快出售</el-tag
            ></el-radio>
              <el-radio style="margin-top: 5px;" v-model="radio" label="0"><span>暂不同步</span></el-radio>
            </div>
          </div>
          <div class="myBargainDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="agreeWithVisible = false"
              >再考虑一下</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="agreeWithClick"
              >同意报价</el-button
            >
          </div>
        </div>
      </template>
    </myBargainDialog>
    <tipDialog
      :visible="deleteVisible"
      :right_title="true"
      @dialogClone="deleteTipDialogClone"
      @dialogSubmit="deleteDialogSubmitClone"
    >
      <template slot="content"> 您确定要删除吗？删除后不可恢复 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
import { getOrderTeam } from '@/api/confirmOrder.js';
import isLogin from '@/utils/isLogin';
import util from '@/utils/index';
import { getKfList, getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import myBargainDialog from '@/components/borderDialog/index2.vue';
import tipDialog from '@/components/borderDialog/index3.vue';
import {
  getSellerList,
  sellerDo,
  sellerOfferPrice,
  negotiaDelete,
} from '@/api/myAssess.js';

export default {
  name: 'Home',
  components: {
    myBargainDialog,
    tipDialog,
  },
  data() {
    return {
      util,
      deleteVisible: false,
      agreeWithVisible:false,
      radio:'0',
      counterofferRadio:'1',
      agreeWithVisibleDate:{},
      deleteId: '',
      myBargainDialogLogo: '../../../static/imgs/text_Logo.svg',
      page: 1,
      dialogVisibleInden: false, // 还价弹窗
      counteroffer_price: '', // 还价
      dicker_id: '', // 还价的id
      totalPage: 10,
      listLoading: false,
      initDate: [], // 数据
      negoStatus: '',
      bargainList: [
        {
          name: '查看全部',
          value: '',
        },
        {
          name: '议价中',
          value: 'NEGOTIATING',
        },
        {
          name: '议价成功',
          value: 'NEGOTIATION_SUCCESS',
        },
        {
          name: '议价失败/取消',
          value: 'NEGOTIATION_FAILED_OR_CANCELED ',
        },
      ],
      supplyIndex: 0,
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.fetchData();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    goNegoChat(item){
      const { nim, store } = window.__xkit_store__;
      store.sessionStore.insertSessionActive('team', item.zxkfer);
      this.$store.dispatch('ToggleIM', true);
    },
    goChat(item) {
      // getOrderTeam({
      //   orderId: item.orderId,
      // }).then((res) => {
        getMemberHisKFList({
            cateId: item.productCategoryId,
            productId: item.productId,
          }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', item.orderId);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    canCancel(item) {
      return (
        item.status == 1 || (item.status == 2 && item.sellerOfferPrice > 0)
      );
      // return (
      //   (item.status == 1 || item.status == 2) &&
      //   item.responseTime &&
      //   util.isMoreThan12HoursFromNow(item.responseTime)
      // );
    },
    doCancel(date) {
      this.$confirm('您确定要取消吗？取消后不可恢复', '二次确认', {
        closeOnClickModal: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.replayFun(date, 2, 2);
      });
    },
    canDel(item) {
      return item.status == -1 || item.status == 3 || item.status == 6;
    },
    delOrder(item) {
      this.deleteVisible = true;
      this.deleteId = item.id;
      // this.$confirm('您确定要删除吗？删除后不可恢复', '二次确认', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确认',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   negotiaDelete(item.id).then((res) => {
      //     if (res.code == 200) {
      //       this.fetchData();
      //       this.$message.success(res.message);
      //     }
      //   });
      // });
    },
    deleteTipDialogClone() {
      this.deleteVisible = false;
    },
    deleteDialogSubmitClone() {
      negotiaDelete(this.deleteId).then((res) => {
        if (res.code == 200) {
          this.fetchData();
          this.deleteVisible = false;
          this.$message.success(res.message);
        }
      });
    },
    searchList(item, index) {
      this.supplyIndex = index;
      this.negoStatus = item.value;
      this.page = 1;
      this.fetchData({ negoStatus: item.value });
    },
    // 确认还价
    counterPriceSure() {
      if (!this.counteroffer_price) {
        this.$message.error('请输入还价金额');
        return;
      }
      if (this.counteroffer_price.length > 9) {
        this.$message.error('最多输入9位数字');
        return;
      }
      this.listLoading = true;
      sellerOfferPrice({
        negoId: this.dicker_id,
        price: this.counteroffer_price,
        changePrice:this.counterofferRadio=='1'?1:0
      }).then((res) => {
        this.listLoading = false;
        this.dialogVisibleInden = false;
        if (res.code == 200) {
          this.$message.success('议价回复成功！');
          this.fetchData();
        }
      });
      // counterofferApi({
      //   dicker_id: this.dicker_id,
      //   counteroffer_price: this.counteroffer_price,
      // }).then((response) => {
      //   this.listLoading = false;
      //   if (response.code == 200) {
      //     this.dialogVisibleInden = false;
      //     this.$message.success('议价回复成功！');
      //     this.fetchData();
      //   }
      // });
    },
    // 还价弹窗出来
    huanjiaFun(date) {
      this.counterofferRadio='1'
      this.dicker_id = date.id;
      this.dialogVisibleInden = true;
    },
    // 初始化
    fetchData() {
      this.listLoading = true;
      getSellerList({
        pageNum: this.page,
        pageSize: 10,
        negoStatus: this.negoStatus,
      })
        .then((res) => {
          if (res.code == 200) {
            const { list, total } = res.data;
            this.initDate = list;
            this.initDate.forEach((ele) => {
              if (ele.logs) {
                ele.logs = JSON.parse(ele.logs);
              }
            });
            this.totalPage = total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页
    pageFun(val) {
      this.page = `${val}`;
      this.fetchData();
    },
    // 确认报价
    agreeAssessFun(date) {
      this.radio='0'
      this.agreeWithVisible=true
      this.agreeWithVisibleDate=date
      // var that = this;
      // this.$confirm('您确定同意当前报价吗？确定后不可撤销', '温馨提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   that.replayFun(date, 1, 1);
      // });
    },
    agreeWithClick(){
      this.replayFun(this.agreeWithVisibleDate, 1, 1,this.radio==1?true:false);
    },
    disAgreeAssessFun2(date) {
      var that = this;
      this.$confirm(
        '您确定拒绝并拉黑吗？拒绝拉黑后，系统将帮您自动拒绝该买家对本商品的报价。',
        '温馨提示',
        {
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        that.replayFun(date, 2, 1);
      });
    },
    dialogClone() {
      this.dialogVisibleInden = false;
    },
    // 拒绝报价
    disAgreeAssessFun(date) {
      var that = this;
      this.$confirm('您确定拒绝当前报价吗？拒绝后不可撤销', '温馨提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        that.replayFun(date, 2, 0);
      });
    },
    // 回复
    replayFun(date, num, rejectedState,agreeWithFlag=false) {
      this.listLoading = true;
      let data = {
        negoId: date.id,
        status: num,
      };
      if(agreeWithFlag){
        data.changePrice=1
      }
      if (rejectedState || rejectedState == 0) {
        data.rejectedState = rejectedState;
      }
      sellerDo(data)
        .then((res) => {
          if (res.code == 200) {
            this.fetchData();
            this.agreeWithVisible=false
          }
        })
        .finally(() => {
          this.listLoading = false;
        });

      // sellerReplyApi({
      //   id: date.id,
      //   seller_id: date.seller_id,
      //   is_agree: num,
      // }).then((response) => {
      //   this.listLoading = false;
      //   if (response.code == 200) {
      //     this.$message.success('议价回复成功！');
      //     if (num == 2) {
      //       this.callBackRefound(date.id);
      //       return;
      //     }
      //     this.fetchData();
      //   }
      // });
    },
    // callBackRefound(id) {
    //   this.listLoading = true;
    //   dickerQueryRefundApi({
    //     id: id,
    //   }).then((response) => {
    //     this.listLoading = false;
    //     if (response.code == 200) {
    //       this.fetchData();
    //     }
    //   });
    // },
    // 账号详情
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>
<style lang="scss" scoped>
.myBargainDialog_priceSureNote {
  margin-top: 12px;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.24px;
}
.myBargainDialog_input {
  background: var(--btn-background-gradient);
  width: 322px;
  height: 52px;
  border-radius: 50px;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    z-index: 0;
    margin: 3px;
    position: absolute;
    z-index: 1;
  }
  /deep/ .el-input__inner {
    border: none;
    width: 318px;
    height: 48px;
    margin-top: 2px;
    border-radius: 50px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    position: relative;
    z-index: 3;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
}
.widthFour {
  /deep/ .el-link {
    cursor: default;
  }
}
.buyerUsername {
  color: #ff7a00;
  margin-right: 10px;
}
.el-tooltip__popper {
  width: 200px;
  background: rgba(0, 0, 0, 0.8);
  padding-bottom: 15px;
}

.plDt_btn {
  margin-left: 20px;
  margin-top: 30px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.plDt_btn i {
  margin-right: 4px;
}
.yijia_tit {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}
.widthFive {
  width: 180px;
}
.orderTable_btn {
  // margin-bottom: 10px;
}

.myBargainDialog_right_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.myBargainDialog {
  /deep/ .el-dialog__body {
    min-height: 300px;
  }
  /deep/ .doalog2_left_logo {
    width: 135px;
    height: 40px;
  }
  /deep/ .el-dialog__body {
    padding: 38px 44px 38px 44px;
  }
  /deep/ .dialogBk {
    width: 237.173px;
    height: 249.494px;
    top: 25px;
    right: 142.83px;
  }
}
.myBargainDialogContent_dialog-footer {
  margin-top: 22px;
  display: flex;
  justify-content: center;
  .btn {
    width: 129px;
    height: 46px;
    border-radius: 60px;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.64px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /deep/ .el-button {
    border: none;
  }
  .btnCancel {
    position: relative;
    z-index: 1;
    background: var(--btn-background-gradient);

    &::before {
      content: '';
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0px;
      bottom: 0px;
      background: #fff;
      margin: 3px;
      border-radius: 60px;
    }
    /deep/ span {
      position: relative;
      z-index: 3;
      background: var(--btn-background-gradient);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
      -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    }
  }
  .btnSubmit {
    background: var(--btn-background-gradient);
    border: 1px solid #ffddbe;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    margin-left: 16px;
  }
}
.agreeWithOfferPriceBox{
  width: 100%;
  height: 40px;
  border-radius: 10px;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  font-family: PingFang Sc;
  font-size: 16px;
  padding-left: 10px;
  .title{
  }
  .offerPrice{
    color: red;
  }
}
.counteroffer_price_box{
  margin-top: 20px;font-size: 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .agreeWithOfferRadioPriceBox{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 10px;
  }
}

.agreeWithOfferRadioBox{
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}
</style>
