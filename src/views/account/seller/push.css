.pushAccount_box {
  border-radius: 24px;
  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
  padding: 42px 40px 39px 35px;
}
.psuhAccount_title {
  margin-bottom: 20px;
  width: 118px;
}
.btnSe_con {
  /* background: url(../../../../static/imgs/btn_bk2x.png) !important; */
  /* background-size: 100% 100% !important; */
  background: var(--btn-background-gradient) !important;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  border: 1px solid #ffddbe !important;
  padding: 0px 48px !important;
  font-family: PingFang SC;
  font-weight: 500;
  letter-spacing: 0.72px;
  border-radius: 60px !important;
  color: #fff;
  font-size: 18px;
  height: 50px;
  /* background: red !important; */
}
.cengji_wrap {
  width: 100%;
}
.ceng_item {
  width: 208px;
  height: 476px !important;
  background: #ffffff;
  /*border: 1px solid #DCDCDC;*/
  border-radius: 20px;
  margin-right: 20px;
  box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.15);
}
.ceng_item:last-child {
  margin-right: 0px;
}
.ceng_header {
  font-size: 26px;
  font-weight: 500;
  height: 60px;
  color: #222222;
  text-align: center;
  padding: 20px 0px 0px 40px;
  /* border: 1px solid #dcdcdc; */
  border-bottom: none;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  /* text-align: left; */
  /* text-indent: 34.28px; */
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
}
.title_text {
  display: block;
  width: 146px;
  height: 22px;
  line-height: 22px;

  text-align: left;
  font-family: YouSheBiaoTiHei;
  font-size: 26px;
  font-style: normal;
  font-weight: 400;

  color: #ff9d9d;
}
.ceng_header.noBoder {
  /* border: none; */
  border-radius: 24px 24px 0px 0px;
}

/* .ceng_body::-webkit-scrollbar {
  background: red !important;
  width: 7px !important;
}
.ceng_body::-webkit-scrollbar-track {
  background: red !important;
  background-color: transparent !important;
}
.ceng_body::-webkit-scrollbar-thumb {
  border-radius: 24px !important;
  background: red !important;
  opacity: 0.3 !important;
  height: 38px !important;
} */
/* 针对类名为ceng_body的元素内的滚动条进行样式设置 */

.ceng_body {
  width: 100%;
  height: 385px;
  box-sizing: border-box;
  overflow-y: scroll;
  font-size: 16px;
  font-weight: 500;
  color: #969696;
  font-family: 'PingFang SC';
  margin-top: 16px;
}

.cengC_item {
  box-sizing: border-box;
  width: 168px;
  height: 53px;
  display: flex;
  align-items: center;
  padding: 0px 19px;
  cursor: pointer;
  transition: all 0.3s;
  margin-left: 21px;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.64px;
}
.cengC_item > .el-icon-caret-right {
  display: none;
  font-size: 20px;
  width: 18px;
}
.cengC_item:hover,
.cengC_item.active {
  background: #fdf5ed;
  /* color: #ff7000; */

  color: #ff720c;
}

.cengC_item:hover > .el-icon-caret-right,
.cengC_item.active > .el-icon-caret-right {
  display: block;
  font-size: 16px;
}
.choosed_wrap {
  background: #f2f2f2;
  height: 46px;
  border-radius: 24px;
  box-sizing: border-box;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  /* padding: 15px; */
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC';
  color: #969696;
  letter-spacing: 0.64px;
  margin-top: 20px;
  margin-bottom: 40px;
}

.text_value {
  margin-left: 20px;
}
.remove_select_value {
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
  margin-right: 20px;
  cursor: pointer;
}
.restart_chos {
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
  cursor: pointer;
  margin-right: 20px;
}
.userC_form {
  width: 61%;
  /* margin: 0 auto; */
  padding-top: 0px;
}
.formType_name {
  color: #1b1b1b;
  margin-bottom: 40px;
  margin-top: 40px;
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.56px;
}
.goods_cover_upload .picUpload_wrap {
  width: 191.11px;
  height: 93.413px;
  border-radius: 20.567px;
  /* border: 1px dashed #969696; */
  position: relative;
  text-align: center;
  line-height: 93.413px;
  font-size: 28px;
  color: #969696;
  /* overflow: hidden; */
  background: #f7f7f7;
}
.goods_cover_upload1 .picUpload_wrap {
  width: 191.11px;
  height: 93.413px;
  border-radius: 12px !important;
  border: none !important;
}
.goods_cover_upload .el-upload-list__item {
  width: 100%;
  height: 93.413px;
  border: none;
}
.goods_cover_upload .el-upload-list__item-status-label {
  width: 25px;
  height: 25px;
  background: url(../../../../static/imgs/uploadList_right_icon.png);
  background-size: 25px 25px;
  position: absolute;
  right: 0px;
  top: 0px;
  transform: rotate(0deg);
}
.goods_cover_upload .el-upload-list__item-status-label .el-icon-upload-success {
  display: none;
}
.goods_cover_upload .picUpload_wrap .el-upload--picture-card {
  width: 191.11px;
  height: 93.413px;
  line-height: 93.413px;
  background: #f7f7f7;
  border: 1px dashed #969696;
  border-radius: 20.567px;
}
.goods_details_upload .picUpload_wrapSmall {
  width: 94.27px;
  height: 94.27px;
  border-radius: 20.567px;
  border: 1px dashed #969696;
  position: relative;
  text-align: center;
  line-height: 94.27px;
  font-size: 28px;
  color: #969696;
  overflow: hidden;
  float: left;
  background: #f7f7f7;
  margin-right: 15.426px;
  /* margin-bottom: 15px; */
}
.goods_details_upload .picUpload_wrapSmall_img_list {
  border-radius: 12px !important;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.picUpload_pic {
  position: absolute;
  z-index: 1;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  width: 100%;
  height: 100%;
}
/* .delet_item {
  position: absolute;
  width: 30px;
  height: 30px;
  z-index: 10;
  right: 0px;
  top: 0px;
  color: #fff;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  cursor: pointer;
} */
.introduce_push {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #812e1e;
  line-height: 22px;
  background: #fff4ee;
  box-sizing: border-box;
  padding: 15px 10px;
}
/****************************************/
.teding_name {
  word-break: keep-all;
  font-size: 14px;
  color: #606266;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  border: 1px solid #dcdfe6;
  width: 140px;
}
.teding_name_com {
  word-break: keep-all;
  font-size: 14px;
  color: #1b1b1b;
  height: 38px;
  line-height: 38px;
  width: 117.4px;
  font-weight: 400;
  text-align: center;
  font-family: 'PingFang SC';
  letter-spacing: 0.64px;
}
.right_tedian {
  width: 630px;
}
.right_tedian_com {
  /* width: 630px; */
  flex: 1;
}
.tedian_item {
  display: flex;
  align-items: start;
  justify-content: space-between;
  margin-bottom: 15px;
}
.tedian_item_com {
  display: flex;
  margin-bottom: 12px;
}
.tedian_container {
  padding: 10px 10px 4px;
  border: 1px solid #dcdfe6;
  flex-wrap: wrap;
  background: #f7f7f7;
}
.tedian_container_com {
  /* padding: 10px 10px 4px; */
  /* border: 1px solid #dcdfe6; */
  flex-wrap: wrap;
  margin-top: 17.14px;
  /* background: #f7f7f7; */
}
.tedian_itemChoose {
  font-size: 14px;
  color: #606266;
  line-height: normal;
  border-radius: 4px;
  padding: 6px 10px;
  margin-right: 6px;
  margin-bottom: 6px;
  border: 1px solid #dcdfe6;
  flex-shrink: 0;
  transition: all 0.3s;
  cursor: pointer;
}
.tedian_itemChoose_com {
  font-size: 14px;
  font-family: 'PingFang SC';
  color: rgba(0, 0, 0, 0.4);
  letter-spacing: 0.64px;
  font-weight: 400px;
  line-height: normal;
  border-radius: 17.14px;
  padding: 6.856px 18.854px;
  margin-right: 10.283px;
  margin-bottom: 6px;
  flex-shrink: 0;
  transition: all 0.3s;
  background: #ebebeb;
  cursor: pointer;
}
.tedian_Choose_small {
  font-size: 13px;
  color: #606266;
  line-height: normal;
  border-radius: 4px;
  padding: 4px 6px;
  margin-right: 6px;
  margin-bottom: 6px;
  border: 1px solid #dcdfe6;
  flex-shrink: 0;
  margin-top: 6px;
  margin-right: 6px;
}
.tedian_Choose_small.active,
.tedian_itemChoose:hover,
.tedian_itemChoose.active {
  color: #ff6716;
  border-color: #ff6716;
  background: #fff;
}
.tedian_Choose_small_com {
  font-size: 13.712px;
  color: #fff;
  line-height: normal;
  border-radius: 17.14px;
  flex-shrink: 0;
  margin-right: 10.283px;
  margin-bottom: 6.856px;
}

.tedian_Choose_small_com .content {
  /* margin-top: 6.856px; */
  padding: 6.856px 18.854px;
}
.tedian_Choose_small_com.active,
.tedian_itemChoose_com:hover,
.tedian_itemChoose_com.active {
  color: #fff;
  background: #ff7a00;
}
.tedian_container_content_box_input .el-input__inner {
  width: 100% !important;
}
.right_tedian .el-collapse-item__arrow {
  margin-left: -34px !important;
  margin-top: 0px !important;
  font-size: 22px;
  z-index: 1;
}
.searchIpt_done {
  width: 100%;
  border-radius: 4px;
  min-height: 40px;
  line-height: 40px;
  padding: 0 10px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
}
.searchIpt_done_com {
  width: 100%;
  border-radius: 85.7px;
  /* min-height: 46.278px; */
  /* line-height: 46.278px; */
  min-height: 38px;
  line-height: 0;
  display: flex;
  align-items: center;
  padding: 0px 17.14px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  position: relative;
  box-sizing: border-box;
}
.tedian_container_content_box {
  margin-top: 13.712px;
  border-radius: 20.56px;
  padding: 17.14px 20.56px;
  background: #f6f6f6;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.down_arror_tedian,
.up_arror_tedian {
  font-size: 24px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 8px;
}
.down_arror_tedian,
.up_arror_tedian_com {
  font-size: 18px;
  cursor: pointer;
  position: absolute;
  right: 16px;
  /* top: 14px; */
  top: 50%;
  transform: translateY(-50%);
}
.searchList_wrap {
  display: block;
  width: 100%;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 4px;
}
.searchList_wrap_com {
  display: block;
  width: 100%;
  margin-bottom: 10px;
}

.pushAccount_popover {
  background: transparent;
  box-shadow: none;
  padding: 0;
  border: none;
}
.pushAccount_popover .popper__arrow {
  display: none;
}
.tianran_span_text:hover {
  background: transparent !important;
}
