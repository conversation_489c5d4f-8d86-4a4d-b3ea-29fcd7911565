<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="
      border-radius: 24px;
      padding: 0px 0 39px 0; /* margin-bottom: 124.265px; */
    "
  >
    <!-- <div class="acountList_header">
      <div class="title">账号列表</div>
    </div> -->
    <!-- <div class="orderType_cont spaceStart borderBottom">
      <div
        v-for="(item, index) in statusDate"
        :class="status == item.id ? 'active' : ''"
        :key="index"
        class="orderType_item"
        @click="statusChoose(item)"
      >
        {{ item.name }}
      </div>
    </div> -->
    <div class="acountList_header">
      <div
        v-for="(item, index) in statusDate2"
        :key="index"
        class="acountList_header_box"
      >
        <div
          :class="status == item.id ? 'active' : ''"
          class="title"
          @click="statusChoose(item)"
        >
          {{ item.name }}
        </div>
        <div class="divider"></div>
      </div>
    </div>
    <!-- search -->
    <div class="accountList_orderSearch_cont">
      <el-form
        :inline="true"
        :model="formInline"
        style="display: flex; align-items: center"
        class="demo-form-inline"
        @submit.native.prevent="onSubmit()"
      >
        <el-form-item label="商品编号">
          <el-input v-model="formInline.cname" placeholder="请输入商品编号">
            <iconFont
              slot="suffix"
              :size="24"
              icon="web_search"
              color="#000000"
              style="margin-right: 14.16px; margin-top: 0px; cursor: pointer"
              @click="onSubmit"
          /></el-input>
        </el-form-item>
        <el-form-item>
          <!-- <el-button type="primary" round class="btnSe_con" @click="onSubmit"
            >搜索</el-button
          > -->
          <el-button
            type="primary"
            plain
            round
            class="reprovision_btn reprovision_reset_btn"
            @click="resetSearch"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div style="padding: 0px 40px">
      <div v-if="initDate.length">
        <div style="margin-top: 27px" class="orderTable_head spaceBetween">
          <div class="widthOne">商品信息</div>
          <div class="widthTwo">单价</div>
          <div class="widthThree">底价</div>
          <div class="widthFour">订单状态</div>
        </div>

        <div
          v-for="(item, index) in initDate"
          :key="index"
          class="orderTable_body"
        >
          <div class="tableBody_head spaceBetween">
            <div>
              <span class="orderTable_left_title">商品编号：</span
              >{{ item.productSn }}
              <IconFont
                :size="14"
                icon="copy"
                class="accountList_copy"
                @click="copyVal(item.productSn)"
              />

              <!-- {{ getState(item) }}
            <el-popover
              v-if="item.verifyStatus == 2"
              :content="item.verifyDetail"
              placement="top"
              title="审核失败"
              width="400"
              trigger="hover"
            >
              <i slot="reference" class="el-icon-question verifyFailIcon"></i>
            </el-popover> -->
            </div>
            <div>
              <span class="orderTable_left_title">发布时间：</span
              >{{ item.createTime | formatTime }}
            </div>
          </div>
          <div class="spaceBetween tableBody_box">
            <div class="spaceStart widthOne">
              <div class="orderShop_pic">
                <el-image
                  v-if="item.pic"
                  :src="item.pic"
                  style="width: 100%; height: 100%"
                  fit="cover"
                ></el-image>
                <el-image
                  v-else
                  style="width: 100%; height: 100%"
                  src="../../../static/loading.png"
                  fit="cover"
                ></el-image>
              </div>
              <div
                class="order_goods_box"
                style="cursor: pointer"
                @click="goSellDetail(item)"
              >
                <div>
                  <!-- <div class="order_produceSn">【{{ item.productSn }}】</div> -->
                  <div style="margin: 0" class="orderShop_tit text_linTwo">
                    {{ item.subTitle }}
                  </div>
                </div>
                <!-- <div class="orderShop_subT">
                游戏区服：{{ item.gameAccountQufu }}
              </div> -->
                <div class="order_goods_name">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div>
            <div class="widthTwo order_price">¥ {{ item.price }}</div>
            <div class="widthThree order_price">¥{{ item.originalPrice }}</div>
            <div
              class="widthFour spaceEnd goods_status"
              style="flex-wrap: wrap"
            >
              {{ getState(item) }}
              <el-popover
                v-if="item.verifyStatus == 2"
                :content="item.verifyDetail"
                placement="top"
                title="审核失败"
                width="400"
                trigger="hover"
              >
                <i slot="reference" class="el-icon-question verifyFailIcon"></i>
              </el-popover>
            </div>
          </div>
          <div v-if="item.state == -1" class="refuse_reson">
            拒绝原因：{{ item.reason }}
          </div>
          <!-- 操作 -->
          <div
            class="spaceEnd"
            style="margin-right: 41.13px; margin-bottom: 20.56px"
          >
            <!-- <div
              v-if="item.productCategoryId == 75"
              class="orderTable_btn solid"
              @click="goSimilarList(item)"
            >
              相似成交
            </div> -->

            <!-- <div
            v-if="item.qrcode"
            class="orderTable_btn solid"
            @click="checkCode(item.qrcode)"
          >
            二维码
          </div> -->

            <!-- <div
            v-if="item.productCategoryId == 75"
            class="orderTable_btn solid"
            @click="goSimilarList(item)"
          >
            相似成交
          </div> -->

            <div
              v-if="canDel(item) && !uploadIng(item)"
              class="plain cancel_favorites"
              style="margin-right: 14px"
              @click="deleteProduct(item)"
            >
              删除商品
            </div>
            <el-button
              v-if="item.productStatus === 'ON_SHELF' "
              class="reprovision_btn plain accountOrderBtn"
              @click="polishClick(item)"
            >
              擦亮
            </el-button>
            <!-- <div
            v-if="canEdit2(item) && !uploadIng(item)"
            class="orderTable_btn solid"
            @click="editAcc2(item)"
          >
            重新认证
          </div> -->
            <el-button
              v-if="
                item.productStatus === 'ON_SHELF' &&
                item.productCategoryId === 75
              "
              class="reprovision_btn plain accountOrderBtn"
              @click="goSimilarList(item)"
            >
              相似成交
            </el-button>
            <el-button
              v-if="canEdit(item) && !uploadIng(item)"
              class="reprovision_btn plain accountOrderBtn"
              @click="updatePrice(item)"
            >
              改价
            </el-button>
            <el-button
              v-if="canDown(item) && !uploadIng(item)"
              class="reprovision_btn plain accountOrderBtn"
              @click="delePushFun(item)"
            >
              下架
            </el-button>
            <el-button
              v-if="canUp(item) && !uploadIng(item)"
              class="reprovision_btn plain accountOrderBtn"
              @click="editAcc(item)"
            >
              上架
            </el-button>
            <el-button
              v-if="canEdit(item)"
              class="reprovision_btn plain accountOrderBtn"
              @click="goQuestion(item)"
            >
              完善常见问题
            </el-button>
            <!-- v-if="item.pushType != 1 && canEdit(item) && !uploadIng(item)" -->
            <div
              v-if="
               item.pushType!==1&&(['OFF_SHELF'].includes(item.productStatus) || ( item.productStatus==='ON_SHELF' && item.gameGoodsSaletype===3))
              "
              class="orderTable_btn solid accountOrderBtn"
              @click="editAcc(item)"
            >
              编辑
            </div>
            <!-- <div
              class="orderTable_btn solid accountOrderBtn"
              @click="addImage(item)"
              v-if="
               item.pushType!==1
              "
            >
              追加图片
            </div> -->
            <div
              class="orderTable_btn solid accountOrderBtn"
              @click="goChat(item)"
            >
              联系客服
            </div>
          </div>
        </div>
        <!-- tabler end -->

        <div class="tabler_footer_pagination">
          <el-pagination
            :total="totalPage"
            layout="pager,jumper"
            class="search_page_pagination"
            @current-change="pageFun"
          >
          </el-pagination>
        </div>
      </div>
      <div v-else class="list_null_sorry" style="margin-top: 3px">
        <img
          style="width: 54px; height: 56px"
          src="../../../../static/imgs/null.png"
          alt=""
        />
        <div style="margin-left: 15.85px">
          <img
            style="width: 63px; height: 36px"
            src="../../../../static/imgs/sorry_text.svg"
            alt=""
          />
          <div class="sorry_text">暂时无相关数据</div>
        </div>
      </div>
    </div>
    <!-- 认证 -->
    <el-dialog
      :visible.sync="dialogVisibleCode"
      width="30%"
      center
      title="查看二维码"
    >
      <div class="spaceCenter">
        <img :src="qrCode" style="width: 300px" />
      </div>
    </el-dialog>
    <editPriceDialog
      :visible="priceFormDialog"
      :dialog-width="'523px'"
      class="editPriceDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="identify_right_title">修改价格</span>
      </template>
      <template slot="content">
        <div class="editPriceDialogContent">
          <el-form
            ref="priceForm"
            :rules="rules"
            :model="priceForm"
            style="margin-top: 40px"
            label-width="83px"
            class="editPriceDialogContentForm"
          >
            <div class="accountList_top_title">
              改价比例不能超过50%，如超过50%将重新审核
            </div>
            <el-form-item
              :label-width="formLabelWidth"
              :rules="[
                { required: true, message: '请输入账号价格', trigger: 'blur' },
                { validator: validatePositiveInteger, trigger: 'blur' },
              ]"
              prop="price"
              label="账号价格"
            >
              <el-input
                v-model="priceForm.price"
                placeholder="账号价格"
                autocomplete="off"
                onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                type="tel"
              ></el-input>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入心理底价', trigger: 'blur' },
                { validator: validatePositiveInteger, trigger: 'blur' },
              ]"
              :label-width="formLabelWidth"
              label="心理底价"
              prop="originPrice"
            >
              <el-input
                v-model="priceForm.originPrice"
                placeholder="心理底价"
                autocomplete="off"
                onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                type="tel"
              ></el-input>
              <div class="bottomText">
                当有买家咨询最低价时系统将自动发送此价格，请谨慎填写。
              </div>
            </el-form-item>
          </el-form>
          <div slot="footer" class="editPriceDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="priceFormDialog = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="priceFormSubmit"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </editPriceDialog>

    <editPriceDialog
      :visible="polishDialog"
      :dialog-width="'523px'"
      class="polishDialog"
      @dialogClone="polishDialogClone"
    >
      <template slot="right_title">
        <span class="identify_right_title">擦亮商品</span>
      </template>
      <template slot="content">
        <div class="editPriceDialogContent">
          <div style="min-height: 116px;">
            <div class="polishContent">
              可通过“擦亮”来增加曝光，同一商品每日仅可“擦亮”2次
          </div>
          </div>
          <div slot="footer" class="editPriceDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="polishDialog = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="polishSubmit"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </editPriceDialog>

    <tipDialog
      :visible="editVisible"
      @dialogClone="tipDialogClone"
      @dialogSubmit="dialogSubmitClone"
    >
      <!--  -->
      <template slot="content"
        >{{
          editData.productStatus === 'ON_SHELF'
            ? '是否下架商品并编辑，提交后商品将重新进行审核'
            : editData.pushType==1?'修改商品信息会重新提交录号':'修改商品信息会重新进行审核'
        }}
      </template>
    </tipDialog>
    <tipDialog
      :visible="deleteVisible"
      @dialogClone="deleteTipDialogClone"
      @dialogSubmit="deleteDialogSubmitClone"
    >
      <!--  -->
      <template slot="content"> 您确定删除商品吗？删除不可恢复 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
    <!-- <el-dialog :visible.sync="priceFormDialog" width="40%" title="修改价格">
      <el-form ref="priceForm" :rules="rules" :model="priceForm">
        <el-alert
          title="改价比例不能超过50%，如超过50%将重新审核"
          type="warning"
          style="margin-bottom: 10px"
        >
        </el-alert>
        <el-form-item
          :label-width="formLabelWidth"
          :rules="[
            { required: true, message: '请输入账号价格', trigger: 'blur' },
            { validator: validatePositiveInteger, trigger: 'blur' },
          ]"
          prop="price"
          label="账号价格"
        >
          <el-input
            v-model="priceForm.price"
            placeholder="账号价格"
            autocomplete="off"
            onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
            type="tel"
          ></el-input>
        </el-form-item>
        <el-form-item
          :rules="[
            { required: true, message: '请输入心理底价', trigger: 'blur' },
            { validator: validatePositiveInteger, trigger: 'blur' },
          ]"
          :label-width="formLabelWidth"
          label="心理底价"
          prop="originPrice"
        >
          <el-input
            v-model="priceForm.originPrice"
            placeholder="心理底价"
            autocomplete="off"
            onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
            type="tel"
          ></el-input>
          <div class="note-waring">
            当有买家咨询最低价时系统将自动发送此价格，请谨慎填写。
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="priceFormDialog = false">取 消</el-button>
        <el-button type="primary" @click="priceFormSubmit">确 定</el-button>
      </div>
    </el-dialog> -->
    <similarDealDialog
      :product-category-id="similarDealDialogId"
      :price="similarDealDialogPrice"
      :visible="similarDealDialogVisible"
      @dialogClone="similarDealDialogClone"
    />
    <toolDialog
      :product-id="productId"
      :visible="toolDialogVisible"
      @dialogClone="toolDialogClone"
      @dialogSubmit="toolDialogSubmit"
    />
    <editPriceDialog
      :visible="addImageDialog"
      :dialog-width="'730px'"
      class="editPriceDialog addImageDialog"
      @dialogClone="addImageDialogClone"
    >
      <template slot="right_title">
        <!-- <span class="identify_right_title">追加图片</span> -->
      </template>
      <template slot="content">
        <div class="">
          <div style="width: 100%; margin-top: 20px;">
            <uploadList :optionsList="albumPicsTypeOptions" :url-pic="addImageList" :max="maxNumber" name-key="user_pics" class="add_goods_details_upload"
                    @upSuccsessList="picUpLoadListSuc" @deletPicList="deletPic" @changeImgType="changeImgType"/>
            <div class="accountListEditDescriptionBox">商品描述： 
            <el-input
              v-model="description"
              class="accountListEditDescription"
              placeholder="请谨慎填写，将作为商品标题。未填写则用默认，建议突出商品卖点。"
            ></el-input>
          </div>
            </div>
         
          <div slot="footer" class="editPriceDialogContent_dialog-footer">
            <el-button class="btn btnCancel" @click="addImageDialog = false"
              >取 消</el-button
            >
            <el-button
              class="btn btnSubmit"
              type="primary"
              @click="addImageSubmit"
              >确 定</el-button
            >
          </div>
        </div>
      </template>
    </editPriceDialog>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';

import {
  getProductList,
  updatePrice,
  deleteProduct,
  publishDown,
  publishUp,
  productCaliang
} from '@/api/myPost.js';
import { getGameList } from '@/api/index2.js';

import { getMemberHisKFList, m2kfTalk, orderTeam } from '@/api/kf.js';
import editPriceDialog from '@/components/borderDialog/index2.vue';
import tipDialog from '@/components/borderDialog/index3.vue';
import toolDialog from '@/components/borderDialog/toolDialog.vue';
import similarDealDialog from '@/components/borderDialog/similarDealDialog.vue';
import uploadList from '@/components/uploadList/select_img';
import { size } from 'lodash';
import { add } from 'lodash';
import { name } from 'file-loader';
export default {
  name: 'Home',
  components: {
    editPriceDialog,
    tipDialog,
    similarDealDialog,
    toolDialog,
    uploadList
  },
  data() {
    return {
      polishId:'',
      polishDialog:false,
      addImageList:[],
      maxNumber:20,
      similarDealDialogId: null,
      similarDealDialogPrice: null,
      similarDealDialogVisible: false,
      toolDialogVisible: false,
      addImageDialog:false,
      //       上架  stock=9 && publish_status=1 && delete_status = 0
      // 已预订 stock=1  && publish_status=1 && delete_status = 0
      // 强制下架  publish_status=0 && verify_status = 1
      // 自行下架  publish_status=-2
      // 待审核 stock=9  && publish_status=0 && delete_status = 0 && verify_status=0
      // 审核失败 stock=9  && publish_status=0 && delete_status = 0 && verify_status=2

      // 已出售  stock=0  && delete_status = 0
      statusDate: [
        {
          name: '查看全部',
          id: undefined,
        },
        {
          name: '上架',
          id: 'WAIT_PAY',
        },
        {
          name: '已预定',
          id: 'BOOKED',
        },
        {
          name: '强制下架',
          id: 'REFUND',
        },
        {
          name: '自行下架',
          id: 'COMPLETED',
        },
        {
          name: '待审核',
          id: 'CANCELED',
        },
        {
          name: '审核失败',
          id: 'CANCELED',
        },
        {
          name: '已出售',
          id: 'CANCELED',
        },
      ],
      statusDate2: [
        {
          name: '查看全部',
          id: undefined,
        },
        {
          name: '在售',
          id: 'ON_SHELF',
        },
        {
          name: '已预定',
          id: 'BOOKED',
        },
        {
          name: '已下架',
          id: 'OFF_SHELF',
        },
        {
          name: '待审核',
          id: 'TO_REVIEW',
        },
      ],
      rules: {},
      priceForm: {
        price: null,
        originPrice: null,
        id: null,
      },
      priceFormDialog: false,
      formLabelWidth: '83px',
      dialogVisibleCode: false,
      formInline: {
        cname: '',
        productCategoryId: '',
      },
      description:'',
      index: 2,
      listLoading: false,
      value: false,
      page: 1,
      totalPage: 10,
      initDate: [],
      allGameList: [],
      qrCode: '',
      status: undefined,
      editVisible: false,
      editData: {},
      deleteVisible: false,
      deleteDate: {},
      productId: 0,
      albumPicsTypeOptions:[
      ]
    };
  },
  watch: {},
  mounted() {
    if (isLogin()) {
      this.initDateFun();
      this.initGame();
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },
  methods: {
    changeImgType(v,i){
      this.addImageList[i].name=v
     
    },
       // 多张上传-组件：单图片-上传成功
       picUpLoadListSuc(url, key) {
        if(this.addImageList.length<this.maxNumber){
          this.addImageList.push({url:url,name:'其他物品'});
        }
      
    },
    // 删除已上传的图片-多张的
    deletPic(index, key) {
      this.addImageList.splice(index, 1);
    },
    addImageSubmit(){
      if(!this.addImageList.length){
        this.$message.error('请上传图片')
        return
      }

      console.log(this.addImageList,222222)
      this.addImageDialog=false
    },
    addImageDialogClone(){
      this.addImageDialog=false
    },
    addImage(){
      this.addImageDialog=true
    },
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    tipDialogClone() {
      this.editVisible = false;
    },
    toolDialogClone() {
      this.toolDialogVisible = false;
    },
    polishDialogClone(){
      this.polishDialog=false
    },
    toolDialogSubmit() {
      this.initDateFun();
      this.toolDialogVisible = false;
    },
    dialogSubmitClone() {
      if (this.editData.productStatus === 'ON_SHELF') {
        publishDown({
          id: this.editData.id,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('下架成功');
            this.resetSearch();
            if (this.editData.gameGoodsSaletype === 3) {
              this.productId = this.editData.id;
              this.toolDialogVisible = true;
              this.editVisible = false;
            } else {
              this.editAcc2(this.editData);
              this.editVisible = false;
            }
          }
        });
      } else {
        if (this.editData.gameGoodsSaletype === 3) {
          this.productId = this.editData.id;
          this.toolDialogVisible = true;
          this.editVisible = false;
        } else {
          this.editAcc2(this.editData);
          this.editVisible = false;
        }
      }
    },
    deleteTipDialogClone() {
      this.deleteVisible = false;
    },
    deleteDialogSubmitClone() {
      deleteProduct(this.deleteDate.id).then((res) => {
        this.listLoading = false;
        if (res.code == 200) {
          this.deleteVisible = false;
          this.$message.success('删除成功');
          this.resetSearch();
        }
      });
    },
    statusChoose(date) {
      this.status = date.id;
      this.page = 1;
      this.totalPage = 0;
      this.initDateFun();
    },
    goSimilarList(item) {
      console.log(item, 111111);
      this.similarDealDialogId = item.id;
      this.similarDealDialogPrice = item.price;
      this.similarDealDialogVisible = true;
      // this.$router.push({
      //   path: `/account/similarList?productId=${item.id}&price=${item.price}`,
      // });
    },
    similarDealDialogClone() {
      this.similarDealDialogVisible = false;
    },
    goQuestion(item) {
      this.$router.push({
        path: '/account/questions?productId=' + item.id,
      });
    },
    deleteProduct(date) {
      this.deleteVisible = true;
      this.deleteDate = date;
      // this.$confirm('您确定删除商品吗？删除不可恢复', '提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   this.listLoading = true;
      // deleteProduct(date.id).then((res) => {
      //   this.listLoading = false;
      //   if (res.code == 200) {
      //     this.$message.success('删除成功');
      //     this.resetSearch();
      //   }
      // });
      // });
    },
    validatePositiveInteger(rule, value, callback) {
      if (!value) {
        return callback(new Error('请输入账号价格'));
      }
      const num = Number(value);
      if (Number.isInteger(num) && num > 0) {
        // 验证成功
        callback();
      } else {
        // 验证失败
        callback(new Error('请输入一个有效的正整数'));
      }
    },
    priceFormSubmit() {
      this.$refs.priceForm.validate((valid) => {
        if (valid) {
          const newPrice = this.priceForm.price;
          const newOriginPrice = this.priceForm.originPrice;
          updatePrice(this.priceForm.id, {
            newPrice,
            newOriginPrice,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success('修改成功');
              this.priceFormDialog = false;
              this.$refs.priceForm.resetFields();
              this.resetSearch();
            }
          });
        }
      });
    },
    updatePrice(item) {
      this.priceForm.id = item.id;
      this.priceFormDialog = true;
    },
    polishClick(item){
      this.polishId = item.id;
      this.polishDialog=true
    },
    polishSubmit(){
      productCaliang(this.polishId).then((res) => {
            if (res.code == 200) {
              this.$message.success('擦亮成功');
              this.polishDialog = false;
              this.resetSearch();
            }
          });
    },
    goSellDetail(item) {
      // this.$router.push({
      //   path: `/account/sellDetail?productId=${item.id}&productSn=${item.productSn}`,
      // });
      this.$router.push({
        path: '/gd/' + item.productSn,
      });
    },
    goChat(item) {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      if (item.stock == 0) {
        this.goTeam(item);
      } else {
        this.goSingle(item);
      }
    },
    goTeam(item) {
      orderTeam({
        id: item.id,
      }).then((res) => {
        if (res.code == 200) {
          const findKf = res.data;
          if (findKf) {
            const { nim, store } = window.__xkit_store__;
            const imcode = findKf;
            const sessionId = `team-${imcode}`;
            if (store.sessionStore.sessions.get(sessionId)) {
              store.uiStore.selectSession(sessionId);
            } else {
              store.sessionStore.insertSessionActive('team', imcode);
            }
            this.$store.dispatch('ToggleIM', true);
          } else {
            this.$store.dispatch('ToggleIM', true);
          }
        }
      });
    },
    goSingle(item) {
      getMemberHisKFList({
        cateId: item.productCategoryId,
        productId: item.id,
      }).then((res) => {
        if (res.code == 200) {
          const findKf = res.data;
          if (findKf) {
            const { nim, store } = window.__xkit_store__;
            const imcode = findKf;
            const sessionId = `p2p-${imcode}`;
            m2kfTalk({
              cateId: item.productCategoryId,
              kfIM: imcode,
            });
            if (store.sessionStore.sessions.get(sessionId)) {
              store.uiStore.selectSession(sessionId);
            } else {
              store.sessionStore.insertSessionActive('p2p', imcode);
            }
            if (item.id) {
              this.$store.dispatch('ToggleProductCardId', item.id);
            }
            this.$store.dispatch('ToggleIM', true);
          } else {
            this.$store.dispatch('ToggleIM', true);
          }
        }
      });
    },
    getState(item) {
      if (item.deleteStatus === 2) {
        return '已删除';
      } else if (item.deleteStatus === 1) {
        return '管理员删除';
      } else if (item.stock === 0) {
        return '已出售';
      } else if (item.stock === 1) {
        return '已预订';
      } else if (item.publishStatus === 1) {
        return '在售';
      } else if (item.publishStatus === -2) {
        return '已下架';
      } else if (item.verifyStatus === 0) {
        return '待审核';
      } else if (item.verifyStatus === 2) {
        return '审核失败';
      }
    },
    canPreview() {
      return true;
    },
    canEdit2(item) {
      return item.pushType == 1 && item.pushStatus == 3;
    },
    canDel(item) {
      return item.deleteStatus == 0 && (item.stock == 9||item.stock == 8);
    },
    canDown(item) {
      return (
        item.deleteStatus == 0 && item.publishStatus == 1 && (item.stock == 9||item.stock == 8)
      );
    },
    canUp(item) {
      return (
        item.deleteStatus == 0 &&
        item.publishStatus == -2 &&
        item.verifyStatus == -1 &&
       ( item.stock == 9|| item.stock == 8)
      );
    },
    canEdit(item) {
      return (
        item.deleteStatus == 0 && (item.stock == 9||item.stock == 8) && item.publishStatus != -2
      );
    },
    uploadIng(item) {
      return item.pushType == 1 && item.pushStatus == 1;
    },
    // 查看二维码
    checkCode(str) {
      this.qrCode = str;
      this.dialogVisibleCode = true;
    },
    // 游戏-初始化所有游戏-可自助上传的游戏
    initGame() {
      getGameList().then((res) => {
        if (res.code === 200) {
          this.allGameList = res.data;
        }
      });

      //   allGameaPushApi({
      //     type: '',
      //     letter: '',
      //   }).then((response) => {
      //     if (response.code == 200) {
      //       this.allGameList = response.data.list;
      //       this.initDateFun();
      //     }
      //   });
    },
    // 初始化数据
    initDateFun(productSn) {
      let data = {
        pageNum: this.page,
        pageSize: 10,
      };
      if (productSn) {
        data.productSn = productSn;
      }
      if (this.status) {
        data.productStatus = this.status;
      }
      this.listLoading = true;
      getProductList(data)
        .then((res) => {
          if (res.code == 200) {
            this.totalPage = res.data.total;
            this.initDate = res.data.list;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    publishUp(date) {
      this.listLoading = true;
      publishUp({
        id: date.id,
      }).then((res) => {
        this.listLoading = false;
        if (res.code == 200) {
          this.$message.success('上架成功');
          this.resetSearch();
        }
      });
    },
    // 下架发布
    delePushFun(date) {
      this.$confirm('您确定下架当前商品？下架后不可恢复', '温馨提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        publishDown({
          id: date.id,
        }).then((res) => {
          this.listLoading = false;
          if (res.code == 200) {
            this.$message.success('下架成功');
            this.resetSearch();
          }
        });
      });
    },
    // 搜索
    onSubmit() {
      this.page = 1;
      const cname = this.formInline.cname.trim();
      this.initDateFun(cname);
    },
    // 重置搜索
    resetSearch() {
      this.formInline.cname = '';
      this.page = 1;
      this.totalPage = 0;
      this.initDateFun();
    },
    // 分页
    pageFun(val) {
      this.page = `${val}`;
      this.initDateFun();
    },
    // 账号详情
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
    // 编辑账号
    editAcc2(date) {
      this.$router.push({
        path:
          '/pushAccount?productCategoryId=' +
          date.productCategoryId +
          '&attriCateId=' +
          date.productAttributeCategoryId +
          '&productId=' +
          date.id,
      });
    },
    editAcc(date) {
      // if(date.gameGoodsSaletype===3){
      //   this.productId = date.id;
      //   this.toolDialogVisible=true
      // }else{
      this.editVisible = true;
      this.editData = date;
      // }

      // this.$confirm('修改账号信息会重新进行审核', '提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   this.editAcc2(date);
      // });
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    dialogClone() {
      this.priceFormDialog = false;
    },
  },
};
</script>
<style>
@import url(./orderTable.css);
.add_goods_details_upload{
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.add_goods_details_upload .picUpload_wrapSmall {
  width: 120px;
  height: 120px;
  border-radius: 20.567px;
  border: 1px dashed #969696;
  position: relative;
  text-align: center;
  line-height:120px;
  font-size: 28px;
  color: #969696;
  overflow: hidden;
  float: none;
  background: #f7f7f7;
  /* // margin-right: 15.426px; */
}
.add_goods_details_upload .picUpload_wrapSmall:nth-child(5n){
  margin-right: 0px;
}
.add_goods_details_upload .picUpload_wrapSmall_img_list {
  border-radius: 12px !important;
}
</style>
<style lang="scss" scoped>
.widthFive {
  width: 240px;
}
.orderTable_btn {
  /* margin-bottom: 10px; */
}
.verifyFailIcon {
  cursor: pointer;
  font-size: 16px;
}
// .acountList_header {
//   width: 100%;
//   height: 77.13px;
//   background: #fff;
//   box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
//   padding: 33.423px 0px 23.139px 55.705px;
//   font-family: YouSheBiaoTiHei;
//   font-size: 18.854px;
//   font-style: normal;
//   font-weight: 400;
//   line-height: 24px;
//   background: var(--btn-background-gradient);
//   -webkit-background-clip: text;
//   background-clip: text;
//   color: transparent;
// }

.accountList_copy {
  cursor: pointer;
}
.editPriceDialog {
  /deep/ .doalog2_left_logo {
    width: 229px;
    height: 50px;
  }
  /deep/ .el-dialog__body {
    padding: 25px 33px 32px 29px;
  }
  /deep/ .dialogBk {
    width: 252.864px;
    height: 266px;
    top: 93px;
    right: 30.14px;
  }
}
.polishDialog{
  /deep/ .doalog2_left_logo {
    width: 229px;
    height: 50px;
  }
  /deep/ .el-dialog__body {
    min-height: 280px;
    padding: 25px 33px 32px 29px;
  }
  /deep/ .dialogBk {
    width: 152.864px;
    height: 166px;
    top: 93px;
    right: 30.14px;
  }
  .polishContent{
    margin-top: 30px;
    font-size: 18px;    
    font-family: pingfang Sc;
    color: #1b1b1b;
    font-weight: 400;
  }
}
.addImageDialog{
  /deep/ .dialogBk {
    display: none;
  }
}
.accountList_top_title {
  display: flex;
  width: 425px;
  height: 30px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: linear-gradient(
    190deg,
    #fff5ed 3.42%,
    #ffe1c6 52.35%,
    #ffebd9 78.22%,
    #ffebd9 114.78%
  );
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 150% */
  margin-bottom: 30px;
}
.editPriceDialogContent {
  width: 425px;
  margin: 0 auto;
  .bottomText {
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.24px;
    white-space: nowrap;
    margin-top: 12px;
  }
}
.editPriceDialogContentForm {
  .el-form-item {
    margin-bottom: 30px;
  }
  /deep/ .el-input {
    background: var(--btn-background-gradient);
    width: 322px;
    height: 52px;
    border-radius: 50px;
    &::before {
      content: '' !important;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50px;
      z-index: 0;
      margin: 3px;
      position: absolute;
      z-index: 1;
    }
  }
  /deep/ .el-form-item__content {
    margin-left: 103px !important;
  }
  /deep/ .el-input__inner {
    width: 318px;
    height: 48px;
    border-radius: 50px;
    border: none;
    background: #fff;
    position: relative;
    z-index: 3;
    margin-top: 2px;
    margin-left: 2px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
    color: #1b1b1b;
    &::placeholder {
      color: #969696;
    }
  }
  /deep/.el-form-item__label {
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    letter-spacing: 0.64px;
    padding: 0;
    margin-top: 11px;
    &:before {
      background: var(--btn-background-gradient);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
      -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    }
  }
}
.identify_right_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
}
.editPriceDialogContent_dialog-footer {
  margin-top: -11px;
  display: flex;
  justify-content: center;
  .btn {
    width: 129px;
    height: 46px;
    border-radius: 60px;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.64px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /deep/ .el-button {
    border: none;
  }
  .btnCancel {
    position: relative;
    z-index: 1;
    background: var(--btn-background-gradient);

    &::before {
      content: '';
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0px;
      bottom: 0px;
      background: #fff;
      margin: 3px;
      border-radius: 60px;
    }
    /deep/ span {
      position: relative;
      z-index: 3;
      background: var(--btn-background-gradient);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
      -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    }
  }
  .btnSubmit {
    background: var(--btn-background-gradient);
    border: 1px solid #ffddbe;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    margin-left: 16px;
  }

}
.accountListEditDescriptionBox{
  display: flex;align-items: center;margin-bottom: 30px;
  .accountListEditDescription{
    flex: 1;
    /deep/.el-input__inner{
      border-radius: 50px;
    }
  }

}
</style>
