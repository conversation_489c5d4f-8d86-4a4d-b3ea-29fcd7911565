.orderType_cont {
  margin-bottom: 30px;
  font-size: 14px;
  color: #222222;
}
.orderType_item {
  cursor: pointer;
  padding: 14px 8px 18px;
  margin-right: 50px;
  position: relative;
  transition: all 0.3s;
}
.orderType_item:after {
  content: '';
  width: 100%;
  height: 2px;
  background: transparent;
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: 0;
}
.orderType_item.active,
.orderType_item:hover {
  color: #ff6716;
}
.orderType_item.active:after,
.orderType_item:hover:after {
  background: #ff6716;
}
.orderSearch_cont {
  padding-bottom: 10px;
}
.orderTable_head {
  width: 100%;
  background: #fbf9f7;
  border-bottom: 1px solid #e9e9e9;
  border-radius: 24px 24px 0px 0px;
  /* padding: 13.712px 24px; */
  padding: 16px 24px 10px 26px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-weight: 400;
  letter-spacing: 0.64px;
  line-height: 30px;
  height: 56px;
}
.orderTable_left_title {
  color: #969696;
}
.orderTable_body {
  border: 1px solid #e9e9e9;
  border-top: none;
  border-radius: 2px;
  margin-top: 0px;
}
.tableBody_head {
  width: 100%;
  background: #fbf9f7;
  height: 42px;
  font-size: 14px;
  color: #9a9a9a;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0px 24px 0px 26px;
  letter-spacing: 0.56px;
}
.order_status {
  font-size: 12px;
  border-bottom: 1px solid #eeeeee;
  color: #f7423f;
}
.tableBody_box {
  box-sizing: border-box;
  /* padding: 15px; */
  padding: 26px 24px 18px 26px;
  font-size: 14px;
  font-family: San Francisco Display;
  color: #222222;
}
.orderShop_pic {
  flex-shrink: 0;
  width: 110px;
  height: 110px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 12px;
}

.widthOne {
  flex-shrink: 0;
  width: 413px;
}
.widthTwo {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthThree {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthFour {
  flex-shrink: 0;
  width: 100px;
  text-align: center;
}
.widthFive {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}
.widthSix {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}
.widthSeven {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}

.orderShop_tit {
  width: 291px;
  color: rgba(0, 0, 0, 0.4);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.56px;
  margin-top: 3px;
}
.orderShop_subT {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  /* padding-top: 7px; */
  line-height: 24px;
  letter-spacing: 0.56px;
  font-weight: 400;
}
.orderTable_btn {
  font-size: 14px;
  color: #ff6716;
  padding: 0 30px;
  height: 36px;
  font-family: 'PingFang SC';
  letter-spacing: 0.28px;
  font-weight: 500;
  border-radius: 24px;
  box-sizing: border-box;
  margin-left: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
/*.orderTable_btn:first-child{
	margin-left: 0;
}*/
.orderTable_btn.solid {
  /* background: linear-gradient(90deg, #ff8c5c, #ff4b67); */
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  color: #fff;
}
.orderTable_btn.plain {
  background: #fff4ee;
  color: #ff6716;
  border: 1px solid #ff6716;
  padding: 5px 16px;
}
.el-button--primary.btnSe_con {
  background: linear-gradient(90deg, #ff8c5c, #ff4b67);
  border: none;
  padding: 12px 30px;
}
.el-button--primary:focus,
.el-button--primary:hover {
  background: linear-gradient(90deg, #ff8c5c, #ff4b67);
  border: none;
  color: #fff;
}
.el-button--primary.is-plain.btnSe_plain {
  background: #fff4ee;
  color: #ff6716;
  border: 1px solid #ff6716;
  padding: 12px 30px;
  transition: all 0.3s;
}
.el-button--primary.is-plain.btnSe_plain:focus,
.el-button--primary.is-plain.btnSe_plain:hover {
  background: linear-gradient(90deg, #ff8c5c, #ff4b67);
  border: none;
  color: #fff;
}
.or_statu_btn {
  color: #ff6716;
}
.tab_seller {
  background: #fff4ee;
  color: #ff6716;
  border: 1px solid #fff4ee;
}

.colect_head {
  /* background: linear-gradient(90deg, #2a3047, #3e4458); */
  /* border-radius: 4px; */
  box-sizing: border-box;
  /* padding: 10px 20px; */
  margin-bottom: 32.566px;
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 18.854px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.56px; /* 109.091% */
  letter-spacing: 0.44px;
}
.colect_icon {
  width: 26px;
  margin-top: 4px;
  margin-right: 4px;
}
.refuse_reson {
  width: 100%;
  color: #ff4b67;
  font-size: 14px;
  box-sizing: border-box;
  padding: 0 20px 20px;
}
.account_table_box {
  border-radius: 20.56px;
  padding: 46.278px 40px 39px 40px;
}

.goods_status {
  color: #505050 !important;
  display: flex;
  justify-content: center;
  font-family: YouSheBiaoTiHei;
  font-size: 16px !important;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.order_goods_box {
  width: 291px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 110px;
  font-family: 'PingFang SC';
}
.order_goods_name {
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
}
.order_price {
  color: #505050;

  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0.56px;
}
.order_produceSn {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0.64px;
  text-indent: -10px;
}

.search_page_pagination {
  position: relative;
}
.search_page_pagination .el-pagination__jump {
  position: absolute;
  right: 43.707px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
}
.search_page_pagination .el-pagination__jump .el-input__inner {
  height: 20px;
  border-radius: 20px;
  border: none;
  background: #f6f6f6;
}
.search_page_pagination ul li {
  color: rgba(0, 0, 0, 0.4);
  min-width: 24px;
  padding: 0px 12px;
}
.search_page_pagination ul .active {
  color: #2d2d2d !important;
}
.search_page_pagination ul li:hover {
  color: #2d2d2d !important;
}
.tabler_footer_pagination {
  text-align: center;
  margin: 36px auto 0px;
}
.cancel_favorites {
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  cursor: pointer;
}
.colect_head_title {
  margin-bottom: 31px;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 38px;
}

.individual_box {
  width: 100%;
  min-height: 517.628px;
  border-radius: 20.567px;
  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
}
.individual_box .title {
  height: 56px;
  border-radius: 24px 24px 0px 0px;
  background: #fbf9f7;
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
  padding: 16px 22px 10px 56px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0.64px;
}
.individual_box .title_right {
  font-family: YouSheBiaoTiHei;
  font-size: 28px;
  letter-spacing: 0.56px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.individual_box .individual_form_box {
  padding: 44px 0px 0px 32px;
  width: 100%;
  min-height: 448.21px;
  border-radius: 0px 0px 24px 24px;
  border: 1px solid #e9e9e9;
  position: relative;
  overflow: hidden;
  top: 0px;
  left: 0px;
}
.individual_box .individual_box_bg {
  width: 388.22px;
  height: 403.79px;
  position: absolute;
  top: 118.985px;
  left: 526.198px;
  opacity: 0.05;
}
.individual_form .el-form-item__label {
  /* margin-right: 34.28px; */
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.64px;
  padding: 0px;
}
.individual_form .el-form-item__error {
  margin-left: 56px;
}
.individual_form .el-input.is-disabled .el-input__inner {
  background: #fff6eb;
}
.individual_form .el-input .el-input__inner {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  border-radius: 50px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  width: 322px;
  height: 52px;
  margin-left: 40px;
}
.individual_form .el-textarea__inner {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
}
.individual_form .el-form-item__label:before {
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important;
}
.individual_form .el-form-item {
  margin-bottom: 30px;
}

.payldentify_popover {
  background: transparent !important;
  box-shadow: none !important;
  border: none;
}
.payldentify_popover .popper__arrow {
  display: none;
}
.account_btn_border_color_style {
}

.accountList_orderSearch_cont {
  width: 100%;
  height: 82px;
  background: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  padding: 21px 0px 0px 65px;

  margin-top: 8px;
}
.accountList_orderSearch_cont .el-form-item {
  margin-right: 0px;
}
.accountOrderBtn {
  height: 30px !important;
  padding: 0px 20px !important;
}
.reprovision_btn {
  /* width: 101px; */
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.28px;
  background: var(--btn-background-gradient) !important;
  position: relative;
  border: none;
  border-radius: 50px !important;
  padding: 0px 30px;
  margin-left: 10px !important;
}
.reprovision_reset_btn {
  width: 101px !important;
  margin-left: 16px !important;
}
.reprovision_btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50px; /* 圆角需与主按钮一致 */
  background: #fff; /* 内部背景色 */
  z-index: 1;
  margin: 1px; /* 边框宽度 */
}
.reprovision_btn span {
  position: relative;
  z-index: 2;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.accountList_orderSearch_cont .el-form-item__label {
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.64px;
  padding-right: 30px;
}
.accountList_orderSearch_cont .el-input__inner {
  border-radius: 20px;
  font-size: 16px;
  width: 339px;
  height: 44px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  background: #fff;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.05);
}
.acountList_header {
  width: 100%;
  height: 90px;
  background: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 24px 24px 0px 0px;
  /* padding: 33.423px 0px 23.139px 55.705px; */
  padding-left: 40px;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.4);
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.44px;
  line-height: 24px;
  display: flex;
  align-items: center;
  .acountList_header_box {
    display: flex;
    align-items: center;
    .title {
      cursor: pointer;
      margin: 0px 34.28px;
    }
  }
  .acountList_header_box:nth-child(1) {
    .title {
      margin-left: 0px;
    }
  }
  .acountList_header_box:last-child {
    .divider {
      display: none;
    }
  }
  .divider {
    width: 0.5px;
    height: 7.5px;
    background: rgba(0, 0, 0, 0.4);
  }
  .active {
    background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.accountList_copy {
  cursor: pointer;
}

.list_null_sorry {
  font-size: 18px;
  display: flex;
  justify-content: center;
  text-align: left;
  padding: 64px 0px;
  background: #fff;
  border-radius: 24px;
}
.list_null_sorry .sorry_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 27.42px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.list_null_sorry .sorry_text {
  color: #969696;
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 0%;
  margin-top: 5px;
}
