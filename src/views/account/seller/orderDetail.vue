<template>
  <div
    class="page_comStyle"
    style="border-radius: 20.567px; padding: 29.13px 50.563px 93.413px 50.563px"
  >
    <div class="order_detail_colect_head_title">订单详情</div>
    <div>
      <div class="orderDetail_tableBody_head spaceBetween">
        <div>
          <span style="color: #969696">商品编号：</span
          >{{ orderDetail.orderSn }}
          <IconFont
            :size="14"
            icon="copy"
            class="accountList_copy"
            @click="copyVal(orderDetail.orderSn)"
          />
        </div>
        <div>
          <span style="color: #969696">下单时间：</span
          >{{ orderDetail.createTime | formatTime }}
        </div>
      </div>
      <div class="spaceBetween tableBody_box">
        <div
          class="spaceStart widthOne cursor"
          @click="palyPage(productDetail)"
        >
          <div class="orderShop_pic">
            <el-image
              :src="productDetail.productPic"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
          <div class="orderDetail_text_box">
            <div>
              <div class="orderDetail_title">
                【{{ productDetail.productSn }}】
              </div>
              <div class="orderDetail_Shop_tit text_linTwo">
                {{ productDetail.productName }}
              </div>
            </div>

            <div class="orderDetail_shop_subT">
              {{ orderDetail.productCategoryName }}
            </div>
          </div>
        </div>
        <div class="widthFour">{{ orderDetail.productSn }}</div>
        <div class="widthSix orderDetail_goods_status">
          <div>{{ util.getStatus(orderDetail.orderStatus) }}</div>
        </div>
      </div>
    </div>
    <div v-if="baopeiDetail && baopeiDetail.length" class="baopei_box">
      <div class="orderDetail_tableBody_head spaceBetween">
        <div>已加购包赔服务</div>
        <div></div>
      </div>
      <div class="spaceStart tableBody_box">
        <div
          v-for="(item, index) in baopeiDetail"
          :key="index"
          class="compensate_wrap active"
        >
          <img
            class="basic_not_active_logo"
            src="../../../../static/imgs/confirmOrder_logo_active.svg"
            alt=""
          />

          <div style="position: relative; z-index: 2">
            <div class="spaceBetween">
              <div class="spaceBetween bp_note_top">
                <div class="title">
                  {{ item.productName }}
                  <!-- <span @click="doQuestion(index, item, $event)">
                    <IconFont
                      :size="16.28"
                      style="margin: 0; cursor: pointer; margin-top: -3px"
                      color="#FF720C"
                      icon="remind"
                    />
                  </span> -->
                  <!-- @click="addBaopeiShow('basic')" -->
                </div>
                <div
                  :class="
                    item.type == 'BASIC_COMPENSATION'
                      ? 'basic_price'
                      : 'appreciate_price'
                  "
                >
                  ¥{{ item.productPrice }}
                </div>
              </div>
            </div>
            <div class="basic_text">
              最高赔付<span
                :class="
                  item.type == 'BASIC_COMPENSATION'
                    ? 'payPrice'
                    : 'payPrice2'
                "
                >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</span
              >
            </div>
            <div class="spaceBetween itemstart">
              <div class="bp_note">
                {{ item.ruler }}
              </div>
              <div class="spaceStart">
                <img
                  class="acrive_icon"
                  src="../../../../static/imgs/confirmOrder_ative_icon.svg"
                  alt=""
                />
              </div>
            </div>
          </div>
          <!-- <div class="spaceBetween compensate_tit">
            <div class="">{{ item.productName }}</div>
            <div class="payPrice">￥{{ item.productPrice }}</div>
          </div>
          <div>包赔比例{{ getPrecent(item) }}%</div> -->
        </div>
      </div>
    </div>
    <div class="spaceBetweenNoAi" style="width: 100%">
      <div>
        <div
          v-if="buyerAttri.length > 0"
          style="margin-top: 87.414px"
          class="title-box"
        >
          收货信息
        </div>
        <div v-for="(item, index) in buyerAttri" :key="index">
          <div
            style="
              margin-top: 6.856px;
              font-family: 'PingFang SC';
              font-weight: 400;
              width: 260px;
            "
            class="spaceBetween"
          >
            <div style="width: 130px; color: #000; font-size: 14px">
              {{ item.name }}
            </div>
            <div style="color: rgba(27, 27, 27, 0.6)">{{ item.value }}</div>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end">
        <div class="order-info">
          <div class="title-box">订单明细</div>
          <div class="spaceBetween column">
            <div class="label">订单编号</div>
            <div>{{ orderDetail.orderSn }}</div>
          </div>
          <div class="spaceBetween column">
            <div class="label">创建时间</div>
            <div>{{ orderDetail.createTime | formatTimetoSS }}</div>
          </div>
          <div class="spaceBetween column">
            <div class="label">是否包赔</div>
            <div>{{ orderDetail.orderTypeName }}</div>
          </div>
          <div class="midline">
            <div class="spaceBetween column">
              <div class="label">单价</div>
              <div class="price">¥{{ productDetail.productPrice }}</div>
            </div>
            <div v-if="baopeiDetail.length">
              <div
                v-for="(item, index) in baopeiDetail"
                :key="index"
                class="spaceBetween column"
              >
                <div class="label">{{ item.productName }}</div>
                <div class="price">￥{{ item.productPrice }}</div>
                <!-- <div class="price">¥{{ getBaopeiFei() }}</div> -->
              </div>
            </div>
            <div v-if="orderDetail.couponAmount" class="spaceBetween column">
              <div class="label">代金券:</div>
              <div class="price">-¥{{ orderDetail.couponAmount }}</div>
            </div>
            <div
              v-if="
                util.add(
                  orderDetail.discountAmount,
                  orderDetail.promotionAmount
                ) != 0
              "
              class="spaceBetween column"
            >
              <div class="label">优惠金额</div>
              <div class="price">
                ¥-{{
                  util.add(
                    orderDetail.discountAmount,
                    orderDetail.promotionAmount
                  )
                }}
              </div>
            </div>
            <div class="spaceEnd column">
              <div class="label big">总价</div>
              <div class="price">¥{{ orderDetail.payAmount }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="spaceEnd">
      <!-- v-if="orderDetail.status == 0" -->
      <div
        v-if="orderDetail.status == 0"
        class="pay_now spaceCenter orderDetail_reprovision_btn"
        style="margin-right: 8.57px"
        @click="payNowOrder(orderDetail.id)"
      >
        <span>立即支付</span>
      </div>
      <div
        style="border: 1px solid #ffddbe"
        class="pay_now spaceCenter"
        @click="goChat"
      >
        联系客服
      </div>
    </div>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';
import { getOrderDetail, getOrderTeam } from '@/api/confirmOrder.js';
import { getProductCategory } from '@/api/search.js';
import util from '@/utils/index';

export default {
  components: {},
  data() {
    return {
      util,
      orderDetail: {},
      productDetail: {},
      baopeiDetail: [],
      buyerAttri: [],
    };
  },
  watch: {
    '$route.fullPath'() {
      this.init();
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    init() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      this.orderId = this.$route.query.orderId;
      this.getOrderDetail();
    },
    payNowOrder(id) {
      this.$router.push({
        path: '/payOrder?orderId=' + id,
      });
    },
    getBaopeiFei() {
      let price = 0;
      this.baopeiDetail.forEach((ele) => {
        price = util.add(price, ele.productPrice);
      });
      return price;
    },

    accMul(arg1, arg2) {
      return util.times(arg1, arg2);
    },
    getPrice(item) {
      return this.accMul(
        this.productDetail.productPrice,
        item.topRepay
      );
    },
    getPrecent(item) {
      return util.times(item.topRepay, 100);
    },
    palyPage(date) {
      if (date.productId != 0) {
        this.$router.push({
          path: '/gd/' + date.productSn,
        });
      }
    },
    hasTeam(status) {
      return status == 1 || status == 2 || status == 3 || status == 6;
    },
    goChat() {
      getOrderTeam({
        orderId: this.orderId,
      }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', this.orderId);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },

    formatOrder(orderDetail) {
      this.orderDetail = orderDetail;
      this.productDetail = orderDetail.orderItemList.find(
        (item) => item.itemType === 0
      );
      // 获取包赔具体配置
      this.getBaopei(orderDetail.productCategoryId)
    },

    getOrderDetail() {
      getOrderDetail(this.orderId).then((res) => {
        if (res.code === 200) {
          this.formatOrder(res.data);
          this.buyerAttri = JSON.parse(res.data.buyerAttri) || [];
        }
      });
    },
    getBaopei(productCategoryId) {
      getProductCategory(productCategoryId).then((res) => {
        if (res.code == 200) {
          let bpList=[]
          if (res.data.custom) {
            bpList = JSON.parse(res.data.custom||'{}').baopei||[];
          }

          // 包赔列表数据
          let { orderItemList } = this.orderDetail;
          this.baopeiDetail = orderItemList
          .filter((ele) => {
            return ele.itemType === 1;
          })
          .map((item) => {
            const { productAttr,productName } = item;
            const attr = JSON.parse(productAttr)[0] || {};
            const obj = bpList.find(ele=>ele.value===productName && ele.type === attr.type)||{}

             // 包赔卡片数据做兼容
            return {
              ...item,
              ...attr,
              ...obj
            };
          });

          console.log('this.baopeiDetail',this.baopeiDetail)
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.order_detail_colect_head_title {
  margin-bottom: 31px;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 38px;
}
.pay_now {
  margin-top: 24.853px;
  height: 42.85px;
  border-radius: 42.85px;
  text-align: center;
  padding: 0px 41.135px;
  cursor: pointer;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
}
.baopei_box {
  // border-top: 1px solid #eeeeee;
  // border-bottom: 1px solid #eeeeee;
  // height: 160px;
  // padding-left: 16px;
  margin-top: 48.849px;
}
.midline {
  border-top: 1px solid #e9e9e9;
  margin-top: 28.281px;
  padding-top: 16.283px;
}
.order-info {
  // padding-left: 16px;
  margin-top: 87.414px;
  width: 302px;
  .column {
    line-height: 14px;
    color: rgba(27, 27, 27, 0.6);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-top: 6.856px;
    .label {
      color: #000;
    }
    .price {
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .big {
    margin-right: 9.427px;
  }
}
.title-box {
  display: inline-block;
  color: #000;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 4.285px;
  // height: 40px;
  // line-height: 40px;
  position: relative;
}
// .compensate_wrap {
//   width: 340px;
//   margin-right: 10px;
//   box-sizing: border-box;
//   padding: 15px;
//   min-height: 106px;
//   border: 1px solid #eeeeee;
//   border-radius: 6px;
//   font-size: 14px;
//   color: #909090;
//   transition: all 0.3s;
//   .compensate_tit {
//     font-size: 16px;
//     color: #222222;
//     padding-bottom: 18px;
//   }
// }
// .compensate_wrap.active {
//   background: #fff8ef;
//   border: 1px solid #ffdcba;
// }
// .compensate_wrap.active2 {
//   background: #eff7ff;
//   border: 1px solid #0082ff;
// }

.orderTable_body {
  // border-radius: 20px;
  // padding: 20px 15px 0 0;
}
.tableBody_box {
  box-sizing: border-box;
  padding: 26px 24px 26px 22.282px;
  font-size: 14px;
  font-family: San Francisco Display;
  color: #222222;
  border-radius: 0px 0px 10.283px 26px;
  border: 1px solid #e9e9e9;
}
.orderShop_pic {
  flex-shrink: 0;
  width: 110px;
  height: 110px;
  border-radius: 16px;
  overflow: hidden;
  margin-right: 12px;
}

.widthOne {
  flex-shrink: 0;
  width: 350px;
}
.widthTwo {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthThree {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthFour {
  flex-shrink: 0;
  width: 100px;
  text-align: center;
}
.widthFive {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}
.widthSix {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}
.widthSeven {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}

.orderDetail_Shop_tit {
  width: 291px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-top: 3px;
  line-height: 24px;
  letter-spacing: 0.56px;
}
.orderDetail_shop_subT {
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.4);
}
.orderDetail_tableBody_head {
  height: 56px;
  width: 100%;
  border-radius: 24px 24px 0px 0px;
  background: #fbf9f7;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  padding: 16px 24px 10px 26px;
}
.orderDetail_title {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-indent: -10px;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
}
.orderDetail_text_box {
  height: 110px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.orderDetail_goods_status {
  color: #505050 !important;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 16px !important;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.orderDetail_reprovision_btn {
  background: var(--btn-background-gradient) !important;
  position: relative;
  border: none;
}
.orderDetail_reprovision_btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50px; /* 圆角需与主按钮一致 */
  background: #fff; /* 内部背景色 */
  z-index: 1;
  margin: 2.57px; /* 边框宽度 */
}
.orderDetail_reprovision_btn span {
  position: relative;
  z-index: 2;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.compensate_wrap {
  width: 336.859px;
  min-height: 146.57px;
  box-sizing: border-box;
  padding: 24px 24.71px 22.28px 29.14px;
  // border: 1px solid rgba(166, 166, 166, 0.4);
  background: rgba(245, 245, 245, 0.4);
  border-radius: 20px;
  font-size: 13px;
  color: #909090;
  transition: all 0.3s;
  cursor: pointer;
  // margin-top: 34.286px;
  margin-right: 14.285px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border-radius: 20px;
    // margin: 1px;
    border: 1px solid rgba(166, 166, 166, 0.4);
  }
}
.compensate_wrap:nth-child(3) {
  margin-right: 0px !important;
}
.compensate_wrap:first-child {
  // margin-left: 10px;
}
.compensate_wrap.active {
  // background: #fffcf9;
  // border: none;
  // padding: 24.5px 24.71px 23.28px 29.14px;

  // border: 1px solid rgba(255, 255, 255, 0) !important;

  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  border-radius: 20px;
  position: relative;
  // text-decoration: none;
  // border: 1px solid #fff !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    margin: 1px;
    background: #fffcf9;
    position: absolute;
    border-radius: 19px;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    border: none;
  }
}

.basic_not_active_logo {
  position: absolute;
  top: 20.5716;
  right: 25.714px;
  width: 98.57px;
  height: 103.71px;
  z-index: 0;
}
.bp_note_top {
  flex: 1;

  .title {
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 17.143px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.basic_price {
  color: #ff720c;
  font-family: 'PingFang SC';
  font-size: 20.57px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-right: 7.714px;
}
.basic_text {
  margin-top: 15.42px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 13.711px;
  font-style: normal;
  font-weight: 400;
  .payPrice {
    margin-left: 3.42px;
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
  }
  .payPrice2 {
    margin-left: 3.42px;

    font-family: YouSheBiaoTiHei;
    background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.appreciate_price {
  font-family: 'PingFang SC';
  font-size: 20.57px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.itemstart {
  // align-items: start;
  margin-top: 5.14px;

  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 13.71px;
  font-style: normal;
  font-weight: 400;
}
.bp_note {
  max-width: 216.85px;
}
</style>
