<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="orderTable_head spaceBetween">
      <div class="widthOne">商品信息</div>
      <div class="widthTwo">单价</div>
      <!-- <div class="widthThree">底价</div> -->
    </div>

    <div v-for="(item, index) in initDate" :key="index" class="orderTable_body">
      <div class="tableBody_head spaceBetween">
        <div>发布时间：{{ item.createTime | formatTime }}</div>
      </div>
      <div class="spaceBetween tableBody_box">
        <div class="spaceStart widthOne">
          <div class="orderShop_pic">
            <el-image
              class="soled_pic"
              style="width: 100%; height: 100%"
              src="../../../../static/soled.jpg"
              fit="cover"
            ></el-image>
            <el-image
              v-if="item.pic"
              :src="item.pic"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
            <el-image
              v-else
              style="width: 100%; height: 100%"
              src="../../../static/loading.png"
              fit="cover"
            ></el-image>
          </div>
          <div style="cursor: pointer" @click="goSellDetail(item)">
            <div class="orderShop_tit">【{{ item.productSn }}】</div>
            <div class="orderShop_tit textOneLine">
              {{ item.subTitle }}
            </div>
            <div class="orderShop_subT">
              游戏区服：{{ item.gameAccountQufu }}
            </div>
          </div>
        </div>
        <div class="widthTwo">¥ {{ item.price }}</div>
        <!-- <div class="widthThree">¥{{ item.originalPrice }}</div> -->
      </div>
    </div>
    <div v-if="initDate.length === 0" class="empty_order">暂无数据</div>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';

import { sameProductList } from '@/api/myPost.js';
export default {
  name: 'Home',
  components: {},
  data() {
    return {
      listLoading: false,
      initDate: [],
    };
  },
  watch: {},
  mounted() {
    if (isLogin()) {
      this.initDateFun();
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },
  methods: {
    goSellDetail(item) {
      this.$router.push({
        path:`/gd/${item.productSn}`
      });
    },
    // 初始化数据
    initDateFun() {
      let data = {
        offDay: 30,
        pageSize: 6,
        price: this.$route.query.price,
        productId: this.$route.query.productId,
      };
      this.listLoading = true;
      sameProductList(data)
        .then((res) => {
          if (res.code == 200) {
            this.initDate = res.data.list;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
  },
};
</script>

<style scoped>
@import url(./orderTable.css);
.widthFive {
  width: 240px;
}
.orderTable_btn {
  margin-bottom: 10px;
}
.orderShop_pic {
  position: relative;
}
.soled_pic {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
}
.empty_order {
  text-align: center;
  padding-top: 20px;
}
</style>
