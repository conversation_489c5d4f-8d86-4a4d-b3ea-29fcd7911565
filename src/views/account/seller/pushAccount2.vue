<template>
  <div
    ref="bodyScroll"
    :style="{
      background: pageOneShow
        ? 'linear-gradient(180deg, #fff 16.51%, #ffe1c3 100%)!important'
        : 'linear-gradient(180deg, #FFF1E2 16.51%, #FFF 54.92%, #FFE1C3 100%)!important',
    }"
    class="dark_container scrollPageSmoth"
  >
    <headerKk :active-index="index" />

    <div class="safe_width" style="margin-bottom: 60px">
      <el-breadcrumb
        separator-class="el-icon-arrow-right"
        class="pdTopBottom"
        style="padding: 20px 0px"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/allSell' }"
          >官网寄售</el-breadcrumb-item
        >
        <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
          pageOneJson.name
        }}</el-breadcrumb-item>
      </el-breadcrumb>

      <div
        v-loading.fullscreen.lock="listLoading"
        class="page_comStyle pushAccount_box"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <!-- <div class="spaceStart colect_head">
          <img src="../../../../static/push.png" class="colect_icon" />
          <div>发布账号</div>
        </div> -->
        <!-- <span style="font-size: 32px; font-family: YouSheBiaoTiHei"
          >发布账号</span
        > -->
        <img
          class="psuhAccount_title"
          src="../../../../static/imgs/pushAccount_push.svg"
          alt=""
        />

        <div v-if="!showSuccess">
          <!-- 拒绝原因 -->
          <el-alert
            v-if="postForm.state == -1 && postForm.reason"
            :title="`拒绝原因：` + postForm.reason"
            :closable="false"
            style="margin-bottom: 20px"
            type="error"
            show-icon
            effect="dark"
          >
          </el-alert>

          <!-- 层级筛选 -->
          <div v-if="pageOneShow">
            <div class="spaceStart cengji_wrap">
              <!-- 按大区-一级二级联动 -->
              <div v-if="hasQufu" class="ceng_item" style="height: 332px">
                <div class="ceng_header noBoder">
                  <span class="title_text">按{{ findServerName }}</span>
                </div>
                <!-- <el-input
                  v-model="serverKey"
                  placeholder="请输入内容"
                  suffix-icon="el-icon-search"
                  @input="serverKeyChange"
                >
                </el-input> -->
                <div class="ceng_body">
                  <div
                    v-for="(item, index) in arrayServerCopy"
                    :class="
                      postForm.parent_name == item.parent_name ? 'active' : ''
                    "
                    :key="index"
                    class="cengC_item spaceBetween"
                    @click="chooseSever(item)"
                  >
                    <div>{{ item.parent_name }}</div>
                    <!-- <i class="el-icon-caret-right"></i> -->
                    <img
                      class="el-icon-caret-right"
                      src="../../../../static/imgs/pushAccount_arrow.svg"
                      alt=""
                    />
                  </div>
                </div>
              </div>

              <!-- 按大区-一级二级联动 -->
              <div
                v-if="postForm.parent_name && qufuLevel === 2"
                class="ceng_item"
                style="height: 332px"
              >
                <div class="ceng_header noBoder">
                  <span class="title_text"> 服务器 </span>
                </div>
                <!-- <el-input
                  v-model="serverSubKey"
                  placeholder="请输入内容"
                  suffix-icon="el-icon-search"
                  @input="serverSubKeyChange"
                >
                </el-input> -->
                <div class="ceng_body">
                  <div
                    v-for="(item, index) in arrayServerSubCopy"
                    :class="postForm.server_name == item ? 'active' : ''"
                    :key="index"
                    class="cengC_item spaceBetween"
                    @click="chooseSeverSub(item)"
                  >
                    <div>{{ item }}</div>
                    <!-- <i class="el-icon-caret-right"></i> -->
                    <img
                      class="el-icon-caret-right"
                      src="../../../../static/imgs/pushAccount_arrow.svg"
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <!-- top3的单选 -->
              <div
                v-for="(item, index) in topList"
                v-if="index + (hasQufu ? qufuLevel : 0) <= topShowIndex"
                :key="index"
                class="ceng_item"
                style="height: 332px"
              >
                <div>
                  <div class="ceng_header noBoder">
                    <span class="title_text"> {{ item.name }}</span>
                  </div>
                  <!-- <el-input
                    v-model="item.searchKey"
                    placeholder="请输入内容"
                    suffix-icon="el-icon-search"
                    @input="(v) => filterByInput(v, item)"
                  >
                  </el-input> -->
                  <div class="ceng_body">
                    <div
                      v-for="(iteminner, idx) in item.inputListCopy"
                      :class="item.value == iteminner ? 'active' : ''"
                      :key="idx"
                      class="cengC_item spaceBetween"
                      @click="chooseTop3(iteminner, item)"
                    >
                      <div>{{ iteminner }}</div>
                      <img
                        class="el-icon-caret-right"
                        src="../../../../static/imgs/pushAccount_arrow.svg"
                        alt=""
                      />
                      <!-- <i class="el-icon-caret-right"></i> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="choosed_wrap spaceBetween">
              <div class="spaceStart text_value">
                当前选择为：
                <div>{{ pageOneJson.name }}</div>
                <div v-if="postForm.parent_name">
                  > &nbsp;{{ postForm.parent_name }}
                </div>
                <div v-if="postForm.server_name">
                  > &nbsp;{{ postForm.server_name }}
                </div>
                <div
                  v-for="(item, index) in topList"
                  v-if="item.value"
                  :key="index"
                >
                  > &nbsp;{{ item.value }}
                </div>
              </div>
              <span class="remove_select_value" @click="resartChoose"
                >清除所有选择

                <img
                  style="width: 20.5px; margin: -3px 0px 0px -7px"
                  src="../../../../static/imgs/list_detail.svg"
                  alt=""
              /></span>
              <!-- <div class="restart_chos" @click="resartChoose">重新选择</div> -->
            </div>

            <el-button
              style="width: 194.5px; margin: 0 auto; display: block"
              type="primary"
              round
              class="btnSe_con"
              @click="nextNews"
              >下一步填写信息</el-button
            >
          </div>

          <!-- 信息填写 -->
          <div v-if="!pageOneShow">
            <div class="choosed_wrap spaceBetween">
              <div class="spaceStart text_value">
                当前选择为：
                <div>{{ pageOneJson.name }}</div>
                <div v-if="postForm.parent_name">
                  > &nbsp;{{ postForm.parent_name }}
                </div>
                <div v-if="postForm.server_name">
                  > &nbsp;{{ postForm.server_name }}
                </div>
                <div
                  v-for="(item, index) in topList"
                  v-if="item.value"
                  :key="index"
                >
                  > &nbsp;{{ item.value }}
                </div>
              </div>
              <div class="restart_chos" @click="returnPageone">重新选择</div>
            </div>
            <el-form
              ref="postForm"
              :rules="rules"
              :model="postForm"
              style="margin-top: 0; width: 80%"
              class="userC_form pushAccount_form"
              label-width="140px"
            >
              <div class="formType_name">基本信息</div>
              <el-form-item label="出售方式" prop="sell_method">
                <el-radio-group v-model="postForm.sell_method">
                  <el-radio :label="0">平台代售</el-radio>
                  <el-radio :label="1">平台代售+合作号商回收</el-radio>
                  <el-popover placement="right" width="600" trigger="hover">
                    <img
                      style="width: 100%"
                      src="https://images2.kkzhw.com/mall/images/********/nsh_sell_method.webp"
                    />

                    <el-button
                      slot="reference"
                      style="
                        color: #ff720c;
                        border: none;
                        font-size: 16px;
                        font-family: PingFang SC;
                        font-weight: 400;
                        /* background: url(../../../../static/ques.jpg) no-repeat
                          left center; */
                        /* background-size: 18px; */
                        background-position: left;
                        letter-spacing: 0.32px;
                      "
                    >
                      <IconFont
                        :size="19.14"
                        style="margin-right: 5.364px"
                        icon="cautionary"
                      />模式说明
                    </el-button>
                  </el-popover>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="截图方式" prop="pushType">
                <!-- v-if="navStatus == 1 || navStatus == 3" -->
                <div class="custom-radio-group spaceStart">
                  <div
                    :class="postForm.pushType === 1 ? 'active' : ''"
                    class="custom-radio-item"
                    @click="changePushType(1)"
                  >
                    <div class="name">
                      <img
                        v-if="postForm.pushType !== 1"
                        class="icon"
                        src="../../../../static/imgs/pushAccount_screenshot_icon1.svg"
                      />
                      <img
                        v-if="postForm.pushType === 1"
                        class="icon"
                        src="../../../../static/imgs/logo_icon.svg"
                      />官方截图
                    </div>
                    <div>填写账号官方自动截图</div>
                  </div>
                  <!-- v-if="navStatus == 1 || navStatus == 2" -->
                  <div
                    :class="postForm.pushType === 2 ? 'active' : ''"
                    class="custom-radio-item"
                    @click="changePushType(2)"
                  >
                    <div class="name">
                      <img
                        v-if="postForm.pushType !== 2"
                        class="icon"
                        src="../../../../static/imgs/pushAccount_screenshot_icon2.svg"
                      />
                      <img
                        v-if="postForm.pushType === 2"
                        class="icon"
                        src="../../../../static/imgs/pushAccount_screenshot_active_icon2.svg"
                      />
                      自主截图
                    </div>
                    <div>卖家自行上传游戏截图</div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="可否议价" prop="is_kanjia">
                <el-radio-group v-model="postForm.is_kanjia">
                  <el-radio :label="1">支持议价</el-radio>
                  <el-radio :label="0">不支持议价</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- <el-form-item label="是否一手" prop="is_firsthand">
                <el-radio-group v-model="postForm.is_firsthand">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item> -->
              <el-form-item
                v-for="(item, index) in opetionOtherDate0Group0"
                :key="`opetionOtherDate0Group0${index}`"
                :label="item.name"
                :prop="`${item.id}`"
                :class="getClass(item)"
              >
                <el-input
                  v-if="item.type === 1"
                  v-model="item.iptVal"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  placeholder="请输入"
                ></el-input>
                <el-popover
                  v-if="item.name === '天霓染'"
                  placement="right"
                  width="600"
                  trigger="hover"
                >
                  <img
                    style="width: 100%"
                    src="https://images2.kkzhw.com/mall/images/********/tianniran.webp"
                  />
                  <el-button
                    slot="reference"
                    style="
                      color: #fe5a1e;
                      border: none;
                      font-size: 16px;
                      font-weight: 600;
                      background: url(../../../../static/ques.jpg) no-repeat
                        left center;
                      background-size: 18px;
                      background-position: left;
                    "
                  >
                    天霓染说明
                  </el-button>
                </el-popover>
                <el-radio-group
                  v-if="item.type === 3 || item.type === 4"
                  v-model="item.value"
                  @change="
                    radioGroupChange(item, 'opetionOtherDate0Group0', index)
                  "
                >
                  <el-radio
                    v-for="(r, idx) in item.inputList"
                    :key="idx"
                    :label="r"
                  >
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- 输入框 -->
              <el-form-item
                v-for="(item, index) in opetionOtherDate0Group1"
                :key="`opetionOtherDate0Group1${index}`"
                :label="item.name"
                :prop="`${item.id}`"
                :class="getClass(item)"
              >
                <el-input
                  v-if="item.type === 1"
                  v-model="item.iptVal"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  placeholder="请输入"
                ></el-input>
                <el-popover
                  v-if="item.name === '天霓染'"
                  placement="right"
                  width="600"
                  trigger="hover"
                >
                  <img
                    style="width: 100%"
                    src="https://images2.kkzhw.com/mall/images/********/tianniran.webp"
                  />
                  <el-button
                    slot="reference"
                    style="
                      color: #fe5a1e;
                      border: none;
                      font-size: 16px;
                      font-weight: 600;
                      background: url(../../../../static/ques.jpg) no-repeat
                        left center;
                      background-size: 18px;
                      background-position: left;
                    "
                  >
                    天霓染说明
                  </el-button>
                </el-popover>
              </el-form-item>

              <el-form-item label="商品价格" prop="yusuan">
                <el-input
                  v-model="postForm.yusuan"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  placeholder="商品价格"
                ></el-input>
                <div v-if="shouyi && feeRate.note" class="compprice">
                  <div class="spaceBetween priceone">
                    <span v-if="feilv == minPrice">代售费用(固定)</span
                    ><span v-else>{{ feeRate.note || '' }}</span>
                    <span>￥{{ feilv }}</span>
                  </div>
                  <div class="spaceBetween">
                    <span>最终收益</span>
                    <span v-if="postForm.yusuan <= minPrice" class="price_color"
                      >最低售卖金额:¥{{ minPrice }}</span
                    >
                    <span v-else class="price_color">￥{{ shouyi }}</span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="预期底价" prop="min_price">
                <el-input
                  v-model="postForm.min_price"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  placeholder="如有买家达到您的心理底价，客服会帮您锁定买家，请准确填写以免错失交易"
                ></el-input>
                <div class="note-waring">
                  当有买家咨询最低价时系统将自动发送此价格，请谨慎填写。
                </div>
              </el-form-item>

              <el-form-item
                v-for="(item, index) in opetionOtherDate0Group10"
                :key="`opetionOtherDate0Group10${index}`"
                :label="item.name"
                :prop="`${item.id}`"
                :class="getClass(item)"
              >
                <el-input
                  v-if="item.type === 1"
                  v-model="item.iptVal"
                  :placeholder="
                    item.name != '已使用天赏石' && item.name != '未使用天赏石'
                      ? '请输入'
                      : '请输入,没有请填0'
                  "
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                ></el-input>
                <el-popover
                  v-if="item.name === '天霓染'"
                  placement="right"
                  width="600"
                  trigger="hover"
                >
                  <img
                    style="width: 100%"
                    src="https://images2.kkzhw.com/mall/images/********/tianniran.webp"
                  />
                  <el-button
                    slot="reference"
                    style="
                      color: #fe5a1e;
                      border: none;
                      font-size: 16px;
                      font-weight: 600;
                      background: url(../../../../static/ques.jpg) no-repeat
                        left center;
                      background-size: 18px;
                      background-position: left;
                    "
                  >
                    天霓染说明
                  </el-button>
                </el-popover>
              </el-form-item>

              <el-form-item
                v-for="(item, index) in opetionOtherDate0Group11"
                :key="`opetionOtherDate0Group11${index}`"
                :label="item.name"
                :prop="`${item.id}`"
                :class="getClass(item)"
              >
                <el-input
                  v-if="item.type === 1"
                  v-model="item.iptVal"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  placeholder="请输入"
                ></el-input>
                <el-popover
                  v-if="item.name === '天霓染'"
                  placement="right"
                  width="600"
                  trigger="hover"
                >
                  <img
                    style="width: 100%"
                    src="https://images2.kkzhw.com/mall/images/********/tianniran.webp"
                  />
                  <el-button
                    slot="reference"
                    style="
                      color: #fe5a1e;
                      border: none;
                      font-size: 16px;
                      font-weight: 600;
                      background: url(../../../../static/ques.jpg) no-repeat
                        left center;
                      background-size: 18px;
                      background-position: left;
                    "
                  >
                    天霓染说明
                  </el-button>
                </el-popover>
              </el-form-item>
              <!-- 多选 -->
              <el-form-item
                v-if="postForm.pushType == 2"
                label="商品描述"
                prop="description"
              >
                <el-input
                  v-model="postForm.description"
                  placeholder="商品描述最多30字，请输入重要信息，账号内各类物品请通过选项选择"
                ></el-input>
              </el-form-item>
              <el-form-item
                v-if="postForm.pushType == 2"
                label="商品封面图"
                prop="cover"
              >
                <uploadSingle
                  :url-pic="postForm.cover"
                  name-key="cover"
                  @upSuccsessSingle="picUpLoadSuc"
                />
              </el-form-item>
              <el-form-item
                v-if="postForm.pushType == 2"
                label="商品详情图"
                prop="user_pics"
              >
                <uploadList
                  :url-pic="postForm.user_pics"
                  name-key="user_pics"
                  @upSuccsessList="picUpLoadListSuc"
                  @deletPicList="deletPic"
                />
              </el-form-item>
              <pushTedian
                v-if="opetionDate1.length > 0 && postForm.pushType != 1"
                :flag-id="postForm.productCategoryId"
                :opetion-date="opetionDate1"
                :detail-options="detailOptions1"
                label="特点描述"
                @getopetion="getOpetionResult1"
              />

              <div
                v-if="
                  opetionOtherDate2.length ||
                  opetionOtherDate22.length ||
                  postForm.pushType == 1
                "
              >
                <div class="formType_name">账号信息</div>
                <div v-if="postForm.pushType == 1">
                  <el-form-item
                    v-for="(item, index) in opetionOtherDate2"
                    :key="`opetionOtherDate2${index}`"
                    :label="item.name"
                    :prop="`${item.id}`"
                    :class="getClass(item)"
                  >
                    <!-- // 1输入框，2多选，3checkbox，4单选 -->
                    <el-input
                      v-if="item.type === 1"
                      v-model="item.iptVal"
                      placeholder="请输入"
                    ></el-input>
                    <el-radio-group
                      v-if="item.type === 3 || item.type === 4"
                      v-model="item.value"
                      @change="
                        radioGroupChange(item, 'opetionOtherDate2', index)
                      "
                    >
                      <el-radio
                        v-for="(r, idx) in item.inputList"
                        :key="idx"
                        :label="r"
                      >
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div v-else>
                  <el-form-item
                    v-for="(item, index) in opetionOtherDate22"
                    :key="`opetionOtherDate22${index}`"
                    :label="item.name"
                    :prop="`${item.id}`"
                    :class="getClass(item)"
                  >
                    <!-- // 1输入框，2多选，3checkbox，4单选 -->
                    <el-radio-group
                      v-if="item.type === 3 || item.type === 4"
                      v-model="item.value"
                      @change="
                        radioGroupChange(item, 'opetionOtherDate22', index)
                      "
                    >
                      <el-radio
                        v-for="(r, idx) in item.inputList"
                        :key="idx"
                        :label="r"
                      >
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>

                <el-form-item v-if="postForm.pushType == 1" label="温馨提示">
                  <el-radio
                    >授权截图期间请勿顶号。（平均截图时间半小时）</el-radio
                  >
                </el-form-item>
                <el-form-item
                  v-if="postForm.pushType == 1"
                  label="商品描述"
                  prop="description"
                >
                  <el-input
                    v-model="postForm.description"
                    placeholder="商品描述最多30字，请输入重要信息，账号内各类物品请通过选项选择"
                  ></el-input>
                </el-form-item>
              </div>
              <!-- 新增选项-特点描述 -->

              <div class="formType_name">保障信息</div>
              <el-form-item
                class="safeguards_form_item"
                label="手机号码"
                prop="phone"
              >
                <el-input
                  v-model="userInfo.phone"
                  disabled
                  type="tel"
                  placeholder="请输入联系手机号"
                ></el-input>
              </el-form-item>
              <el-form-item
                class="safeguards_form_item"
                label="备用手机号码"
                prop="gameCareinfoPhone2"
              >
                <el-input
                  v-model="postForm.gameCareinfoPhone2"
                  type="tel"
                  placeholder="请输入备用手机号"
                ></el-input>
              </el-form-item>

              <el-form-item
                class="safeguards_form_item"
                label="联系时间"
                prop="phone"
              >
                <el-select
                  v-model="startTime"
                  :popper-append-to-body="false"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dateTime"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
                点 -
                <el-select
                  v-model="endTime"
                  :popper-append-to-body="false"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dateTime"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option> </el-select
                >点
              </el-form-item>
              <el-form-item
                class="safeguards_form_item"
                label="联系微信"
                prop="vxname"
              >
                <el-input
                  v-model="postForm.vxname"
                  placeholder="请输入联系微信"
                ></el-input>
              </el-form-item>
              <!-- <el-alert
                v-if="postForm.pushType == 1"
                style="margin: 10px 0 20px 20px"
                show-icon
                title="账号发布成功后请联系官方客服"
                type="warning"
              >
              </el-alert> -->

              <el-form-item>
                <el-button
                  style="width: 240px"
                  type="primary"
                  round
                  class="btnSe_con"
                  @click="onSubmit('postForm')"
                  >发布账号</el-button
                >
                <!-- <el-button type="primary" plain round class="btnSe_plain">重置</el-button> -->
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 发布成功 -->
        <pushSuccess
          v-if="showSuccess"
          :code-num="prod"
          :product-id="productId"
          :game-name="pageOneJson.name"
        />
      </div>
    </div>

    <!-- 责任须知 -->
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible.sync="popupSw"
      title="上架须知"
      width="40%"
      center
    >
      <div class="zeren_needKnow">
        <p>
          选择官方截图上架，手机账号可能会需要短信验证码或扫码上号，请点击我知道了开始上号
        </p>
        <p>登录成功后账号才能发布到网站，如未登录成功账号不会上架。</p>
        <p>高峰期上架人数过多，中途退出需重新排队，敬请谅解。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="popupTime > 0" type="success" disabled>
          我知道了{{ popupTime > 0 ? '（' + popupTime + '秒）' : '' }}
        </el-button>
        <el-button v-else type="success" @click="popupHide">我知道了</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="showSmsFail"
      :text-inside="true"
      width="30%"
      center
      title="温馨提示"
    >
      <div class="smsFail">
        <div class="iconbox">
          <img class="icon" src="../../../../static/ding2.png" />
        </div>
        <div class="smsMsg">{{ smsMessage }}</div>
        <div class="spaceAround btnbox">
          <el-button @click="goChat">联系客服</el-button>
          <el-button type="primary" @click="goAllSell">查看账号</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="showWaitSms"
      :text-inside="true"
      width="30%"
      center
    >
      <div class="waitsms">
        <div class="waittit">正在上号中</div>
        <div class="waitnote">
          正在上号中，可能会需要您接收手机验证码!请耐心等待～
        </div>
        <div class="progressBox">
          <el-progress
            :percentage="percent"
            :stroke-width="22"
            status="exception"
          />
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogSm"
      width="30%"
      title="正在上号中"
      center
      class="dialogSm"
    >
      <div class="codeBgbox">
        <img class="codeBg" src="../../../../static/codeBg.png" />
      </div>
      <div style="margin-bottom: 10px">
        <div class="smsnote">
          检测到异地登录需要<span class="light">短信验证码</span>
        </div>
        <div class="smsnote">
          已发送至绑定手机<span class="light">{{ bindname }}</span
          >,请注意查收
        </div>
      </div>
      <el-form ref="formSms" :model="formSms">
        <el-form-item
          :rules="[
            { required: true, message: '请输入短信验证码', trigger: 'blur' },
          ]"
          label-width="0"
          style="margin-bottom: 0"
        >
          <el-input
            v-model="formSms.sms"
            autocomplete="off"
            placeholder="请输入短信验证码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="goAllSell">稍后上号</el-button>
        <el-button type="primary" @click="doSendCode">授权上号</el-button>
      </div>
    </el-dialog>

    <AccountInterval v-if="taskId" :task-id="taskId" @finish="afterInterval" />

    <el-dialog
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogErrorTip"
      width="30%"
      title="温馨提示"
      center
      class="dialogSm"
    >
      <div class="codeBgbox">
        <img class="codeBg" src="../../../../static/codeBg.png" />
      </div>
      <div style="margin-bottom: 10px">
        <div class="smsnote" style="text-align: center">
          {{ dialogErrorTipText }}2
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogErrorTip = false"
          >我知道了</el-button
        >
      </div>
    </el-dialog>
    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed
      :flag-id="postForm.productCategoryId"
      @goPageTop="backTopPage"
    />
  </div>
</template>
<script>
import { sendSm, startLuhao, startLuhao2 } from '@/api/submitAccount.js';
import _ from 'lodash';
import util from '@/utils/index';
import uploadSingle from '@/components/uploadSingle/index';
import uploadList from '@/components/uploadList/index';
import pushTedian from '@/components/pushTedian';

import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import pushSuccess from '@/components/pushSuccess/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import AccountInterval from './accountInterval.vue';

import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import { getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import {
  getProductAttribute,
  productCreate,
  getUpdateInfo,
  productUpdate,
} from '@/api/submitAccount.js';
var minPrice = 40;
import { getProductCategory } from '@/api/search';
var validatePositiveInteger = (rule, value, callback) => {
  // 首先检查是否为空，如果是空，则直接通过验证（这里假设你已经在其他地方处理了必填）
  const { field } = rule;

  if (!value) {
    return callback(new Error('请输入商品价格'));
  }
  if (value.length > 9) {
    callback(new Error('最多输入9位数字'));
    return;
  }
  if (field == 'yusuan') {
    if (value < minPrice) {
      callback(new Error(`最低售卖金额${minPrice}`));
      return;
    }
  }
  // 使用正则表达式检查是否为正整数
  // 注意：这里的正则表达式不允许前导零，并且至少有一个数字
  // 如果你允许0作为有效输入，请相应地调整正则表达式
  if (!/^\+?[1-9]\d*$/.test(value)) {
    callback(new Error('商品价格必须为正整数'));
  } else {
    // 验证通过
    callback();
  }
};
var validatePhone = (rule, value, callback) => {
  const { field } = rule;

  if (!value) {
    return callback();
  }
  if (!/^1\d{10}$/.test(value)) {
    return callback(new Error('备用手机号格式错误'));
  }
  return callback();
};
const defRules = {
  gameCareinfoPhone2: [{ validator: validatePhone, trigger: 'blur' }],
  pushType: [{ required: true, message: '请选择截图方式', trigger: 'blur' }],
  vxname: [{ required: true, message: '请输入联系微信', trigger: 'blur' }],
  sell_method: [{ required: true, message: '请选择出售方式', trigger: 'blur' }],
  is_kanjia: [{ required: true, message: '请选择是否可议价', trigger: 'blur' }],
  yusuan: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { validator: validatePositiveInteger, trigger: 'blur' },
  ],
  min_price: [
    { required: true, message: '请输入心里底价', trigger: 'blur' },
    { validator: validatePositiveInteger, trigger: 'blur' },
  ],
  // is_firsthand: [
  //   { required: true, message: '请选择是否一手', trigger: 'blur' },
  // ],
  cover: [
    {
      required: true,
      message: '请选择封面',
      trigger: 'blur',
    },
  ],
  user_pics: [
    {
      required: true,
      message: '请选择详情图',
      trigger: 'blur',
    },
  ],
};
export default {
  name: 'Home',
  components: {
    headerKk,
    footerKk,
    pushSuccess,
    safeNews,
    navigationFixed,
    uploadSingle,
    uploadList,
    pushTedian,
    AccountInterval,
  },
  data() {
    return {
      dialogErrorTip: false,
      dialogErrorTipText: '',

      findServerName: '大区',
      showWaitSms: false,
      formSms: {
        sms: '',
      },
      accType: '',
      bindname: '',
      feeRate: {},
      feilv: 0,
      shouyi: 0,
      qufuLevel: 0,
      topShowIndex: 0,
      opetionDateAll: [],
      opetionDate0: [],
      opetionOtherDate0: [],
      opetionOtherDate0Group0: [],
      opetionOtherDate0Group1: [],
      opetionOtherDate0Group10: [],
      opetionOtherDate0Group11: [],
      detailOptions0: [],
      opetionDate1: [],
      opetionOtherDate1: [],
      opetionOtherDate2: [],
      opetionOtherDate22: [],
      detailOptions1: [],
      topList: [],
      hasQufu: false,
      popupTime: 3,
      popupTimer: null,
      popupSw: false, // 协议弹框
      value1: '',
      navStatus: 2,
      serverKey: '', // 大区搜索
      serverSubKey: '', // 二级大区搜索
      professionKey: '',
      accTypeKey: '',
      sexKey: '',
      msg: '逆水寒手游的发布游戏页面',
      index: 2,
      pageOneShow: true, // 第一页层级选择出现
      showServer: false, // 选择数据是否加载结束出现大区-暂时不用
      showSuccess: false, // 是否发布成功
      productId: '', // 账号id编辑用
      // txImcode: '', //客服信息
      pageOneJson: {
        // 第一页已选择展示
      },
      postForm: {
        gameCareinfoPhone2: '',
        description: '',
        gameAccountQufu: '',
        pushType: 1, // 截图方式
        free_time: '00-23',
        vxname: '',
        productCategoryId: '',
        productCategoryName: '',
        sex: '',
        is_kanjia: 1,
        sell_method: 0,
        profession_id: '',
        parent_name: '', // 一级大区名称
        server_name: '', // 二级大区id
        level: '',
        pingfen: '',
        yipin: '',
        account_type: '',
        yusuan: '',
        min_price: '',
        intro_tedian: '',
        // is_firsthand: 0,
        is_zhuanzhi: 0,
        is_zhuansex: 0,
        cover: '',
        user_pics: [],
      },
      listLoading: false, // 页面加载状态
      isUploading: false,
      // allGameList: [], // 全部游戏
      arrayServer: [], // 区服数据
      arrayServerCopy: [],
      arrayServerSub: [], // 区服二级数据
      arrayServerSubCopy: [],
      dateTime: [
        '00',
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23',
      ],
      startTime: '00',
      endTime: '23',
      rules: Object.assign({}, defRules),
      prod: '',
      // opetionDate: [], // 特点描述
      childOpetion: [], // 传给组件的 数据
      dialogSm: false,
      percent: 0,
      showSmsFail: false,
      smsMessage: '',

      taskId: '',
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  watch: {
    'postForm.yusuan'(to, from) {
      if (to && this.feeRate.note) {
        this.feilv = util.times(to, this.feeRate.rate);
        if (this.feilv < this.minPrice) {
          this.feilv = this.minPrice;
        }
        this.shouyi = util.minus(to, this.feilv);
        if (to <= this.minPrice) {
          this.shouyi = this.minPrice;
        }
      }
    },
  },
  beforeDestroy() {
    this.stl = clearInterval(this.stl);
    this.stl2 = clearInterval(this.stl2);
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.attriCateId = this.$route.query.attriCateId;
    this.postForm.productCategoryId = this.$route.query.productCategoryId;
    this.productId = this.$route.query.productId;
    this.initSelectDate();

    // 测试代码
    // this.taskId = 1038

    // this.smsMessage = this.prod + '上号异常,请点击联系客服';
    // this.showSmsFail = true;
  },
  methods: {
    radioGroupChange(item, name, index) {
      if (item.saveInputList) {
        // 如果有是级联 第一级
        const findIt = item.saveInputList.find((ele) => {
          return ele.parent_name === item.value;
        });
        this[name][index + 1].inputList = findIt.childList;
        this[name][index + 1].value = '';
      }
    },
    goChat() {
      this.showSmsFail = false;
      getMemberHisKFList({
        cateId: this.$route.query.productCategoryId,
      }).then((res) => {
        if (res.code == 200) {
          if (res) {
            let findKf = res.data;
            const { nim, store } = window.__xkit_store__;
            const imcode = findKf;
            const sessionId = `p2p-${imcode}`;
            m2kfTalk({
              cateId: this.$route.query.productCategoryId,
              kfIM: imcode,
            });
            if (store.sessionStore.sessions.get(sessionId)) {
              store.uiStore.selectSession(sessionId);
            } else {
              store.sessionStore.insertSessionActive('p2p', imcode);
            }
            this.$store.dispatch('ToggleIM', true);
          } else {
            this.$store.dispatch('ToggleIM', true);
          }
        }
      });
    },
    startLuhao() {
      let data = {
        productSn: this.prod,
      };
      startLuhao(data)
        .then((res) => {
          if (res.code == 200) {
            this.showWaitSms = false;
            this.formSms.sms = '';
            this.dialogSm = true;
          }
        })
        .finally(() => {
          this.stl && clearInterval(this.stl);
        });
    },
    goAllSell() {
      this.$router.push({
        path: '/account/accountList',
      });
    },
    afterInterval(isSuccess) {
      if (isSuccess) {
        this.goAllSell();
      } else {
        this.smsMessage = this.prod + '上号异常,请点击联系客服';
        this.showSmsFail = true;
      }
    },
    doSendCode() {
      this.$refs.formSms.validate((valid) => {
        if (valid) {
          this.listLoading = true;
          let data = {
            productSn: this.prod,
            smsCode: this.formSms.sms,
          };
          sendSm(data)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('提交成功');
                this.goAllSell();
              } else {
                const { message } = res;
                this.smsMessage = `${message}`;
                this.dialogSm = false;
                this.showSmsFail = true;
              }
            })
            .finally(() => {
              this.listLoading = false;
            });
        }
      });
    },
    // changeSelect0(value, index, item) {
    //   if (!item.saveInputList) {
    //     return;
    //   }
    //   const findIt = item.saveInputList.find((ele) => {
    //     return ele.parent_name === value;
    //   });
    //   this.opetionOtherDate0[index + 1].inputList = findIt.childList;
    //   this.opetionOtherDate0[index + 1].value = '';
    // },
    changeSelect1(value, index, item) {
      if (!item.saveInputList) {
        return;
      }
      const findIt = item.saveInputList.find((ele) => {
        return ele.parent_name === value;
      });
      this.opetionOtherDate1[index + 1].inputList = findIt.childList;
      this.opetionOtherDate1[index + 1].value = '';
    },
    changeFirstJiLian(value, item) {
      const findIt = item.inputList.find((ele) => ele.parent_name === value);
      item.choosedList[1] = '';
      item.secondList = findIt.childList;
    },
    getClass(item) {
      let clazz = item.is_required ? 'is-required' : '';
      if (item.clazz) {
        clazz += ' ' + item.clazz;
      }
      if (this.postForm.pushType == 1) {
        let custom = item.custom || '{}';
        custom = JSON.parse(custom);
        if (custom.pushType1 != 'show') {
          clazz = 'hide';
        }
      }
      return clazz;
    },
    changePushType(index) {
      this.postForm.pushType = index;
      this.setRules();
    },
    filterByInput(v, item) {
      item.searchKey = v;
      let inputList = _.cloneDeep(item.inputList);
      // 用来搜索用，避免修改原inputList
      item.inputListCopy = inputList.filter((ele) => {
        return ele.includes(v) || !v;
      });
    },
    // getOpetionResult0(date) {
    //   this.opetionDate0 = date;
    // },
    getOpetionResult1(date) {
      this.opetionDate1 = date;
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    popupHide() {
      this.popupTime = 0;
      this.popupSw = false;
      clearInterval(this.popupTimer);
      let regexp = /^1\d{10}$/;
      if (this.postForm.pushType == 1 && regexp.test(this.bindname)) {
        // this.$router.push({
        //   path: `/account/sellDetail?needShowSm=1&productId=${this.productId}&productSn=${this.prod}`,
        // });
        this.showSuccess = true;
      } else {
        // 其他的直接去个人中心
        this.showSuccess = true;
        // this.$router.push({
        //   path: '/allSell',
        // });
      }
    },
    // 大区数据-正则匹配搜索
    serverKeyChange(e) {
      this.serverKey = e;
      this.arrayServerCopy = this.arrayServer.filter((item) =>
        item.parent_name.includes(this.serverKey)
      );
    },
    // 二级大区数据-正则匹配搜索
    serverSubKeyChange(e) {
      this.serverSubKey = e;
      this.arrayServerSubCopy = this.arrayServerSub.filter((item) =>
        item.includes(this.serverSubKey)
      );
    },
    chooseTop3(value, item) {
      this.$set(item, 'value', value);
      this.topShowIndex++;
    },
    // 选择大区
    chooseSever(date) {
      // this.pageOneJson.server_name = date.parent_name;
      this.postForm.parent_name = date.parent_name;
      // 二级数据
      if (this.qufuLevel === 2) {
        this.arrayServerSub = date.childList;
        this.arrayServerSubCopy = _.cloneDeep(this.arrayServerSub);
        this.postForm.server_name = '';
      }
      this.topShowIndex = 1;
    },
    // 选择二级大区
    chooseSeverSub(date) {
      // this.pageOneJson.server_subName = date;
      this.postForm.server_name = date;
      this.topShowIndex = 2;
    },
    // 重新选择
    resartChoose() {
      this.postForm.parent_name = '';
      this.postForm.server_name = '';
      this.postForm.profession_id = '';
      this.postForm.account_type = '';
      this.postForm.sex = '';
      this.topShowIndex = 0;
      this.topList.forEach((ele) => {
        ele.value = '';
      });
    },
    // 下一步
    nextNews() {
      if (this.qufuLevel >= 1 && !this.postForm.parent_name) {
        this.$message.error('请选择大区');
        return;
      }
      if (this.qufuLevel >= 2 && !this.postForm.server_name) {
        this.$message.error('请选择二级大区');
        return;
      }
      for (let i = 0, item; i < this.topList.length; i++) {
        item = this.topList[i];
        if (!item.value) {
          this.$message.error(`请选择${item.name}`);
          return;
        }
      }
      this.pageOneShow = false;
    },
    // 返回上一页
    returnPageone() {
      this.pageOneShow = true;
    },
    formatOpetionDate(param) {
      let value = param.value || '';
      if (param.type === 2 && param.choosedList.length) {
        // 多选
        value = param.choosedList.map((c) => c.name).join(',');
      } else if (param.type === 1) {
        // 输入框
        value = param.iptVal;
      } else {
        // 单选
        value = param.value;
      }
      let obj = {
        productAttributeId: param.id,
        value,
        attriName: param.name,
        type: param.sourceType,
      };
      if (this.productId) {
        obj.id = param.valueId;
      }
      return obj;
    },
    // 发布账号
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = this.postFormToData();
          if (this.postForm.pushType == 1) {
            const pwd0 = data.productAttributeValueList.find(
              (ele) => ele.attriName == '游戏密码'
            );
            const pwd1 = data.productAttributeValueList.find(
              (ele) => ele.attriName == '确认密码'
            );
            if (pwd0 && pwd1 && pwd0.value !== pwd1.value) {
              this.$message.error('两次秘密输入不一致');
              return;
            }
            const { productAttributeValueList } = data;
            const findAccType = productAttributeValueList.find(
              (ele) => ele.attriName == '账号类型'
            );
            if (findAccType && findAccType.value == '手机账号') {
              this.accType = findAccType.value;
              let findusername = productAttributeValueList.find(
                (ele) => ele.attriName == '游戏账号'
              );
              if (!/^1\d{10}$/.test(findusername.value)) {
                this.$message.error('账号类型与游戏账号填写不一致');
                return;
              }
            }
          }
          if (!this.productId) {
            // 提交新的发布
            data.source = 'WEB';
            this.submitAdd(data);
          } else {
            this.submitEdit(data);
          }
        } else {
          setTimeout(() => {
            const element = document.querySelectorAll(
              '.el-form-item__error'
            )[0];
            // 滚动到错误元素对应位置
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            });
          }, 50);

          return false;
        }
      });
    },
    postFormToData() {
      this.postForm.free_time = this.startTime + '-' + this.endTime;
      let detail_options = [];
      this.topList.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        detail_options.push(data);
      });
      let tempListForJilian = [];

      this.opetionOtherDate0Group0.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        if (v.selectType == 3) {
          // 级联的合并 push 进去
          tempListForJilian.push(data);
        } else {
          detail_options.push(data);
        }
      });

      this.opetionOtherDate0Group1.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        if (v.selectType == 3) {
          // 级联的合并 push 进去
          tempListForJilian.push(data);
        } else {
          detail_options.push(data);
        }
      });
      this.opetionOtherDate0Group10.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        if (v.selectType == 3) {
          // 级联的合并 push 进去
          tempListForJilian.push(data);
        } else {
          detail_options.push(data);
        }
      });
      this.opetionOtherDate0Group11.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        if (v.selectType == 3) {
          // 级联的合并 push 进去
          tempListForJilian.push(data);
        } else {
          detail_options.push(data);
        }
      });

      this.opetionDate1.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        detail_options.push(data);
      });
      this.opetionOtherDate1.forEach((v, i) => {
        let data = this.formatOpetionDate(v);
        if (v.selectType == 3) {
          // 级联的合并 push 进去
          tempListForJilian.push(data);
        } else {
          detail_options.push(data);
        }
      });

      if (this.postForm.pushType == 1) {
        this.opetionOtherDate2.forEach((v, i) => {
          let data = this.formatOpetionDate(v);
          if (v.name == '游戏账号') {
            this.bindname = v.iptVal.trim();
          }
          if (v.selectType == 3) {
            // 级联的合并 push 进去
            tempListForJilian.push(data);
          } else {
            detail_options.push(data);
          }
        });
      } else {
        this.opetionOtherDate22.forEach((v, i) => {
          let data = this.formatOpetionDate(v);
          if (v.selectType == 3) {
            // 级联的合并 push 进去
            tempListForJilian.push(data);
          } else {
            detail_options.push(data);
          }
        });
      }

      tempListForJilian.forEach((item, index) => {
        if (index % 2 === 0) {
          item.value = `${item.value}|${tempListForJilian[index + 1].value}`;
          detail_options.push(item);
        }
      });

      let gameAccountQufu = `${this.postForm.parent_name}|${this.postForm.server_name}`;
      if (this.qufuLevel === 1 || this.qufuLevel === 0) {
        gameAccountQufu = this.postForm.parent_name || '';
      }

      const findServer = this.attrs.find(
        (ele) => ele.ename === 'gameAccountQufu'
      );
      findServer &&
        detail_options.push({
          productAttributeId: findServer.id,
          value: gameAccountQufu,
          attriName: findServer.name,
          type: findServer.type,
        });

      if (this.productId) {
        this.productAttributeValueList.forEach((ele) => {
          const findIt = detail_options.find(
            (item) => item.productAttributeId == ele.productAttributeId
          );
          const oldAttr = this.productAttributeList.find(
            (item) => item.id == ele.productAttributeId
          );
          if (!findIt) {
            ele.type = oldAttr.type;
            ele.attriName = oldAttr.name;
            // 界面没有编辑的，加上 type 再发回去
            detail_options.push(ele);
          }
        });
      }
      detail_options.forEach((ele) => {
        const oldAttr = this.productAttributeList.find(
          (item) => item.id == ele.productAttributeId
        );
        ele.searchType = oldAttr.searchType;
      });
      if (this.postForm.pushType == 2) {
        if (this.postForm.cover != this.postForm.user_pics[0]) {
          // 如果第一
          this.postForm.user_pics.unshift(this.postForm.cover);
        }
      }
      let albumPics = this.postForm.user_pics.join(',');
      let data = {
        gameCareinfoPhone2: this.postForm.gameCareinfoPhone2,
        pushType: this.postForm.pushType,
        gameAccountQufu,
        productAttributeValueList: detail_options,
        pic: this.postForm.cover,
        albumPics,
        description: this.postForm.description,
        gameCareinfoPhone: this.userInfo.phone,
        gameCareinfoVx: this.postForm.vxname,
        gameCareinfoTime: this.postForm.free_time,
        price: this.postForm.yusuan,
        originalPrice: this.postForm.min_price,
        productCategoryId: this.postForm.productCategoryId,
        productCategoryName: this.pageOneJson.name,
        productAttributeCategoryId: this.attriCateId,
        gameGoodsSaletype: this.postForm.sell_method,
        gameGoodsYijia: this.postForm.is_kanjia,
        // gameGoodsYishou: this.postForm.is_firsthand,
      };
      return data;
    },
    afterSunbmit(response) {
      const { productSn, productId, taskId } = response.data || {};
      this.prod = productSn;
      this.productId = productId;

      // 新流程
      if (taskId) {
        startLuhao2({ productSn: response.data.productSn }).then((res) => {
          if (res.code === 200) {
            this.taskId = response.data.taskId;
          }
        });
      } else {
        this.dialogErrorTip = true;
        this.dialogErrorTipText = response.message || '设备繁忙，请稍后再试';
        this.showSuccess = true;
        // 老的流程
        // if (this.postForm.pushType === 1) {
        //   if (this.accType == '手机账号') {
        //     // 开始录号
        //     this.showWaitSms = true;
        //     this.stl = setInterval(() => {
        //       this.percent = this.percent + 0.2;
        //       if (this.percent >= 99) {
        //         this.stl && clearInterval(this.stl);
        //       }
        //     }, 34);
        //     this.startLuhao();
        //   } else {
        //     this.showSuccess = true;
        //   }
        // } else {
        //   this.showSuccess = true;
        // }
      }
    },
    submitAdd(data) {
      if (this.listLoading) return;

      this.listLoading = true;
      productCreate(data)
        .then((response) => {
          if (response.code == 200) {
            this.afterSunbmit(response);
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    submitEdit(data) {
      this.listLoading = true;
      productUpdate(this.productId, data).then((response) => {
        this.listLoading = false;
        if (response.code == 200) {
          this.afterSunbmit();
        }
      });
    },
    initSelectDate() {
      this.getProductCategory();
      this.getGameListFun();
    },
    getProductCategory() {
      getProductCategory(this.postForm.productCategoryId).then((res) => {
        if (res.code == 200) {
          if (!this.productId) {
            this.navStatus = res.data.navStatus; //0客服，1，全部，2 自主，3 官方
            // 非编辑状态
            if (this.navStatus == 2) {
              this.postForm.pushType = 2;
            }
            if (this.navStatus == 3) {
              this.postForm.pushType = 1;
            }
          }

          this.pageOneJson = res.data;
          let custom = this.pageOneJson.custom || '{}';
          custom = JSON.parse(custom);
          this.feeRate = custom.feeRate || {};
          if (this.feeRate.minPrice) {
            this.minPrice = this.feeRate.minPrice;
          } else {
            if (
              this.$route.query.productCategoryId == 75 ||
              this.$route.query.productCategoryId == 134
            ) {
              this.minPrice = 30;
            } else {
              this.minPrice = 40;
            }
          }
          // 全局变量给validatePositiveInteger用
          minPrice = this.minPrice;
        }
      });
    },
    getGameListFun() {
      if (this.productId) {
        this.initShop();
      } else {
        getProductAttribute(this.attriCateId).then((res) => {
          // 过滤不要的属性配置字段
          this.productAttributeList = res.data.filter(
            (e) => !['游戏密码', '确认密码'].includes(e.name)
          ); //_.cloneDeep(res.data);
          this.formatAttr(this.productAttributeList);
        });
      }
    },
    formatAttr(data) {
      this.attrs = data || [];
      let findServer = this.attrs.find(
        (ele) => ele.ename === 'gameAccountQufu'
      );
      if (findServer && findServer.selectType === 1) {
        let qufulist = findServer.inputList
          ? findServer.inputList.split(',')
          : [];
        if (qufulist.length === 1) {
          // 如果是单选，并且只有一个区服，算没有区服
          findServer = null;
          this.postForm.parent_name = qufulist[0];
          this.qufuLevel = 0;
        }
      }
      if (findServer) {
        if (findServer.name != '区服') {
          this.findServerName = findServer.name;
        }
        this.hasQufu = true;
        if (findServer.selectType === 3) {
          this.arrayServer = JSON.parse(findServer.inputList);
          this.qufuLevel = 2;
        } else {
          this.arrayServer = findServer.inputList
            ? findServer.inputList.split(',')
            : [];
          this.arrayServer = this.arrayServer.map((ele) => {
            return {
              parent_name: ele,
            };
          });
          this.qufuLevel = 1;
        }
        this.arrayServerCopy = _.cloneDeep(this.arrayServer);
      } else {
        this.hasQufu = false;
      }

      this.attrsList = [];
      this.attrs.forEach((ele) => {
        // 过滤掉区服
        if (
          ele.ename !== 'gameAccountQufu' &&
          ele.type !== 3 &&
          ele.type !== 4 &&
          ele.handAddStatus != 2
        ) {
          if (ele.inputList && ele.selectType !== 3) {
            ele.inputList = ele.inputList && ele.inputList.split(',');
          }
          this.attrsList.push(ele);
        }
      });
      // 排序
      this.attrsList.sort((a, b) => {
        return a.type - b.type;
      });
      this.attrsList.sort((a, b) => {
        return a.sort - b.sort;
      });
      this.topList = [];
      this.topShowIndex = 0;
      for (let i = 0; i < this.attrsList.length; i++) {
        const ele = this.attrsList[i];
        if (ele.selectType == 3 && !ele.isjilian2) {
          // 级联拆开 2 个，变成 2 个单选，有关联
          ele.inputList = JSON.parse(ele.inputList);
          const childName = ele.inputList[0].childName;
          const temp = _.cloneDeep(ele);
          ele.saveInputList = ele.inputList;
          temp.inputList = [];
          temp.name = childName;
          temp.isjilian2 = true;

          // 在当前元素后面插入新元素
          this.attrsList.splice(i + 1, 0, temp);

          // 修改原始元素的 inputList
          ele.inputList = ele.inputList.map((item) => item.parent_name);
        }
      }
      // 为了 top3 根据 sort 可控 sort 大的在前面
      this.attrsList.sort((a, b) => {
        return b.sort - a.sort;
      });
      this.attrsList.forEach((ele) => {
        // 1输入框，2多选，3单选只有2个选项，4单选多个选项,5 级联
        let opetion = {
          childList: [],
          name: ele.name,
          nameGroup: ele.nameGroup,
          is_required: ele.handAddStatus == 1 ? 1 : 0,
          default_word: '请输入',
          iptVal: '',
          selectType: ele.selectType,
          saveInputList: ele.saveInputList,
          isjilian2: ele.isjilian2,
          custom: ele.custom || '{}',
        };
        let type = 1;
        if (
          ele.inputType !== 0 &&
          (ele.selectType === 1 || ele.selectType === 3)
        ) {
          // 级联拆 2 个单选来处理
          //单选
          if (ele.inputList.length === 2 && ele.selectType !== 3) {
            // 用redio
            type = 3;
          } else {
            // 用下拉
            type = 4;
          }

          opetion.value = '';
          opetion.inputList = ele.inputList;
        } else if (ele.inputType !== 0 && ele.selectType === 2) {
          // 多选
          type = 2;
          opetion.searchList = [];
          opetion.choosedList = [];
          opetion.value = '';
          opetion.childList = ele.inputList.map((item) => {
            return {
              checked: false,
              icon: 2,
              name: item,
              value: item,
            };
          });
        }
        opetion.type = type;
        // ele.type 有 1,2 对应SKU 属性分类
        opetion.sourceType = ele.type;
        opetion.id = ele.id;
        opetion.inputListCopy = _.cloneDeep(opetion.inputList);
        this.opetionDateAll.push(opetion);
        // 提取 3 个到顶部,单选的,排除账号信息的
        if (
          opetion.sourceType != 5 &&
          this.topList.length < 3 &&
          opetion.selectType == 1 &&
          opetion.inputList.length > 1
        ) {
          if (this.postForm.pushType == 1) {
            let custom = opetion.custom;
            custom = JSON.parse(custom);
            // 官方截图必须要pushType1:show
            if (custom.pushType1 == 'show') {
              this.topList.push(opetion);
            }
          } else {
            this.topList.push(opetion);
          }
        } else {
          if (ele.type === 1) {
            // 属性 1
            // 提取输入框和单选到opetionOtherDate0，opetionDate0是多选
            if (opetion.type === 2) {
              // this.opetionDate0.push(opetion);
              // 多选都去opetionDate1
              this.opetionDate1.push(opetion);
            } else {
              this.opetionOtherDate0.push(opetion);
            }
          } else if (ele.type === 2) {
            // 属性 2
            // 提取输入框和单选到opetionOtherDate1，opetionDate1是多选
            if (opetion.type === 2) {
              this.opetionDate1.push(opetion);
            } else {
              this.opetionOtherDate1.push(opetion);
            }
          } else if (ele.type === 5) {
            // 提取属性 5
            this.opetionOtherDate2.push(opetion);
            if (opetion.name == '账号来源') {
              this.opetionOtherDate22.push(opetion);
            }
          }
        }
      });
      if (!this.hasQufu && this.topList.length == 0) {
        // 第一页没东西显示了
        this.pageOneShow = false;
      }
      this.setRules();
      this.groupOpetionDate();
    },
    validatorLength(rule, value, callback) {
      if (value.length > 30) {
        return callback(new Error('描述最多输入30字'));
      }
      callback();
    },
    setRules() {
      this.rules = {};

      this.$nextTick(() => {
        this.rules = Object.assign({}, defRules);
        this.opetionDateAll.forEach((opetion) => {
          if (opetion.type != 2) {
            // 如果需要必填,多选的不做必填
            if (this.postForm.pushType == 1) {
              if (opetion.nameGroup == '官方截图') {
                this.addRules(opetion);
              }
            } else {
              this.addRules(opetion);
            }
          }
        });
        if (this.postForm.pushType == 2) {
          this.rules['description'] = [
            {
              required: true,
              message: '请输入商品描述',
              trigger: 'blur',
            },
            {
              validator: this.validatorLength,
              trigger: 'blur',
            },
          ];
        } else {
          this.rules['description'] = [
            {
              validator: this.validatorLength,
              trigger: 'blur',
            },
          ];
        }
        this.$nextTick(() => {
          this.$refs.postForm && this.$refs.postForm.clearValidate();
        });
      });
    },
    addRules(opetion) {
      this.rules[`${opetion.id}`] = [
        {
          validator: (rule, value, callback) => {
            const { field } = rule;
            const tempValue = this.getValueByField(field);
            if (!tempValue) {
              // 找不到这个属性了，代表不需要填
              callback();
              return;
            }
            const { realv, findIt } = tempValue;
            if (
              this.postForm.productCategoryId == 75 &&
              findIt.name == '评分'
            ) {
              // 逆水寒评分6位
              if (realv.length != 6) {
                callback(new Error(`${findIt.name}请填写6位数字`));
              } else {
                callback();
              }
            } else if (
              findIt.name == '游戏账号' ||
              findIt.name == '游戏密码' ||
              findIt.name == '确认密码'
            ) {
              let regexp = /^[\x00-\x7F]*$/;
              if (realv || realv === 0) {
                if (!regexp.test(realv)) {
                  callback(new Error(`${findIt.name}格式不对`));
                } else {
                  callback(); // 没有错误
                }
              } else {
                if (findIt.is_required) {
                  callback(new Error(`请输入${findIt.name}`));
                } else {
                  callback();
                }
              }
            } else {
              if (findIt.type == 1 && realv.length > 9) {
                // 如果是输入框，最多 9 位
                callback(new Error(`最多输入9位数字`));
              }
              if (findIt.type == 1 && realv < 0) {
                // 如果是输入框，最多 9 位
                callback(new Error(`${findIt.name}必须为正整数`));
              }
              if (realv || realv === 0) {
                callback(); // 没有错误
              } else {
                if (findIt.is_required) {
                  callback(new Error(`请输入${findIt.name}`));
                } else {
                  callback();
                }
              }
            }
          },
          trigger: opetion.type === 1 ? 'blur' : 'change',
        },
      ];
    },
    groupOpetionDate() {
      let tempList = this.opetionOtherDate0.concat(this.opetionOtherDate1);
      // 分 2 组，单选opetionOtherDate0Group0，
      // 输入框opetionOtherDate0Group1,opetionOtherDate0Group10,opetionOtherDate0Group11
      this.opetionOtherDate0Group0 = [];
      this.opetionOtherDate0Group1 = [];
      this.opetionOtherDate0Group10 = [];
      this.opetionOtherDate0Group11 = [];

      tempList.forEach((ele) => {
        if (ele.type == 1) {
          if (ele.is_required) {
            if (ele.name === '已使用天赏石' || ele.name === '未使用天赏石') {
              this.opetionOtherDate0Group10.push(ele);
            } else {
              this.opetionOtherDate0Group1.push(ele);
            }
          } else {
            this.opetionOtherDate0Group11.push(ele);
          }
        } else if (ele.type == 3 || ele.type == 4) {
          this.opetionOtherDate0Group0.push(ele);
        }
      });
      this.sortByReqAndSort(this.opetionOtherDate0Group0);
      this.sortByReqAndSort(this.opetionOtherDate0Group1);
      this.sortByReqAndSort(this.opetionOtherDate0Group10);
      this.sortByReqAndSort(this.opetionOtherDate0Group11);
    },
    sortByReqAndSort(list) {
      list.sort((a, b) => {
        if (a.is_required == b.is_required) {
          return b.sort - a.sort;
        } else {
          return b.is_required - a.is_required;
        }
      });
    },
    getValueByField(field) {
      let tempList = this.opetionOtherDate0Group0.concat(
        this.opetionOtherDate0Group1,
        this.opetionOtherDate0Group10,
        this.opetionOtherDate0Group11,
        this.opetionOtherDate1
      );
      if (this.postForm.pushType == 1) {
        tempList = tempList.concat(this.opetionOtherDate2);
      } else {
        tempList = tempList.concat(this.opetionOtherDate22);
      }
      const findIt = tempList.find((ele) => {
        return ele.id == field;
      });
      // type 1,输入框，2 多选（不做必填），3 单选，4 下拉单选
      if (findIt) {
        const { type } = findIt;
        if (type === 1) {
          return {
            realv: findIt.iptVal,
            findIt,
          };
        } else {
          // 3 单选，4 下拉单选
          return {
            realv: findIt.value,
            findIt,
          };
        }
      }
    },
    initShop() {
      this.listLoading = true;
      if (this.productId) {
        getUpdateInfo(this.productId).then((res) => {
          this.listLoading = false;
          if (res.code == 200) {
            let data = res.data;
            const { product, productAttributeList, productAttributeValueList } =
              data;

            this.productAttributeList = _.cloneDeep(productAttributeList);
            this.productAttributeValueList = _.cloneDeep(
              productAttributeValueList
            );
            this.postForm.pushType = product.pushType;
            // 编辑后，定死截图类型
            if (this.postForm.pushType == 1) {
              this.navStatus = 3;
            }
            if (this.postForm.pushType == 2) {
              this.navStatus = 2;
            }
            this.formatAttr(productAttributeList);

            if (product.gameAccountQufu) {
              const gameAccountQufu = product.gameAccountQufu.split('|');
              this.postForm.parent_name = gameAccountQufu[0];
              if (gameAccountQufu[1]) {
                this.postForm.server_name = gameAccountQufu[1];
                this.qufuLevel = 2;
                const findqu = this.arrayServer.find((ele) => {
                  return ele.parent_name === this.postForm.parent_name;
                });
                let subList = [];
                findqu.childList.forEach((v) => {
                  subList.push(v);
                });
                this.arrayServerSub = subList;
                this.arrayServerSubCopy = _.cloneDeep(this.arrayServerSub);
              } else {
                this.qufuLevel = 1;
              }
            }
            this.postForm.cover = product.pic;
            if (product.albumPics) {
              this.postForm.user_pics = product.albumPics.split(',');
            }

            this.postForm.description = product.description;
            this.postForm.phone = product.gameCareinfoPhone;
            this.postForm.vxname = product.gameCareinfoVx;
            this.postForm.yusuan = product.price;
            this.postForm.min_price = product.originalPrice;
            this.postForm.sell_method = product.gameGoodsSaletype;
            this.postForm.is_kanjia = product.gameGoodsYijia;
            // this.postForm.is_firsthand = product.gameGoodsYishou;
            const gameCareinfoTime = product.gameCareinfoTime.split('-');
            this.startTime = parseInt(gameCareinfoTime[0]);
            this.endTime = parseInt(gameCareinfoTime[1]);

            // 找到账号和特点多选的值
            // this.detailOptions0 = this.getDetailOptions(
            //   this.opetionDate0,
            //   productAttributeValueList,
            // );
            this.detailOptions1 = this.getDetailOptions(
              this.opetionDate1,
              productAttributeValueList
            );
            // 找到其他单选和输入框的值
            this.getOpetionOtherDate(
              this.opetionOtherDate0,
              productAttributeValueList
            );

            this.getOpetionOtherDate(
              this.opetionOtherDate0Group0,
              productAttributeValueList
            );

            this.getOpetionOtherDate(
              this.opetionOtherDate0Group1,
              productAttributeValueList
            );
            this.getOpetionOtherDate(
              this.opetionOtherDate0Group10,
              productAttributeValueList
            );
            this.getOpetionOtherDate(
              this.opetionOtherDate0Group11,
              productAttributeValueList
            );

            this.getOpetionOtherDate(
              this.opetionOtherDate1,
              productAttributeValueList
            );
            if (this.postForm.pushType == 1) {
              this.getOpetionOtherDate(
                this.opetionOtherDate2,
                productAttributeValueList
              );
            } else {
              this.getOpetionOtherDate(
                this.opetionOtherDate22,
                productAttributeValueList
              );
            }

            this.getOpetionOtherDate(this.topList, productAttributeValueList);
            this.topShowIndex =
              parseInt(this.hasQufu ? this.qufuLevel : 0, 10) +
              this.topList.length;
          }
        });
      }
    },
    getOpetionOtherDate(opetionOtherDate, productAttributeValueList) {
      opetionOtherDate.forEach((ele, index) => {
        const findIt = productAttributeValueList.find((item) => {
          return item.productAttributeId === ele.id;
        });
        if (findIt) {
          ele.valueId = findIt.id;
          if (ele.type === 1) {
            ele.iptVal = findIt.value;
          } else {
            if (ele.saveInputList) {
              // 如果是级联的一级
              ele.value = findIt.value.split('|')[0];
            } else if (ele.isjilian2) {
              // 级联的 2 级
              const splist = findIt.value.split('|');
              ele.value = splist[1];
              const saveInputList = opetionOtherDate[index - 1].saveInputList;
              const jilian1Value = splist[0];
              const findOne = saveInputList.find(
                (ele) => ele.parent_name === jilian1Value
              );
              ele.inputList = findOne.childList;
            } else {
              ele.value = findIt.value;
            }
          }
        }
      });
    },
    getDetailOptions(opetionDate, productAttributeValueList) {
      let list = [];
      opetionDate.forEach((ele) => {
        const findIt = productAttributeValueList.find((item) => {
          return item.productAttributeId === ele.id;
        });
        if (findIt) {
          ele.valueId = findIt.id;
          list.push({
            title: ele.name,
            value: findIt.value,
          });
        }
      });
      return list;
    },
    // 继续发布-刷新页面
    pushAgain() {
      this.$router.push({
        path: '/allSell',
      });
    },
    // 单张上传-组件上传成功
    picUpLoadSuc(url, key) {
      this.postForm[key] = url;
    },
    // 多张上传-组件：单图片-上传成功
    picUpLoadListSuc(url, key) {
      this.postForm[key].push(url);
    },
    // 删除已上传的图片-多张的
    deletPic(index, key) {
      this.postForm[key].splice(index, 1);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
@import url(./push.css);
.ceng_item .el-input__inner {
  border-bottom: none;
}
/* .pushAccount_form .is-disabled .el-input__inner {
  background: rgba(0, 0, 0, 0.1) d;
} */
</style>

<style lang="scss" scoped>
.ceng_body {
  overflow-y: auto;

  &::-webkit-scrollbar-track-piece {
    background: #fff;
  }

  &::-webkit-scrollbar {
    width: 5px !important;
    height: 5px !important;
  }

  &::-webkit-scrollbar-thumb {
    background: #969696;
    width: 1px !important;
    height: 5px !important;
    border-radius: 20px;
  }
}
.waitsms {
  height: 240px;
  padding: 20px;
  background: #fff;
  font-size: 16px;
  text-align: center;
  .waittit {
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
  }
  .waitnote {
    padding: 6px;
    margin-bottom: 20px;
    text-align: left;
  }
  .progressBox {
    background: #ffe3d5;
    padding: 30px 10px;
    /deep/ .el-progress__text {
      display: none;
    }
  }
  .uni-progress {
    .uni-progress-bar {
      height: 40px !important;
      border-radius: 30px;
      overflow: hidden;
      background-color: #fff !important;
    }
    .uni-progress-inner-bar {
      background-color: #ff6f01 !important;
      border-radius: 30px;
      overflow: hidden;
    }
  }
}
.smsnote {
  line-height: 30px;
}
.smsFail {
  .iconbox {
    text-align: center;
    margin-bottom: 20px;
  }
  .icon {
    width: 160px;
    height: 160px;
  }
  .smsMsg {
    margin-bottom: 30px;
    text-align: center;
    // color: #ff6f01;
  }
  .btnbox {
    width: 100%;
  }
}
.codeBg {
  width: 160px;
  height: 160px;
}
.codeBgbox {
  text-align: center;
  margin-bottom: 20px;
}
.dialogSm {
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: space-around;
  }
}
.light {
  color: #fc6116;
}
.el-radio {
  margin-bottom: 5px;
  margin-left: 20.56px;
}
.compprice {
  width: 300px;
  .priceone {
    color: #999;
  }
  .price_color {
    color: #e60f0f;
  }
}
.hide {
  display: none;
}
.second {
  margin-left: 85px;
}
.custom-radio-group {
  .custom-radio-item {
    .name {
      position: relative;
      font-size: 20.56px;
      font-weight: 500;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      .icon {
        width: 30px;
        height: 30px;
        object-fit: cover;
        margin-right: 10.283px;
      }
    }
    div {
      position: relative;
    }
    cursor: pointer;
    width: 200.53px;
    // height: 93.413px;
    margin-right: 20.56px;
    box-sizing: border-box;
    padding: 19.711px 0px 18.854px 0px;
    border-radius: 20.56px;
    border: 1px solid #969696;
    font-size: 14px;
    text-align: center;
    color: #969696;
    background-color: #f7f7f7;
    font-family: 'PingFang SC';
    position: relative;
    font-weight: 400;
    letter-spacing: 0.28px;
    line-height: normal;
  }
  .custom-radio-item.active {
    border: 0px;
    color: #1b1b1b;
    background: var(--btn-background-gradient);
    position: relative;
    .name {
      background: var(--btn-background-gradient);
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
    }
    &::before {
      content: '' !important;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 17.56px;
      background: #fffcf9;
      z-index: 0;
      margin: 2.57px;
      position: absolute;
    }
  }
}
</style>
