<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="page_comStyle"
    style="padding: 0px 0 39px 0; border-radius: 24px"
  >
    <div class="acountList_header">
      <div
        v-for="(item, index) in statusDate"
        :key="index"
        class="acountList_header_box"
      >
        <div
          :class="status == item.id ? 'active' : ''"
          class="title"
          @click="statusChoose(item)"
        >
          {{ item.name }}
        </div>
        <div class="divider"></div>
      </div>
    </div>

    <!-- search -->
    <div class="accountList_orderSearch_cont">
      <el-form
        :inline="true"
        :model="formInline"
        style="display: flex; align-items: center"
        class="demo-form-inline"
        @submit.native.prevent="onSubmit()"
      >
        <el-form-item label="订单编号">
          <el-input v-model="formInline.order_no" placeholder="请输入订单编号">
            <iconFont
              slot="suffix"
              :size="24"
              icon="web_search"
              color="#000000"
              style="margin-right: 14.16px; margin-top: 0px; cursor: pointer"
              @click="onSubmit"
          /></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            plain
            round
            class="reprovision_btn reprovision_reset_btn"
            @click="resetForm"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- table start -->
    <div style="padding: 0 40px; margin-top: 27px">
      <div v-if="initDate.length">
        <div class="orderTable_head spaceBetween">
          <div class="widthOne">商品信息</div>
          <div class="widthTwo">订单金额</div>
          <div class="widthThree">实付金额</div>
          <!-- <div class="widthFour">账号编号</div> -->
          <div class="widthSix">订单状态</div>
        </div>
        <div
          v-for="(item, index) in initDate"
          :key="index"
          class="orderTable_body"
        >
          <div class="tableBody_head spaceBetween">
            <div>
              <!-- {{ item.productCategoryName }} &nbsp;&nbsp;  -->
              <!-- 订单类型：{{ item.orderTypeName }}&nbsp;&nbsp; 订单编号： -->
              <span class="orderTable_left_title"
                >{{ item.orderTypeName }}：</span
              >{{ item.orderSn }}
              <IconFont
                :size="14"
                icon="copy"
                class="accountList_copy"
                @click="copyVal(item.orderSn)"
              />
            </div>
            <div>
              <span class="orderTable_left_title">下单时间：</span
              >{{ item.createTime | formatTime }}
            </div>
          </div>
          <div class="spaceBetween tableBody_box">
            <div class="spaceStart widthOne cursor" @click="palyPage(item)">
              <div class="orderShop_pic">
                <el-image
                  :src="item.productItem.productPic"
                  style="width: 100%; height: 100%"
                  fit="cover"
                ></el-image>
              </div>
              <div class="order_goods_box">
                <div>
                  <div class="order_produceSn">
                    【{{
                      (item.accountItem && item.accountItem.productSn) || ''
                    }}】
                  </div>
                  <div class="orderShop_tit text_linTwo">
                    {{ item.productItem.productName }}
                  </div>
                </div>
                <div class="order_goods_name">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div>
            <div class="widthTwo order_price">¥ {{ item.totalAmount }}</div>
            <div class="widthThree order_price">¥ {{ item.payAmount }}</div>
            <!-- <div class="widthFour">{{ item.productItem.productSn }}</div> -->
            <div class="widthSix goods_status">
              <div>{{ util.getStatus(item.orderStatus) }}</div>
            </div>
          </div>
          <div
            class="spaceEnd"
            style="margin-right: 41.13px; margin-bottom: 20.56px"
          >
            <div
              v-if="canDel(item)"
              class="plain cancel_favorites"
              @click="delOrder(item.id)"
            >
              删除订单
            </div>
            <div class="orderTable_btn solid accountOrderBtn" @click="goChat(item)">
              联系客服
            </div>
          </div>
        </div>
        <!-- tabler end -->
        <div class="tabler_footer_pagination">
          <el-pagination
            :total="totalPage"
            layout="pager,jumper"
            class="search_page_pagination"
            @current-change="pageFun"
          >
          </el-pagination>
        </div>
      </div>
      <div v-else class="list_null_sorry">
        <img
          style="width: 54px; height: 56px"
          src="../../../../static/imgs/null.png"
          alt=""
        />
        <div style="margin-left: 15.85px">
          <img
            style="width: 63px; height: 36px"
            src="../../../../static/imgs/sorry_text.svg"
            alt=""
          />
          <div class="sorry_text">暂时无相关数据</div>
        </div>
      </div>
    </div>
    <tipDialog
      :visible="deleteVisible"
      :right_title="true"
      @dialogClone="deleteTipDialogClone"
      @dialogSubmit="deleteDialogSubmitClone"
    >
      <template slot="content"> 您确定要删除？确定后不可撤销 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
const notallow = {
  '3': 1,
  '4': 1,
  '5': 1,
  '6': 1,
  '7': 1,
  '8': 1,
};
import util from '@/utils/index';
import isLogin from '@/utils/isLogin';
import { deleteOrder, getOrderTeam, mySellerList } from '@/api/confirmOrder.js';
import tipDialog from '@/components/borderDialog/index3.vue';
export default {
  name: 'Home',
  components: {
    tipDialog,
  },
  data() {
    return {
      deleteVisible: false,
      deleteId: '',
      util,
      formInline: {
        order_no: '',
      },
      index: 2,
      statusDate: util.statusDate,
      status: undefined, // 状态
      page: 1,
      totalPage: 10,
      initDate: [],
      listLoading: false,
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.initDateFun();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    delOrder(orderId) {
      this.deleteVisible = true;
      this.deleteId = orderId;
      // this.$confirm('您确定要删除？确定后不可撤销', '提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   deleteOrder(orderId).then((res) => {
      //     if (res.code == 200) {
      //       this.initDateFun();
      //     }
      //   });
      // });
    },
    deleteTipDialogClone() {
      this.deleteVisible = false;
    },
    deleteDialogSubmitClone() {
      deleteOrder(this.deleteId).then((res) => {
        if (res.code == 200) {
          this.initDateFun();
          this.deleteVisible = false;
        }
      });
    },
    canDel(item) {
      return [4, 5, 12].includes(item.status);
    },
    goChat(item) {
      getOrderTeam({
        orderId: item.id,
      }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', item.id);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    findProduct(item) {
      let findIt = item.orderItemList.find((ele) => {
        return ele.itemType === 0;
      });
      return findIt;
    },
    // 初始化数据
    initDateFun(orderSn) {
      let data = {
        orderStatus: this.status,
        pageNum: this.page,
        pageSize: 10,
      };
      if (orderSn) {
        data.orderSn = orderSn;
      }
      this.listLoading = true;
      mySellerList(data)
        .then((res) => {
          if (res.code == 200) {
            this.initDate = res.data.list || [];
            this.initDate.forEach((ele) => {
              ele.productItem = this.findProduct(ele);
            });
            this.totalPage = res.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     * 分页
     */
    pageFun(val) {
      this.page = `${val}`;
      this.initDateFun();
    },
    // 分类变化
    statusChoose(date) {
      this.status = date.id;
      this.page = 1;
      this.totalPage = 0;
      this.initDateFun();
    },
    // 搜索
    onSubmit() {
      this.page = 1;
      this.initDateFun(this.formInline.order_no);
    },
    // 搜索重置
    resetForm() {
      this.formInline.order_no = '';
      this.status = undefined;
      this.page = 1;
      this.initDateFun();
    },
    // 账号详情
    palyPage(date) {
      // if (!notallow[date.orderType]) {
      //   this.$router.push({
      //     path: `/gd/${date.productItem.productSn}`,
      //   });
      // }
      this.$router.push({
        path: '/account/orderDetail?orderId=' + date.id,
      });
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
  },
};
</script>

<style>
@import url(./orderTable.css);
</style>
<style lang="scss" scoped>
.widthSix {
  .pay_now {
    margin: 25px auto 0;
    width: 80px;
    background: #ff6716;
    border: 1px solid #ff6716;
    border-radius: 4px;
    text-align: center;
    padding: 5px 0;
    cursor: pointer;
    font-size: 14px;
    color: #ffffff;
  }
  .mgBottomSmall {
    margin: 20px 0 0 0;
    padding: 5px;
  }
}
.witdhTwo {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthThree {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}
</style>
