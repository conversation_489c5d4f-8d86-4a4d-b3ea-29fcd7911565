<template>
  <el-dialog
    :close-on-click-modal="false"
    :show-close="false"
    :visible.sync="dialogSm"
    :title="isSuccess ? '上号成功，正在录号中...' : '设备正在上号中(预计3分钟)'"
    custom-class="accountIntervalDialog"
    width="683px"
    center
    class="dialogSm"
  >
    <div style="position: relative; z-index: 3; padding: 25px 31px 0px 33px">
      <img
        v-if="!isSuccess && !checkNeedSms && !stateImg"
        class="accountIntervalDialogBk1"
        src="../../../../static/imgs/dialog_bk.png"
        alt=""
      />
      <!-- <img
        v-if="!isSuccess && stateImg"
        class="accountIntervalDialogBk2"
        src="../../../../static/imgs/dialog_bk.png"
        alt=""
      /> -->
      <img
        v-if="isSuccess"
        class="accountIntervalDialogBk3"
        src="../../../../static/imgs/dialog_bk.png"
        alt=""
      />

      <div class="accountIntervalDialog3">
        <img
          src="../../../../static/imgs/dialog_logo_text.svg"
          class="accountIntervalDialog_left_logo"
          alt=""
        />

        <!-- <span class="forgetPwdDialogContentText" @click="firgetBtn"
          >修改密码</span
        > -->
        <!-- -->
        <div v-if="!isSuccess">
          <span class="accountIntervalDialog3_right_title">
            请勿关闭此页面</span
          >
          <!-- <slot name="right_title"></slot> -->
        </div>
      </div>
      <!--  -->
      <template v-if="!isSuccess">
        <!-- <p style="text-align: center">
        <img
          src="../../../../static/codeBg.png"
          style="width: 40px; height: auto"
        />请勿关闭此页面...
      </p> -->
        <div
          style="
            text-align: center;
            color: #1b1b1b;
            font-family: YouSheBiaoTiHei;
            font-size: 26px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-top: 43px;
          "
        >
          正在上号中（预计3分钟）
        </div>
        <div
          style="
            color: #969696;
            text-align: center;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 30px;
            letter-spacing: 0.64px;
          "
        >
          可能需要填写验证码或扫码上号，请您耐心等待!
        </div>
        <!-- 进度条 -->
        <div
          class="progressBox accountInterval_progress"
          style="margin-bottom: 20px; margin-top: 20px"
        >
          <el-progress
            :percentage="percent"
            :stroke-width="22"
            :show-text="false"
            status="exception"
          />
        </div>
        <!-- 验证码操作 -->
        <!--  -->
        <template v-if="checkNeedSms">
          <div
            style="
              margin: 0 auto;
              width: 500px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              padding-bottom: 38px;
            "
          >
            <el-form ref="formSms" :model="formSms">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入短信验证码',
                    trigger: 'blur',
                  },
                ]"
                label-width="0"
                style="margin-bottom: 0"
              >
                <el-input
                  v-model="formSms.sms"
                  autocomplete="off"
                  placeholder="请输入短信验证码"
                  maxlength="6"
                  style="width: 500px"
                  class="formSms_input"
                ></el-input>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button
                class="stateImgSubmitBtn"
                type="primary"
                @click="handelSmsSubmit"
                >提交</el-button
              >
            </div>
          </div>
        </template>
        <!-- 扫码操作 -->
        <!--  v-if="stateImg"  -->
        <template >
          <div  v-if="stateImg"  class="stateImgBox">
            <div v-if="productCategoryId==75" class="stateImgText" style="margin-bottom: 20px">
              请使用逆水寒手游客户端进行扫码授权
            </div>
            <!-- style="width: 200px;height: 200px;"  -->
            <el-image :src="stateImg" class="stateImg" fit="contain"></el-image>
            <div v-if="productCategoryId==115" class="stateImgText">
              <div v-if="qrcode_type=='qq'" style="font-size: 16px;    display: flex; align-items: center;">
                <IconFont
                :size="20"
                style="margin-right: 6px;  margin-top: -0px"
                icon="QQ"
              />
              请使用QQ扫码
              </div>
             <div v-if="qrcode_type=='weixin'" style="font-size: 16px;    display: flex;align-items: center;">
              <IconFont
                :size="20"
                style="margin-right: 6px;  margin-top: -0px"
                icon="weixin1"
              />请使用微信扫码
             </div>
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button
                class="stateImgSubmitBtn"
                type="primary"
                @click="handleScanCode"
                >已完成操作</el-button
              >
            </div>
          </div>
        </template>
      </template>
      <!-- v-else -->
      <template v-else>
        <div class="account_success_box">
          <div v-if="productCategoryId!=82" class="title">上号成功，正在录号中...</div>
          <div v-else class="title">录号成功，正在审核中...</div>
          <!-- <p style="text-align: center">
            <img
              src="../../../../static/codeBg.png"
              style="width: 40px; height: auto"
            />预计约30分钟，在此期间请不要顶号～
          </p> -->
          <div v-if="productCategoryId!=82" class="text">预计约30分钟，在此期间请不要顶号～</div>
          <div v-if="userInfo.isWxPush !== 1" style="display: flex;flex-direction: column;align-items: center;">
            <div style="position: relative">
              <canvas
                id="wx_qRcode"
                style="width: 128px; height: 128px; border: 1px solid #ffb74a"
              >
              </canvas>
              <img
                style="
                  width: 34.26px;
                  height: 34.26px;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
                src="../../../../static/imgs/qrcode_wechat.png"
                alt=""
              />
            </div>
            <p
              style="
                font-size: 16px;
                margin-top: 20px;
                font-family: PingFang Sc;
                color: #1b1b1b;
                line-height: 0px;
              "
            >
              关注公众号开启提醒享受服务以下服务
            </p>
            <p style="line-height: 0; margin-top: 10px">
              审核通知｜上架通知｜交易通知
            </p>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button
              style="margin-top: 16px"
              class="stateImgSubmitBtn"
              type="primary"
              @click="handelFinish"
              >我已知晓</el-button
            >
          </div>
        </div>
      </template>
    </div>
  </el-dialog>
</template>
<script>
import {
  recordTaskDetail,
  taskUpdate,
  recordTaskSmsCode,
} from '@/api/submitAccount.js';
import { getWxQrcode } from '@/api';
import { mapState } from 'vuex';
import QRCode from 'qrcode';
export default {
  props: {
    taskId: {
      type: [String, Number],
      default: '',
    },
    productCategoryId:{
      type: [String, Number],
      default: '',
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  data() {
    return {
      dialogSm: false,
      smsCode: '',
      checkNeedSms: false,
      smcCodeInputTime: 0,

      percent: 0,
      stateImg: '',
      qrcode_type:'',
      // stateImg:'https://images2.kkzhw.com/mall/images2/********/ZR1SHIy_1730093520688.jpg',

      formSms: {
        sms: '',
      },

      isSuccess: false,
      picUrl: '',
    };
  },
  beforeDestroy() {
    this.stl = clearInterval(this.stl);
    this.stl2 = clearInterval(this.stl2);
  },
  watch: {
    isSuccess(newVal) {
      if (newVal) {
        this.goWechat();
      }
    },
  },
  mounted() {
    this.dialogSm = true;
    this.startTask(this.taskId);
  },
  methods: {
    //关注公众号
    goWechat() {
      getWxQrcode().then((res) => {
        let msg;
        const checkAndExecute = () => {
          msg = document.getElementById('wx_qRcode');
          if (msg) {
            this.picUrl = res.data.url;
            this.creatQrCode();
          } else {
            setTimeout(checkAndExecute, 100); // 每100毫秒检查一次
          }
        };
        setTimeout(checkAndExecute, 100); // 初始延迟1秒后开始检查
      });
    },
    creatQrCode() {
      var that = this;
      const picUrl = this.picUrl;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 128, //宽
        height: 128, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };

      let msg = document.getElementById('wx_qRcode');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          that.$message.error('二维码加载失败');
        } else {
          const canvas = msg.getContext('2d');
          const logoWidth = 34.56;
          const logoHeight = 34.56;
          const logo = new Image();
          logo.src = '../../../../static/imgs/qrcode_wechat.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    // 图片扫码完成
    handleScanCode() {
      taskUpdate(this.upSmsObj).then((res) => {
        if (res.code == 200) {
          this.stateImg = '';
        }
      });
    },
    // 验证码提交
    handelSmsSubmit() {
      this.$refs.formSms.validate((valid) => {
        if (valid) {
          this.listLoading = true;
          let data = {
            taskId: this.taskId,
            smsCode: this.formSms.sms,
          };
          recordTaskSmsCode(data)
            .then((res) => {
              if (res.code == 200) {
                this.checkNeedSms = false;
                this.formSms.sms = '';
                this.smcCodeInputTime = this.smcCodeInputTime + 1;
              }
            })
            .finally(() => {
              this.listLoading = false;
            });
        }
      });
    },
    // 开始任务轮询
    startTask(taskId) {
      this;
      this.stl2 = setInterval(() => {
        this.percent = this.percent + 0.02;
        if (this.percent >= 99) {
          this.stl2 && clearInterval(this.stl2);
        }
      }, 34);
      this.stl = setInterval(() => {
        recordTaskDetail(taskId).then((res) => {
          if (res.code == 200 && res.data && res.data.propertyBag) {
            const obj = JSON.parse(res.data.propertyBag);
            if (obj.state.includes('need_show_')) {
              obj.state = `${obj.state}_done`;
              this.upSmsObj = res.data;
              this.upSmsObj.propertyBag = JSON.stringify(obj);

              this.stateImg = obj.state_img;
              this.qrcode_type=obj.qrcode_type
            } else if (
              obj.state === 'need_sms_code' &&
              this.smcCodeInputTime < 1
            ) {
              // 验证码已提交不处理弹窗，因为验证码提交后台接收有时间差，防止重复弹窗
              this.checkNeedSms = true;
            } else if (
              obj.state === 'need_sms_code2' &&
              this.smcCodeInputTime < 2
            ) {
              // 处理一次验证码输错情况，如果输错重新弹
              this.checkNeedSms = true;
            } else if (
              obj.state === 'login_success' ||
              obj.state === 'login_timeout' ||
              obj.state === 'login_fail'
            ) {
              this.stl = clearInterval(this.stl);
              this.stl2 = clearInterval(this.stl2);
              this.smcCodeInputTime = 0;
              this.checkNeedSms = false;
              this.smsCode = '';
              this.percent = 0;

              this.isSuccess = obj.state === 'login_success';
              if (!this.isSuccess) {
                this.handelFinish();
              }
            }
          }
        });
      }, 5000);
    },
    handelFinish() {
      this.dialogSm = false;
      this.$emit('finish', this.isSuccess);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .accountIntervalDialog {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  border-radius: 26px;
  position: relative;
  // cursor: pointer;
  text-decoration: none;
  color: #fff;
}

::v-deep .accountIntervalDialog::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px;
  background: #fffcf9;
  line-height: 39px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
}
::v-deep .accountIntervalDialog .el-dialog__body {
  min-height: 291px;
  padding: 0px;
}
::v-deep .accountIntervalDialog .el-dialog__header {
  display: none;
}
.accountIntervalDialog_left_logo {
  width: 229px;
  height: 50px;
  position: relative;
  z-index: 22;
}
.accountIntervalDialog3 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
}
.accountIntervalDialog3_right_title {
  color: #ff720c;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.8px;
}
.accountInterval_progress {
  width: 500px;
  height: 26.6667px;
  margin: 0 auto;
}
.accountInterval_progress /deep/ .el-progress-bar__inner {
  height: 26.6667px !important;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
}
.accountInterval_progress /deep/ .el-progress-bar__outer {
  height: 26.6667px !important;
  background: linear-gradient(
    190.21deg,
    #fff5ed 3.42%,
    #ffe1c6 52.35%,
    #ffebd9 78.22%,
    #ffebd9 114.78%
  );
}
.accountIntervalDialogBk1 {
  width: 233.852px;
  height: 246px;
  position: absolute;
  top: 25px;
  right: 76.64px;
  z-index: -1;
}
.stateImgBox {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-bottom: 38.33px;
}
.stateImgText {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.8px;
}
.stateImgSubmitBtn {
  margin-top: 20px;
  // border: 1px solid #ffddbe;
  border: none; /* 去掉原本的外边框 */
  // border-radius: 5px;
  outline: 1px solid #ffddbe;
  border-radius: 60px;
  height: 50px;
  padding: 0px 48px;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
}
.stateImgSubmitBtn:hover {
  border: none;
  outline: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
}
.accountIntervalDialogBk2 {
  width: 353.63px;
  height: 372px;
  position: absolute;
  top: 225px;
  right: 32.37px;
  z-index: -1;
}
.accountIntervalDialogBk3 {
  width: 195.827px;
  height: 206px;
  position: absolute;
  top: 42px;
  right: 111.27px;
  z-index: -1;
}
.account_success_box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-bottom: 46px;
  margin-top: 43px;
  .title {
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 26px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 84.615% */
  }
  .text {
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px; /* 187.5% */
    letter-spacing: 0.64px;
    margin-top: 12px;
  }
}
.formSms_input /deep/ .el-input__inner {
  border-radius: 60px;
  height: 52px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #1b1b1b;
  font-family: 'PingFang SC';
  &::placeholder {
    color: #969696;
  }
}
</style>
