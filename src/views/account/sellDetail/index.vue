<template>
  <div class="page_comStyle">
    <div class="orderTable_body">
      <div class="spaceStart row-line">
        <div>商品编号：</div>
        <div>{{ productDetail.productSn }}</div>
      </div>
      <div class="spaceBetween tableBody_box">
        <div
          class="spaceStart widthOne cursor"
          @click="palyPage(productDetail)"
        >
          <div class="orderShop_pic">
            <el-image
              :src="productDetail.pic"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
          <div>
            <div class="orderShop_tit text_linTwo">
              {{ productDetail.name }}
            </div>
            <div class="orderShop_subT">
              {{ productDetail.productCategoryName }}
            </div>
          </div>
        </div>
      </div>
      <div class="spaceBetween row-line">
        <div>游戏：</div>
        <div>
          {{ productDetail.productCategoryName }}
        </div>
      </div>
      <div class="spaceBetween row-line">
        <div>区服：</div>
        <div>
          {{ productDetail.gameAccountQufu }}
        </div>
      </div>
      <div class="spaceBetween row-line">
        <div>商品价格：</div>
        <div>¥{{ productDetail.price }}</div>
      </div>
      <div class="spaceBetween row-line">
        <div>商品编号：</div>
        <div>{{ productDetail.productSn }}</div>
      </div>
      <div class="spaceBetween row-line">
        <div>创建时间：</div>
        <div>{{ productDetail.createTime | formatTimetoSS }}</div>
      </div>
      <div class="spaceBetween row-line">
        <div></div>
        <div
          v-if="productDetail.pushStatus == 0 && productDetail.pushType == 1"
          class="widthFour"
        >
          <el-button type="primary" @click="startLuhao">授权上号</el-button>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogSm" width="30%" title="正在上号中">
      <div style="margin-bottom: 30px">
        检测到异地登录需要<span class="light">短信验证码</span
        >，已发送至绑定手机<span class="light">{{ bindname }}</span
        >,请注意查收
      </div>
      <el-form ref="form" :model="formSm" :rules="rules">
        <el-form-item label-width="0" prop="sms">
          <el-input
            v-model="formSm.sms"
            autocomplete="off"
            placeholder="请输入短信验证码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sendCodeCancel">稍后上号</el-button>
        <el-button type="primary" @click="doSendCode">授权上号</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { sendSm, startLuhao } from '@/api/submitAccount.js';
import isLogin from '@/utils/isLogin';
import { getDetail } from '@/api/playDetail';
export default {
  components: {},
  data() {
    return {
      productDetail: {},
      dialogSm: false,
      bindname: '',
      formSm: {
        sms: '',
      },
      needShowSm: this.$route.query.needShowSm,
      productId: this.$route.query.productId,
      productSn: this.$route.query.productSn,
      rules: {
        sms: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
      },
    };
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.getDetail();
  },
  methods: {
    startLuhao() {
      let data = {
        productSn: this.productSn,
      };
      startLuhao(data).then((res) => {
        if (res.code == 200) {
          this.dialogSm = true;
        }
      });
    },
    sendCodeCancel() {
      this.dialogSm = false;
      this.$router.replace({
        path: `/account/sellDetail?productId=${this.productId}&productSn=${this.productSn}`,
      });
    },
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
    getDetail() {
      getDetail(this.productId).then((res) => {
        this.productDetail = res.data.product;
        this.getBindName(res.data);
        if (this.needShowSm) {
          this.startLuhao();
        }
      });
    },
    getBindName(data) {
      const { productAttributeList, productAttributeValueList } = data;
      productAttributeList.forEach((ele) => {
        if (ele.name == '游戏账号') {
          const findIt = productAttributeValueList.find(
            (item) => item.productAttributeId == ele.id,
          );
          if (findIt) {
            this.bindname = findIt.value.trim();
          }
        }
      });
    },
    doSendCode() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let data = {
            productSn: this.productSn,
            smsCode: this.formSm.sms,
          };
          sendSm(data).then((res) => {
            if (res.code == 200) {
              this.$message.success('提交成功');
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.row-line {
  margin: 15px 0;
}
.baopei_box {
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
  height: 160px;
  padding-left: 16px;
}
.midline {
  border-top: 1px dashed #ccc;
  margin-top: 14px;
}
.order-info {
  padding-left: 16px;
  width: 410px;
  .column {
    height: 30px;
    font-size: 14px;
    margin-top: 14px;
    line-height: 30px;
    color: #565656;
    .price {
      color: #f7423f;
    }
  }
}
.title-box {
  display: inline-block;
  font-size: 17px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  position: relative;
}
.compensate_wrap {
  width: 340px;
  margin-right: 10px;
  box-sizing: border-box;
  padding: 15px;
  min-height: 106px;
  border: 1px solid #eeeeee;
  border-radius: 6px;
  font-size: 14px;
  color: #909090;
  transition: all 0.3s;
  .compensate_tit {
    font-size: 16px;
    color: #222222;
    padding-bottom: 18px;
  }
  .payPrice {
    font-size: 20px;
    font-weight: 600;
    color: #f7423f;
  }
}
.compensate_wrap.active {
  background: #fff8ef;
  border: 1px solid #ffdcba;
}
.compensate_wrap.active2 {
  background: #eff7ff;
  border: 1px solid #0082ff;
}

.orderTable_body {
  border-radius: 2px;
  padding: 20px 15px;
}
.tableBody_box {
  box-sizing: border-box;
  padding: 15px;
  font-size: 14px;
  font-family: San Francisco Display;
  color: #222222;
}
.orderShop_pic {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 14px;
}

.widthOne {
  flex-shrink: 0;
  width: 350px;
}
.widthTwo {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthThree {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthFour {
  flex-shrink: 0;
  width: 100px;
  text-align: center;
}
.widthFive {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}
.widthSix {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}
.widthSeven {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}

.orderShop_tit {
  line-height: 20px;
  width: 300px;
  padding-top: 2px;
}
.light {
  color: #fc6116;
}
.orderShop_tit {
  line-height: 20px;
  width: 300px;
  padding-top: 2px;
}
.orderShop_subT {
  font-size: 12px;
  color: #909090;
  padding-top: 7px;
  line-height: 18px;
}
</style>
