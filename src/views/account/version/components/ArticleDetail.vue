<template>
  <div class="createPost-container">
    <el-form
      v-loading.fullscreen.lock="listLoading"
      ref="postForm"
      :model="postForm"
      :rules="rules"
      class="form-container"
      label-width="120px"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <p class="success-content" style="width: 94%; margin: 30px auto">
        APP配置信息，请谨慎操作
      </p>
      <el-card class="box-card" style="width: 94%; margin: 0 auto">
        <div slot="header" class="clearfix">
          <span>版本基础信息</span>
        </div>

        <div class="createPost-main-container">
          <el-form-item label="选择平台" prop="type">
            <el-radio-group v-model="postForm.type">
              <el-radio :label="1">IOS</el-radio>
              <el-radio :label="2">安卓</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="title" label="最新版本">
            <el-input v-model="postForm.title" placeholder="请输入最新版本号" />
          </el-form-item>

          <el-form-item label="版本状态" prop="status">
            <el-radio-group v-model="postForm.status">
              <el-radio :label="1">全量发布</el-radio>
              <el-radio :label="0">审核中</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="url" label="下载地址">
            <el-input v-model="postForm.url" placeholder="请输入下载地址" />
          </el-form-item>

          <el-form-item label="更新内容" prop="content">
            <el-input
              v-model="postForm.content"
              type="textarea"
              placeholder="请输入更新内容"
            />
            <div>多个请以英文逗号隔开</div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm('postForm')"
              >保存提交</el-button
            >
            <el-button @click="onCancel('postForm')">重置取消</el-button>
          </el-form-item>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { versionSaveApi, versionDetailApi } from '@/api/index';

const defaultForm = {
  content: '', // 更新内容
  title: '', // 版本号
  type: 1, // 平台
  url: '', // 下载地址
  status: 1, // 版本状态
};

export default {
  msg: 'ArticleDetail',
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      listLoading: false,
      postForm: Object.assign({}, defaultForm),
      rules: {
        title: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        content: [
          { required: true, message: '请输入版本更新内容', trigger: 'blur' },
        ],
        type: [{ required: true, message: '请选择平台', trigger: 'blur' }],
        url: [{ required: true, message: '请输入下载地址', trigger: 'blur' }],
        status: [
          { required: true, message: '请选择版本状态', trigger: 'blur' },
        ],
      },
    };
  },
  computed: {},
  mounted() {},
  /**
   * 判断是新增 还是 编辑
   */
  created() {
    if (this.isEdit) {
      const id = this.$route.query && this.$route.query.id;
      this.fetchData(id);
    } else {
      this.postForm = Object.assign({}, defaultForm);
    }
  },
  methods: {
    /**
     * 详情初始化数据
     **/
    fetchData(id) {
      this.listLoading = true;
      versionDetailApi({
        id: id,
      }).then((response) => {
        if (response.code == 200) {
          this.postForm = response.data;
        } else {
          this.$message.error(response.msg);
        }
        this.listLoading = false;
      });
    },
    /**
     * 保存发布
     **/
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitData();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    /**
     * 提交数据
     **/
    submitData() {
      this.listLoading = true;
      versionSaveApi(this.postForm).then((response) => {
        if (response.code == 200) {
          this.backFun();
        } else {
          this.$message.error(response.msg);
        }
        this.listLoading = false;
      });
    },
    /**
     * 提交数据成功回调
     */
    backFun() {
      if (this.isEdit) {
        this.$message({
          message: '版本编辑成功！',
          type: 'success',
        });
      } else {
        this.$message({
          message: '版本发布成功！',
          type: 'success',
        });
      }
      setTimeout(() => {
        this.$router.push({
          path: '/data/verision',
        });
      }, 1500);
    },
    /**
     * 取消返回
     **/
    draftForm() {
      this.$router.push({
        path: '/data/verision',
      });
    },
  },
};
</script>

<style>
.header_fixed {
  padding: 15px 0 15px;
  padding-left: 120px;
}
.draft {
  /*background: linear-gradient(90deg, #20b6f9 0%, #20b6f9 0%, #2178f1 100%, #2178f1 100%);*/
  background: #d0d0d0;
  overflow: hidden;
}
.icon_uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.icon_uploader .el-upload:hover {
  border-color: #409eff;
}

.icon_uploader .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

.icon_uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.ql-container.ql-snow {
  min-height: 300px;
}
.pic_itemUpload {
  width: 200px;
  height: 200px;
  position: relative;
  border: 1px solid #f3f3f3;
  border-radius: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  overflow: hidden;
}
.delet_item {
  position: absolute;
  width: 30px;
  height: 30px;
  background: #fe5a1e;
  z-index: 10;
  right: 0px;
  top: 0px;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 26px;
  font-size: 22px;
  cursor: pointer;
}
.config_items {
  margin-top: 20px;
  display: flex;
}
/*.el-tag + .el-tag {
	    margin-left: 10px;
	  }
	  .button-new-tag {
	    margin-left: 10px;
	    height: 32px;
	    line-height: 30px;
	    padding-top: 0;
	    padding-bottom: 0;
	  }
	  .input-new-tag {
	    width: 90px;
	    margin-left: 10px;
	    vertical-align: bottom;
	  }*/
</style>
