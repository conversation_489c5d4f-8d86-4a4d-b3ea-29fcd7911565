<template>
  <div class="app-container">
    <el-tabs type="border-card">
      <!-- 商品列表 -->
      <el-tab-pane label="版本管理">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <!-- <el-form-item label="banner名称">
                        <el-input v-model="formInline.keyword" placeholder="请输入商品名称"></el-input>
                    </el-form-item> -->
          <!-- <el-form-item label="banner状态">
                        <el-select v-model="formInline.status" placeholder="banner状态">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="上架中" :value="1"></el-option>
                            <el-option label="下架中" :value="0"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item >
                        <el-button type="primary" icon="el-icon-search"  @click="searchFun">查询</el-button>
                    </el-form-item> -->
          <el-form-item style="float: right">
            <el-button type="success" @click="addNews">新建版本</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading.fullscreen.lock="listLoading"
          :data="initDate"
          border
          style="width: 100%"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <el-table-column :index="indexMethod" type="index" fixed>
          </el-table-column>
          <el-table-column
            prop="title"
            label="版本号"
            width=""
          ></el-table-column>

          <el-table-column prop="type" label="版本平台" width="">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 1" type="text">IOS</span>
              <span v-if="scope.row.type == 2" type="text">安卓</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            label="更新内容"
            width=""
          ></el-table-column>

          <el-table-column prop="status" label="版本状态" width="">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 1" type="success"
                >审核通过</el-tag
              >
              <el-tag v-if="scope.row.status == 0" type="danger">审核中</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="created_at"
            label="提交时间"
            width="180"
          ></el-table-column>

          <el-table-column fixed="right" label="操作" width="">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleClick(scope.row.id)"
                >详情/编辑</el-button
              >
              <!--   <el-button size="small" type="success" v-if="scope.row.status == 0" @click="handleNew(scope.row.id, 1)">上架</el-button>
                            <el-button size="small" type="danger" v-if="scope.row.status == 1" @click="handleNew(scope.row.id, 0)">下架</el-button> 

                            <el-button v-if="scope.row.status == 0" type="danger" size="small" icon="el-icon-delete" circle @click="deletBanner(scope.row.id)"></el-button>-->
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          :total="totalPage"
          style="margin-top: 20px"
          background
          layout="prev, pager, next,jumper"
          @current-change="pageFun"
        >
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { versionListApi, handleBannerApi } from '@/api/index';

export default {
  data() {
    return {
      page: 1, // 当前页码
      listLoading: false,
      totalPage: 10, // 总页码
      formInline: {
        keyword: '',
        status: '',
      },
      initDate: [],
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    /**
     * 初始化数据
     */
    fetchData() {
      this.listLoading = true;
      var json = {
        page_index: this.page,
        page_size: 20,
      };
      versionListApi(json).then((response) => {
        if (response.code == 200) {
          this.initDate = response.data.list;
          this.totalPage = response.data.pages * 10;
        } else {
          this.$message.error(response.msg);
        }
        this.listLoading = false;
      });
    },
    /**
     * 搜索
     */
    searchFun() {
      this.page = 1;
      this.fetchData();
    },
    /**
     * 广告装填
     */
    handleNew(id, num) {
      this.$confirm('您将改变此广告的上架状态, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        var json = {
          id: id,
          status: num,
        };
        handleBannerApi(json).then((response) => {
          if (response.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.fetchData();
          }
          this.listLoading = false;
        });
      });
    },
    /**
     * 广告删除
     */
    deletBanner(id) {
      this.$confirm('您将删除此广告, 删除后不可恢复, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        deletBannerApi({
          id: id,
        }).then((response) => {
          if (response.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.fetchData();
          }
          this.listLoading = false;
        });
      });
    },
    /**
     * 分页
     */
    pageFun(val) {
      this.page = `${val}`;
      this.fetchData();
    },
    indexMethod(index) {
      return index + 1;
    },
    /**
     * 新建轮播
     */
    addNews() {
      this.$router.push({
        path: '/data/verisionAdd',
      });
    },
    /**
     * 轮播详情
     */
    handleClick(id) {
      this.$router.push({
        path: '/data/verisionDetail',
        query: {
          id: id,
        },
      });
    },
  },
};
</script>

<style scoped>
.line {
  text-align: center;
}
</style>
