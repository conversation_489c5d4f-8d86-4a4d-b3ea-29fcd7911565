<template>
    <div
      :class="`coupon-item-${isCanUse === '可用' ? item.useStatus : 1}`"
      class="coupon-item"
      @click="handleClick(item)"
    >
      <div class="price">
        <span style="font-size: 14px">￥</span>
        <span class="text">{{ item.amount }}</span>
      </div>
      <div class="info-box">
        <div class="info">
          <div class="title">{{ item.couponName }}</div>
          <div class="time">
            有效期：{{ item.startTime | formatTime('YYYY.MM.DD') }}-{{
              item.endTime | formatTime('YYYY.MM.DD')
            }}
          </div>
        </div>
        <div
          v-if="isShowBtn && type === 'btn'"
          class="btn"
          @click.stop="toUse(item)"
        >
          <span v-if="item.useStatus === 0">去使用</span>
          <span v-else-if="item.useStatus === 2">已失效</span>
          <span v-else>已使用</span>
        </div>
        <div
          v-if="type === 'check' && item.useStatus === 0 && isCanUse === '可用'"
          class="check-box"
        >
          <img
            v-if="selectItem.id === item.id"
            class="icon"
            src="../../../../../static/imgs/coupon/actived.png"
            alt=""
          />
          <img
            v-else
            class="icon"
            src="../../../../../static/imgs/coupon/no-actived.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </template>
  <script>
  export default {
    name: 'CouponItem',
    props: {
      selectItem: {
        type: Object,
        default: () => {},
      },
      type: {
        type: String,
        default: 'btn',
      },
      isShowBtn: {
        type: Boolean,
        default: true,
      },
      item: {
        type: Object,
        default: () => {},
      },
      isCanUse: {
        type: String,
        default: '可用',
      },
    },
    data() {
      return {};
    },
    methods: {
      toUse(item) {
        if (this.item.useStatus !== 0) {
          return;
        }
        this.$emit('use', item);
      },
      handleClick(item) {
        if (
          this.type === 'check' &&
          this.item.useStatus === 0 &&
          this.isCanUse === '可用'
        ) {
          this.$emit('check', item);
        } else {
          this.$emit('click', item);
        }
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .coupon-item {
    display: flex;
    align-items: center;
    height: 84px;
    margin-right: 30px;
    margin-bottom: 30px;
    cursor: pointer;
    &:last-child {
      margin-bottom: 0;
    }
    .price {
      width: 90px;
      height: 84px;
      line-height: 84px;
      display: flex;
      align-items: baseline;
      justify-content: center;
      font-size: 24px;
      color: #ff5500;
      font-weight: 600;
      border: 1px solid #ffe2db;
      box-sizing: border-box;
      background: #fdf4f5;
      border-radius: 8px;
      position: relative;
      padding: 0 5px;
      .text {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
      }
  
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -2px;
        transform: translate(50%, -50%);
        width: 4px;
        height: 70px;
        background: #fdf4f5;
        border-left: 1px dashed #ffccc0;
      }
    }
    .info-box {
      width: 260px;
      height: 100%;
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      padding: 0 10px 0 8px;
      border: 1px solid #ffe2db;
      box-sizing: border-box;
      background: #fdf4f5;
      border-radius: 8px;
      .info {
        flex: 1;
        overflow: hidden;
        margin-right: 5px;
        .title {
          font-size: 14px;
          color: #1f1f1f;
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          white-space: nowrap;
        }
        .time {
          font-size: 12px;
          color: #9a9a9a;
          margin-top: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          white-space: nowrap;
        }
      }
      .btn {
        width: 56px;
        height: 28px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .check-box {
        .icon {
          display: block;
          width: 18px;
          height: 18px;
        }
      }
    }
  }
  .coupon-item-0 {
    .btn {
      color: #fff;
      background: #ff6948;
    }
  }
  .coupon-item-1,
  .coupon-item-2 {
    .price {
      color: #9a9a9a;
      background: #f4f5f7;
      border-color: #e9e9e9;
      &::after {
        border-left-color: #cdcfce;
        background: #f4f5f7;
      }
    }
    .info-box {
      background: #f4f5f7;
      border-color: #e9e9e9;
      .info {
        .title {
          color: #9a9a9a;
        }
      }
      .btn {
        color: #9a9a9a;
      }
    }
  }
  </style>
  