<template>
    <div
      v-loading.fullscreen.lock="listLoading"
      element-loading-span="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="page_comStyle"
      style="padding: 30px 40px; border-radius: 24px"
    >
      <div class="tabs">
        <div
          v-for="item in COUPON_STATUS"
          :key="item.value"
          :class="{ 'tabs-item-actived': status === item.value }"
          class="tabs-item"
          @click="changeStatus(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="coupon-list">
        <template v-if="couponList.length">
          <CouponItem
            v-for="item in couponList"
            :key="item.id"
            :item="item"
            @click="toDetails"
            @use="toBuy"
          />
        </template>
        <div v-else class="list_null_sorry" style="margin-top: 3px">
          <img
            style="width: 54px; height: 56px"
            src="../../../../static/imgs/null.png"
            alt=""
          />
          <div class="sorry_text">暂时没有您要的数据</div>
        </div>
      </div>
      <CouponDetailsPopup
        :visible="isShowCouponDetailsPopup"
        :dialog-width="'491px'"
        class="bargainDialog"
        @dialogClone="dialogClone"
      >
        <template slot="title">
          <span class="bargain_right_title">代金券详情</span>
        </template>
        <template slot="content">
          <div class="popup-content">
            <CouponItem :is-show-btn="false" :item="couponItem" />
            <div class="coupon-content-item">
              <div class="label">代金券名称：</div>
              <div class="value">{{ couponItem.couponName }}</div>
            </div>
            <div class="coupon-content-item">
              <div class="label">金额：</div>
              <div class="value">￥{{ couponItem.amount }}</div>
            </div>
            <div class="coupon-content-item">
              <div class="label">状态：</div>
              <div class="value">
                <span v-if="couponItem.useStatus === 0">待使用</span>
                <span v-else-if="couponItem.useStatus === 2">已失效</span>
                <span v-else>已使用</span>
              </div>
            </div>
            <div class="coupon-content-item">
              <div class="label">领取时间：</div>
              <div class="value">
                {{ couponItem.createTime | formatTime('YYYY.MM.DD HH:mm:ss') }}
              </div>
            </div>
            <div class="coupon-content-item">
              <div class="label">生效时间：</div>
              <div class="value">
                {{ couponItem.startTime | formatTime('YYYY.MM.DD HH:mm:ss') }}
              </div>
            </div>
            <div class="coupon-content-item">
              <div class="label">失效时间：</div>
              <div class="value">
                {{ couponItem.endTime | formatTime('YYYY.MM.DD HH:mm:ss') }}
              </div>
            </div>
            <div class="coupon-content-item">
              <div class="label">使用规则：</div>
              <div class="value">单笔订单最多同时使用一张</div>
            </div>
            <div class="coupon-content-item">
              <div class="label">退款规则：</div>
              <div class="value">
                <span v-if="couponItem.returnType === 0">退款不退券</span>
                <span v-else>退款退券</span>
              </div>
            </div>
            <div class="coupon-content-item">
              <div class="label">适用游戏：</div>
              <div class="value">{{ couponItem.useTypeNote }}</div>
            </div>
          </div>
        </template>
      </CouponDetailsPopup>
    </div>
  </template>
  <script>
  import CouponDetailsPopup from '@/components/borderDialog/index2.vue';
  import CouponItem from './modules/coupon-item.vue';
  import { getCouponList } from '@/api/coupon.js';
  export default {
    components: {
      CouponItem,
      CouponDetailsPopup,
    },
    data() {
      return {
        listLoading: false,
        status: -1,
        couponList: [],
        isShowCouponDetailsPopup: false,
        couponItem: {},
        COUPON_STATUS: [
          {
            value: -1,
            label: '全部',
          },
          {
            value: 0,
            label: '待使用',
          },
          {
            value: 2,
            label: '已失效',
          },
          {
            value: 1,
            label: '已使用',
          },
        ],
      };
    },
    mounted() {
      this.getCouponList();
    },
    methods: {
      // 列表加载
      async getCouponList() {
        this.listLoading = true;
        await getCouponList({
          useStatus: this.status === -1 ? undefined : this.status,
        }).then((res) => {
          if (res.code === 200) {
            const data = res.data || [];
            this.couponList = data.sort((a, b) => a.useStatus - b.useStatus);
          }
        });
        this.listLoading = false;
      },
      async changeStatus(item) {
        this.status = item.value;
        await this.getCouponList();
      },
      toBuy() {
        this.$router.push({
          path: '/gameList',
        });
      },
      toDetails(item) {
        this.couponItem = item;
        this.isShowCouponDetailsPopup = true;
      },
      dialogClone() {
        this.isShowCouponDetailsPopup = false;
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .tabs {
    display: flex;
    align-items: center;
    border-radius: 24px 24px 0 0;
    background: #f9f9f9;
    padding: 18px 40px;
    border: 1px solid #e4e4e4;
    border-bottom: none;
    .tabs-item {
      width: 128px;
      height: 20px;
      font-size: 14px;
      color: #9a9a9a;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
    }
    .tabs-item-actived {
      font-size: 16px;
      font-weight: 600;
      color: #1f1f1f;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -8px;
        transform: translateX(-50%);
        width: 18px;
        height: 3px;
        border-radius: 5px;
        background: #fde801;
      }
    }
  }
  .coupon-list {
    padding: 30px 24px;
    border: 1px solid #e4e4e4;
    border-top: none;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    border-radius: 0 0 24px 24px;
    .list_null_sorry {
      width: 100%;
      font-size: 18px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 64px 0px;
      background: #fff;
      border-radius: 24px;
      gap: 12px 0;
      color: #9a9a9a;
    }
  }
  ::v-deep .dialog-content {
    padding-top: 0 !important;
  }
  .popup-content {
    width: 351px;
    margin: 0 auto;
    ::v-deep .coupon-item {
      width: 100%;
      margin-bottom: 20px;
      margin-right: 0;
    }
    .coupon-content-item {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        min-width: 104px;
        white-space: nowrap;
        color: #9a9a9a;
      }
      .value {
        color: #1f1f1f;
        margin-left: 4px;
      }
    }
  }
  </style>
  