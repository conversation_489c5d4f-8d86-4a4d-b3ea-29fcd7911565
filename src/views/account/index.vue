<template>
  <div ref="bodyScroll" class="scrollPageSmoth">
    <div :class="boxBackgroundColor" class="accountCenterBk">
      <headerKk :active-index="index" />
      <div class="safe_width">
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          style="padding: 20px 0px"
          class="pdTopBottom my-bread"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item class="el-breadcrumb__inner_text"
            >个人中心</el-breadcrumb-item
          >
        </el-breadcrumb>
      </div>
      <div
        class="safe_width pdTopBottom spaceBetween"
        style="align-items: flex-start; padding: 0px; padding-bottom: 80px"
      >
        <div class="leftNav_container">
          <el-menu
            :default-active="$route.path"
            class="el-menu-vertical-demo"
            active-text-color="#FF6716"
            @open="handleOpen"
            @close="handleClose"
          >
            <router-link to="/account/center">
              <el-menu-item class="menuOne" index="/account/center">
                <!-- <i class="el-icon-menu"></i> -->
                <IconFont
                  :size="24"
                  style="margin: 0px 4px 0px -5px"
                  class="menu_title_icon"
                  icon="member"
                />
                <span slot="title">会员总览</span>
              </el-menu-item>
            </router-link>

            <el-submenu index="2">
              <template slot="title">
                <!-- <i
                style="font-weight: 600"
                class="el-icon-shopping-cart-full"
              ></i> -->
                <IconFont :size="17.29" class="menu_title_icon" icon="sale" />
                <span>买家中心</span>
              </template>
              <el-menu-item-group>
                <router-link to="/account/buyerOrder">
                  <el-menu-item index="/account/buyerOrder"
                    >我购买的</el-menu-item
                  >
                </router-link>
                <router-link to="/account/myBargain">
                  <el-menu-item index="/account/myBargain"
                    >发起的议价</el-menu-item
                  >
                </router-link>

                <!-- <router-link to="/account/collection">
                <el-menu-item index="/account/collection"
                  >我的收藏</el-menu-item
                >
              </router-link> -->
                <!-- <router-link to="/account/footerMark">
                <el-menu-item index="/account/footerMark"
                  >我的足迹</el-menu-item
                >
              </router-link> -->
              </el-menu-item-group>
            </el-submenu>
            <el-submenu index="1">
              <template slot="title">
                <!-- <i
                style="font-weight: 600"
                class="el-icon-shopping-cart-full"
              ></i> -->
                <IconFont :size="17.29" class="menu_title_icon" icon="buyer" />
                <span>卖家中心</span>
              </template>
              <el-menu-item-group>
                <router-link to="/account/accountList">
                  <el-menu-item index="/account/accountList"
                    >账号列表</el-menu-item
                  >
                </router-link>
                <router-link to="/allSell">
                  <el-menu-item index="/allSell">发布账号</el-menu-item>
                </router-link>
                <router-link to="/account/sellOrderList">
                  <el-menu-item index="/account/sellOrderList"
                    >订单列表</el-menu-item
                  >
                </router-link>
                <router-link to="/account/supplyBargain">
                  <el-menu-item index="/account/supplyBargain"
                    >降价请求</el-menu-item
                  >
                </router-link>
              </el-menu-item-group>
            </el-submenu>
            <el-submenu index="3">
              <template slot="title">
                <!-- <i class="el-icon-user-solid"></i> -->
                <IconFont
                  :size="17.29"
                  class="menu_title_icon"
                  icon="personal-cente"
                />
                <span>认证中心</span>
              </template>
              <el-menu-item-group>
                <router-link to="/account/baseNews">
                  <el-menu-item index="/account/baseNews"
                    >基本信息</el-menu-item
                  >
                </router-link>
                <router-link to="/account/approve">
                  <el-menu-item index="/account/approve">实名认证</el-menu-item>
                </router-link>
                <span @click.prevent.stop="goInentify">
                  <router-link to="/account/identify">
                    <el-menu-item index="/account/identify"
                      >人脸认证</el-menu-item
                    >
                  </router-link>
                </span>
                <span @click.prevent.stop="goPAyInentify">
                  <router-link to="">
                    <el-menu-item index="/account/payIdentify"
                      >包赔认证</el-menu-item
                    >
                  </router-link>
                </span>
              </el-menu-item-group>
            </el-submenu>

            <!-- <el-submenu index="4">
            <template slot="title">
              <IconFont :size="22.282" class="menu_title_icon" icon="price" />
              <span>议价管理</span>
            </template>
            <el-menu-item-group>
              <router-link to="/account/myBargain">
                <el-menu-item index="/account/myBargain">我的议价</el-menu-item>
              </router-link>
              <router-link to="/account/supplyBargain">
                <el-menu-item index="/account/supplyBargain"
                  >降价请求</el-menu-item
                >
              </router-link>
            </el-menu-item-group>
          </el-submenu> -->
          <router-link to="/account/coupon">
              <el-menu-item class="menuOne" index="/account/coupon">
                <img
                  style="
                    display: block;
                    width: 28px;
                    height: 29px;
                    margin: 0px 0px 0px -5px;
                  "
                  src="../../../static/imgs/coupon/logo.png"
                  alt=""
                  srcset=""
                />
                <span slot="title">我的代金券</span>
              </el-menu-item>
            </router-link>
            <router-link to="/account/footerMark">
              <el-menu-item class="menuOne" index="/account/footerMark">
                <!-- <i class="el-icon-menu"></i> -->
                <IconFont
                  :size="24"
                  style="margin: 0px 4px 0px -5px"
                  class="menu_title_icon"
                  icon="menu-spoor"
                />
                <span slot="title">我的足迹</span>
              </el-menu-item>
            </router-link>
            <router-link to="/account/collection">
              <el-menu-item class="menuOne" index="/account/collection">
                <!-- <i class="el-icon-menu"></i> -->
                <IconFont
                  :size="24"
                  style="margin: 0px 4px 0px -5px"
                  class="menu_title_icon"
                  icon="menu-favorite"
                />
                <span slot="title">我的收藏</span>
              </el-menu-item>
            </router-link>
          </el-menu>
        </div>

        <div class="right_container">
          <router-view />
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import { getCertDetail, certAdd } from '@/api/safeCenter';
export default {
  name: 'Home',
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      index: 7,
      activeNames: ['1'],
    };
  },
  computed: {
    boxBackgroundColor() {
      const path = this.$route.path;
      const accountCenterPaths = [
        '/account/center',
        '/account/baseNews',
        '/account/approve',
        '/account/identify',
        '/account/payIdentify',
        '/account/orderDetail',
        '/account/questions',
      ];
      return accountCenterPaths.includes(path)
        ? 'accountCenterBk'
        : 'accountCenterBk2';
    },
  },
  watch: {},
  mounted() {},
  methods: {
    handleClose() {},
    handleOpen() {},
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    goPAyInentify() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          if (res.data.faceStatus != 2) {
            this.$message.error('请先完成人脸认证！');
            this.$router.push('/account/identify');
          } else {
            this.$router.push('/account/payIdentify');
          }
        }
      });
    },
    goInentify() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          if (data.defaultStatus != 2) {
            this.$message.error('请先完成实名认证！');
            this.$router.push('/account/approve');
          } else {
            this.$router.push('/account/identify');
          }
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
::v-deep .el-menu {
  border: none !important;
}
.right_container {
  width: 895px;
  margin-left: 20px;
  flex: 1;
  border-radius: 24px;
  box-sizing: border-box;
}
.accountCenterBk {
  background: #fdf5ed;
}
.accountCenterBk2 {
  background: linear-gradient(
    180deg,
    #fdf5ed 36.61%,
    #fff 53.46%,
    #ffe1c3 100%
  );
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-menu {
  border: none !important;
}
.menuOne {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  text-indent: 0px;
  padding-left: 20px !important;
  height: 75.416px;
  display: flex;
  align-items: center;
  border-radius: 20.567px;
}
.menu_title_icon {
  margin-right: 6px;
}
.account_big_box {
  // background: linear-gradient(180deg, #fdf5ed, #fff, #ffe1c3);
  background: linear-gradient(
      180deg,
      #fdf5ed 0%,
      #fdf5ed 60%,
      #fff 70%,
      #ffe1c3 100%
    )
    local;
}
</style>
