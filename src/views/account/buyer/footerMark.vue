<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle account_table_box"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="spaceBetween colect_head colect_header">
      <div class="spaceStart">
        <!-- <img src="../../../../static/collect.png" class="colect_icon" /> -->
        <div>我的足迹</div>
      </div>
      <el-link class="clearFoot" type="primary" @click="clearFoot"
        >清空足迹<img
          style="width: 20px; margin-top: -3px"
          src="../../../../static/imgs/list_detail.svg"
          alt=""
      /></el-link>
    </div>

    <!-- table start -->

    <div v-if="initDate.length">
      <div class="orderTable_head spaceBetween">
        <div class="widthOne">账号信息</div>
        <!-- <div class="widthTwo">游戏名称</div> -->
        <div class="widthTwo">实付金额</div>
        <div style="width: 70px" class="widthThree">状态</div>
        <div style="width: 120px" class="widthFour"></div>
      </div>
      <div
        v-for="(item, index) in initDate"
        :key="index"
        class="orderTable_body"
      >
        <div class="spaceBetween tableBody_box">
          <div class="spaceStart widthOne cursor" @click="palyPage(item)">
            <div class="orderShop_pic">
              <el-image
                v-if="item.productPic"
                :src="item.productPic"
                style="width: 100%; height: 100%"
                fit="cover"
              ></el-image>
            </div>
            <div class="order_goods_box">
              <div>
                <div class="order_produceSn">【{{ item.productSn }}】</div>
                <div class="orderShop_tit text_linTwo">
                  {{ item.productSubTitle }}
                </div>
              </div>
              <div class="order_goods_name">
                {{ item.productCategoryName }}
              </div>
            </div>
          </div>
          <!-- <div class="widthTwo">{{ item.productCategoryName }}</div> -->
          <div class="widthTwo order_price">¥ {{ item.productPrice }}</div>
          <div style="width: 70px" class="widthThree">
            <!-- <el-tag v-if="item.productStatus == 1" class="tab_seller">{{
              item.productStatusTxt
            }}</el-tag>
            <el-tag v-else type="info">{{ getProductStatus(item) }}</el-tag> -->
            <div class="goods_status">
              {{
                item.productStatus == 1
                  ? item.productStatusTxt
                  : getProductStatus(item)
              }}
            </div>
          </div>
          <div style="width: 120px" class="spaceCenter widthFour">
            <div
              v-if="item.productStatus == 1"
              class="orderTable_btn solid mgBottomSmall accountOrderBtn"
              style="margin: 0px"
              @click="palyPage(item)"
            >
              立即购买
            </div>
            <!-- <div class="orderTable_btn plain" @click="delectAccFun(item)">
              删除足迹
            </div> -->
          </div>
        </div>
      </div>
      <!-- tabler end -->
      <div class="tabler_footer_pagination">
        <el-pagination
          :total="totalPage"
          layout=" pager,jumper"
          class="search_page_pagination"
          @current-change="pageFun"
        >
        </el-pagination>
      </div>
    </div>
    <div v-else class="list_null_sorry">
      <img
        style="width: 54px; height: 56px"
        src="../../../../static/imgs/null.png"
        alt=""
      />
      <div style="margin-left: 15.85px">
        <img
          style="width: 63px; height: 36px"
          src="../../../../static/imgs/sorry_text.svg"
          alt=""
        />
        <div class="sorry_text">暂时无相关数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';
import {
  getReadHistoryList,
  readHistoryDelete,
  readHistoryClear,
} from '@/api/playDetail.js';

export default {
  name: 'Collect',
  components: {},
  data() {
    return {
      listLoading: false,
      page: 1,
      totalPage: 10,
      initDate: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.initDateFun();
  },
  methods: {
    clearFoot() {
      this.$confirm('您将清空足迹, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        readHistoryClear().then((res) => {
          if (res.code == 200) {
            this.queryParams.pageNum = 1;
            this.initDateFun();
          }
        });
      });
    },
    getProductStatus(item) {
      if (item.productStatus == -1) {
        return item.productStatusTxt;
      } else {
        return '已售';
      }
    },
    // 初始化
    initDateFun() {
      this.listLoading = true;
      getReadHistoryList(this.queryParams)
        .then((response) => {
          if (response.code == 200) {
            this.initDate = response.data.list;
            this.totalPage = response.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     * 分页
     */
    pageFun(val) {
      this.queryParams.pageNum = `${val}`;
      this.initDateFun();
    },
    // 删除足迹
    delectAccFun(date) {
      const id = date.productId;
      this.$confirm('您将删除此条记录吗, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        readHistoryDelete({
          ids: id,
        }).then((response) => {
          this.listLoading = false;
          if (response.code == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            });
            this.initDateFun();
          }
        });
      });
    },
    // 账号详情
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style lang="scss" scoped>
.clearFoot {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
.clearFoot:hover {
  color: #1b1b1b;

  &::after {
    border: none;
  }
}
.colect_header {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 18.854px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.56px; /* 109.091% */
  letter-spacing: 0.44px;
}
</style>
