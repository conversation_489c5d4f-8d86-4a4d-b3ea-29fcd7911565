<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="page_comStyle"
    style="padding: 0px 0 39px 0; border-radius: 24px"
  >
    <div class="acountList_header">
      <div
        v-for="(item, index) in statusDate"
        :key="index"
        class="acountList_header_box"
      >
        <div
          :class="status == item.id ? 'active' : ''"
          class="title"
          @click="statusChoose(item)"
        >
          {{ item.name }}
        </div>
        <div class="divider"></div>
      </div>
    </div>

    <!-- search -->

    <div class="accountList_orderSearch_cont">
      <el-form
        :inline="true"
        :model="formInline"
        style="display: flex; align-items: center"
        class="demo-form-inline"
        @submit.native.prevent="onSubmit()"
      >
        <el-form-item label="订单编号">
          <el-input v-model="formInline.order_no" placeholder="请输入订单编号">
            <iconFont
              slot="suffix"
              :size="24"
              icon="web_search"
              color="#000000"
              style="margin-right: 14.16px; margin-top: 0px; cursor: pointer"
              @click="onSubmit"
          /></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            plain
            round
            class="reprovision_btn reprovision_reset_btn"
            @click="resetForm"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div style="padding: 0 40px; margin-top: 27px">
      <div v-if="initDate.length">
        <!-- table start -->
        <div class="orderTable_head spaceBetween">
          <div class="widthOne">商品信息</div>
          <div class="widthTwo">订单金额</div>
          <div class="widthThree">实付金额</div>
          <div class="widthFour">订单状态</div>
          <!-- <div class="widthSeven spaceCenter">操作</div> -->
        </div>

        <div
          v-for="(item, index) in initDate"
          :key="index"
          class="orderTable_body"
        >
          <div class="tableBody_head spaceBetween">
            <div>
              <!-- {{ item.productCategoryName }} &nbsp;&nbsp; -->
              <!-- 订单类型：{{ item.orderTypeName }}&nbsp;&nbsp; 
              订单编号： -->
              <span class="orderTable_left_title"
                >{{ item.orderTypeName }}：</span
              >{{ item.orderSn }}
              <IconFont
                :size="14"
                icon="copy"
                class="accountList_copy"
                @click="copyVal(item.orderSn)"
              />
            </div>
            <div>
              <span class="orderTable_left_title">下单时间：</span
              >{{ item.createTime | formatTime }}
            </div>
          </div>
          <div class="spaceBetween tableBody_box">
            <div class="spaceStart widthOne cursor" @click="palyPage(item)">
              <div class="orderShop_pic">
                <el-image
                  :src="item.productItem.productPic"
                  style="width: 100%; height: 100%"
                  fit="cover"
                ></el-image>
              </div>
              <div class="order_goods_box">
                <div>
                  <div class="order_produceSn">
                    【{{
                      (item.accountItem && item.accountItem.productSn) || ''
                    }}】
                  </div>
                  <div class="orderShop_tit text_linTwo">
                    {{ item.productItem.productName }}
                  </div>
                </div>
                <div class="orderShop_subT">
                  {{ item.productCategoryName }}
                </div>
              </div>
            </div>
            <div class="widthTwo order_price">¥ {{ item.totalAmount }}</div>
            <div class="widthThree order_price">¥ {{ item.payAmount }}</div>
            <div class="widthFour goods_status">
              {{ util.getStatus(item.orderStatus) }}
            </div>
          </div>
          <div
            class="spaceEnd"
            style="margin-right: 41.13px; margin-bottom: 20.56px"
          >
            <div
              v-if="canDel(item)"
              class="plain cancel_favorites"
              @click="delOrder(item.id)"
            >
              删除订单
            </div>
            <div
              v-if="item.orderStatus == 'WAIT_PAY'"
              class="orderTable_btn solid accountOrderBtn"
              @click="payNowOrder(item.id)"
            >
              立即支付
            </div>
            <div
              v-if="item.orderStatus == 'WAIT_PAY'"
              class="orderTable_btn solid accountOrderBtn"
              @click="cancelOrder(item.id)"
            >
              取消支付
            </div>

            <div class="orderTable_btn solid accountOrderBtn" @click="goChat(item)">
              联系客服
            </div>
          </div>
        </div>
        <!-- tabler end -->
        <div class="tabler_footer_pagination">
          <el-pagination
            :total="totalPage"
            layout="pager,jumper"
            class="search_page_pagination"
            @current-change="pageFun"
          >
          </el-pagination>
        </div>
      </div>
      <div v-else class="list_null_sorry" style="margin-top: 3px">
        <img
          style="width: 54px; height: 56px"
          src="../../../../static/imgs/null.png"
          alt=""
        />
        <div style="margin-left: 15.85px">
          <img
            style="width: 63px; height: 36px"
            src="../../../../static/imgs/sorry_text.svg"
            alt=""
          />
          <div class="sorry_text">暂时无相关数据</div>
        </div>
      </div>
    </div>
    <tipDialog
      :visible="deleteVisible"
      :right_title="true"
      @dialogClone="deleteTipDialogClone"
      @dialogSubmit="deleteDialogSubmitClone"
    >
      <template slot="content"> 您确定要删除？确定后不可撤销 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';
import tipDialog from '@/components/borderDialog/index3.vue';
import {
  myOrderList,
  cancelUserOrder,
  deleteOrder,
  getOrderTeam,
} from '@/api/confirmOrder.js';
import util from '@/utils/index';

export default {
  name: 'Home',
  components: {
    tipDialog,
  },
  data() {
    return {
      util,
      deleteVisible: false,
      deleteId: '',
      formInline: {
        order_no: '',
      },
      index: 2,
      statusDate: util.statusDate,
      status: undefined, // 状态
      page: 1,
      totalPage: 10,
      initDate: [],
      listLoading: false,
      colorList: {
        '待审核': '#4285F4',
        '已': '#4285F4',
      },
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.initDateFun();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    deleteTipDialogClone() {
      this.deleteVisible = false;
    },
    deleteDialogSubmitClone() {
      deleteOrder(this.deleteId).then((res) => {
        if (res.code == 200) {
          this.initDateFun();
          this.deleteVisible = false;
        }
      });
    },
    goChat(item) {
      getOrderTeam({
        orderId: item.id,
      }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', item.id);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    canDel(item) {
      return [4, 5, 12].includes(item.status);
    },
    delOrder(orderId) {
      // this.$confirm('您确定要删除？确定后不可撤销', '提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      this.deleteId = orderId;
      this.deleteVisible = true;

      // });
    },
    cancelOrder(orderId) {
      this.$confirm('您确定要取消支付？确定后不可撤销', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        cancelUserOrder(
          {},
          {
            orderId,
          }
        ).then((res) => {
          if (res.code == 200) {
            this.initDateFun();
          }
        });
      });
    },
    findProduct(item) {
      let findIt = item.orderItemList.find((ele) => {
        return ele.itemType === 0;
      });
      return findIt;
    },
    // 初始化数据
    initDateFun(orderSn) {
      let data = {
        orderStatus: this.status,
        pageNum: this.page,
        pageSize: 10,
      };
      if (orderSn) {
        data.orderSn = orderSn;
      }
      this.listLoading = true;
      myOrderList(data)
        .then((res) => {
          if (res.code == 200) {
            this.initDate = res.data.list || [];
            this.initDate.forEach((ele) => {
              ele.productItem = this.findProduct(ele);
            });
            this.totalPage = res.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     * 分页
     */
    pageFun(val) {
      this.page = `${val}`;
      this.initDateFun();
    },
    // 分类变化
    statusChoose(date) {
      this.status = date.id;
      this.page = 1;
      this.totalPage = 0;
      this.initDateFun();
    },
    // 搜索
    onSubmit() {
      this.page = 1;
      this.status = undefined;
      const orderNo = this.formInline.order_no.trim();
      this.initDateFun(orderNo);
    },
    // 搜索重置
    resetForm() {
      this.formInline.order_no = '';
      this.status = '';
      this.page = 1;
      this.initDateFun();
    },
    // 立即支付
    payNowOrder(id) {
      this.$router.push({
        path: '/payOrder?orderId=' + id,
      });
    },
    // 账号详情
    palyPage(date) {
      this.$router.push({
        path: '/account/orderDetail?orderId=' + date.id,
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>
