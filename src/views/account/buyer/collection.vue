<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    class="page_comStyle account_table_box"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="spaceStart colect_head">
      <!-- <img src="../../../../static/collect.png" class="colect_icon" /> -->
      <div>我的收藏</div>
    </div>

    <div v-if="initDate.length">
      <!-- table start -->
      <div class="orderTable_head spaceBetween">
        <div class="widthOne">商品信息</div>
        <!-- <div class="widthTwo">游戏名称</div> -->
        <div class="widthThree">金额</div>
        <div class="widthFour">状态</div>
        <!-- <div class="widthSeven spaceCenter">操作</div> -->
      </div>
      <div
        v-for="(item, index) in initDate"
        :key="index"
        class="orderTable_body"
      >
        <div class="spaceBetween tableBody_box">
          <div class="spaceStart widthOne cursor" @click="palyPage(item)">
            <div class="orderShop_pic">
              <el-image
                v-if="item.productPic"
                :src="item.productPic"
                style="width: 100%; height: 100%"
                fit="cover"
              ></el-image>
            </div>
            <div class="order_goods_box">
              <div>
                <div class="order_produceSn">【{{ item.productSn }}】</div>
                <div class="orderShop_tit text_linTwo">
                  {{ item.productSubTitle }}
                </div>
              </div>
              <div class="order_goods_name">
                {{ item.productCategoryName }}
              </div>
              <!-- <div class="orderShop_subT">
                <span
                  v-for="(itemS, indexS) in item.details"
                  :key="indexS"
                >
                  {{ itemS.lable }}:{{ itemS.value }}；
                </span>
              </div> -->
            </div>
          </div>
          <!-- <div class="widthTwo">{{ item.productCategoryName }}</div> -->
          <div class="widthThree order_price">
            <div
              v-if="item.productPrice !== item.newProductPrice"
              class="oldPirce"
            >
              ¥ {{ item.productPrice }}
            </div>
            <div>¥ {{ item.newProductPrice }}</div>
          </div>
          <div class="widthFour">
            <!-- <el-tag v-if="item.productStatus == 1" class="tab_seller">{{
              item.productStatusTxt
            }}</el-tag>
            <el-tag v-else type="info">{{ getProductStatus(item) }}</el-tag> -->
            <div class="goods_status">
              {{
                item.productStatus == 1
                  ? item.productStatusTxt
                  : getProductStatus(item)
              }}
            </div>
          </div>
        </div>
        <div
          class="spaceEnd"
          style="margin-right: 41.13px; margin-bottom: 20.56px"
        >
          <div class="plain cancel_favorites" @click="delectAccFun(item)">
            取消收藏
          </div>
          <div
            v-if="item.productStatus == 1"
            class="orderTable_btn solid accountOrderBtn"
            @click="palyPage(item)"
          >
            立即购买
          </div>
        </div>
      </div>
      <!-- tabler end -->
      <div class="tabler_footer_pagination">
        <el-pagination
          :total="totalPage"
          layout=" pager,jumper"
          class="search_page_pagination"
          @current-change="pageFun"
        >
        </el-pagination>
      </div>
    </div>
    <div v-else class="list_null_sorry">
      <img
        style="width: 54px; height: 56px"
        src="../../../../static/imgs/null.png"
        alt=""
      />
      <div style="margin-left: 15.85px">
        <img
          style="width: 63px; height: 36px"
          src="../../../../static/imgs/sorry_text.svg"
          alt=""
        />
        <div class="sorry_text">暂时无相关数据</div>
      </div>
    </div>
    <tipDialog
      :visible="cancelVisible"
      :right_title="true"
      @dialogClone="cancelTipDialogClone"
      @dialogSubmit="cancelDialogSubmitClone"
    >
      <template slot="content"> 您将取消此账号的收藏, 是否继续 </template>
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
import isLogin from '@/utils/isLogin';
import tipDialog from '@/components/borderDialog/index3.vue';
import {
  getProductCollectionList,
  productCollectionDetele,
} from '@/api/accountDetail.js';
// import { myCollectApi, delectCollectApi } from '@/api/index';

export default {
  name: 'Collect',
  components: {
    tipDialog,
  },
  data() {
    return {
      cancelVisible: false,
      productId: '',
      listLoading: false,
      page: 1,
      totalPage: 10,
      initDate: [],
    };
  },
  watch: {},
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.initDateFun();
  },
  methods: {
    getProductStatus(item) {
      if (item.productStatus == 1 || item.productStatus == -1) {
        return item.productStatusTxt;
      } else {
        return '已售';
      }
    },
    // 初始化
    initDateFun() {
      this.listLoading = true;
      getProductCollectionList({
        pageNum: this.page,
        pageSize: 10,
      })
        .then((res) => {
          if (res.code == 200) {
            this.initDate = res.data.list;
            this.totalPage = res.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页
    pageFun(val) {
      this.page = `${val}`;
      this.initDateFun();
    },
    // 取消收藏
    delectAccFun(date) {
      this.cancelVisible = true;
      this.productId = date.productId;
      // this.$confirm('您将取消此账号的收藏, 是否继续?', '提示', {
      //   closeOnClickModal: false,
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // }).then(() => {
      //   this.listLoading = true;
      //   // var json = {
      //   //   account_id: date.account.id,
      //   //   flag_id: date.game.id,
      //   // };
      // });
    },
    cancelTipDialogClone() {
      this.cancelVisible = false;
    },
    cancelDialogSubmitClone() {
      productCollectionDetele({
        productId: this.productId,
      }).then((response) => {
        if (response.code == 200) {
          let collects = localStorage.getItem('collects');
          if (collects) {
            collects = JSON.parse(collects);
          } else {
            collects = [];
          }
          collects = collects.filter((ele) => {
            return ele != this.productId;
          });
          localStorage.setItem('collects', JSON.stringify(collects));
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.cancelVisible = false;
          this.initDateFun();
        }
      });
    },
    // 账号详情
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style type="text/css" scoped>
.oldPirce {
  text-decoration: line-through;
  color: #ccc;
}
/* .newPrice {
  color: #fe5a1e;
} */
</style>
