<template>
  <div
    class="page_comStyle"
    style="padding: 29.13px 50.563px 52px 50.563px; border-radius: 20.56px"
  >
    <div class="spaceStart">
      <div class="colect_head_title">人脸认证</div>
    </div>

    <div v-show="showPageOne" class="individual_box">
      <div class="title">
        <div>人脸认证</div>
        <div v-if="faceState == 1" class="title_right">
          <!-- 已认证 -->
        </div>
      </div>
      <div style="padding-bottom: 119px" class="individual_form_box">
        <img
          class="individual_box_bg"
          style="top: 358.226px"
          src="../../../../static/imgs/login_main.png"
          alt=""
        />
        <div v-if="faceState == -2" class="realPerson_tip realPerson_tip_error">
          温馨提示：根据国家规定, 本平台禁止未成年人交易
        </div>
        <div v-if="faceState == -1" class="realPerson_tip realPerson_tip_error">
          人脸认证失败，请重新认证
        </div>
        <div v-if="faceState == 0" class="realPerson_tip realPerson_tip_review">
          认证中...
        </div>
        <!--  -->
        <div
          v-if="faceState == 1"
          class="realPerson_tip realPerson_tip_success"
          style="color: #67c23a; background: #f0f9eb"
        >
          认证已通过
        </div>
        <el-form
          v-loading.fullscreen.lock="loading"
          v-show="showPageOne"
          ref="loginForm"
          :model="formJson"
          :rules="rules"
          class="individual_form individual_form_identify"
          label-width="125px"
          auto-complete="on"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div class="userC_form" style="width: 45%">
            <el-form-item prop="userIdPic1" label="身份证正面">
              <div
                :style="{ 'box-shadow': formJson.userIdPic1 ? 'none' : '' }"
                class="userC_identify cursor"
              >
                <uploadSingle
                  :need-water="false"
                  class="uploadSingle"
                  name-key="userIdPic1"
                  @upSuccsessSingle="picUpLoadSuc"
                />
                <!-- https://pic4.zhimg.com/v2-267217db1a16cf81a50184ea7d520289_1440w.jpg -->
                <!-- :src="formJson.userIdPic1" -->
                <!-- v-if="formJson.userIdPic1" -->
                <el-image
                  v-if="formJson.userIdPic1"
                  :src="formJson.userIdPic1"
                  style="width: 100%; height: 100%; border-radius: 9.6px"
                  fit="cover"
                ></el-image>
                <el-image
                  v-else
                  src="../../../../static/idCard_01.png"
                  style="width: 271px; margin-top: 20px; margin-left: 38.44px"
                  fit="cover"
                ></el-image>
              </div>
            </el-form-item>
            <el-form-item prop="userIdPic2" label="身份证反面">
              <div
                :style="{ 'box-shadow': formJson.userIdPic2 ? 'none' : '' }"
                class="userC_identify cursor"
              >
                <uploadSingle
                  :need-water="false"
                  class="uploadSingle"
                  name-key="userIdPic2"
                  @upSuccsessSingle="picUpLoadSuc"
                />
                <el-image
                  v-if="formJson.userIdPic2"
                  :src="formJson.userIdPic2"
                  style="width: 100%; height: 100%; border-radius: 9.6px"
                  fit="cover"
                ></el-image>
                <el-image
                  v-else
                  src="../../../../static/idCard_02.png"
                  style="width: 271px; margin-top: 20px; margin-left: 38.44px"
                  fit="cover"
                ></el-image>
              </div>
            </el-form-item>
            <!-- <div class="userC_subBtn" @click="doTest">直接人脸测试</div> -->
            <!-- v-if="faceState != 1" -->
            <div class="spaceStart">
              <div
                :class="
                  formJson && formJson.userIdPic1 && formJson.userIdPic2
                    ? 'userC_subBtn_active'
                    : 'userC_subBtn_not'
                "
                class="spaceCenter userC_subBtn1"
                @click="submitForm('loginForm')"
              >
                下一步
              </div>
              <!-- userC_subBtn1 -->
            </div>
            <div
              style="
                font-size: 14px;
                margin-top: 20px;
                margin-left: 83px;
                width: 100%;
                font-family: PingFang SC;
                font-weight: 400;
                letter-spacing: 0.56px;
                color: rgba(0, 0, 0, 0.4);
                display: flex;
                align-items: center;
              "
            >
              <IconFont
                v-if="!checked"
                :size="17.14"
                style="margin-right: 7px; cursor: pointer; margin-top: -0px"
                icon="unchecked"
                @click="changChecked(true)"
              />
              <img
                v-if="checked"
                style="width: 17.14px; margin-right: 7px; cursor: pointer"
                src="../../../../static/imgs/confirmOrder_order_chebox_icon.svg"
                alt=""
                @click="changChecked(false)"
              />
              我已阅读并同意
              <span class="agreeText" @click="agreeMentFun"
                >《看看账号网用户协议》</span
              >
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <div v-show="!showPageOne" class="codeIden_wrap">
      <div
        style="
          color: #1b1b1b;
          text-align: center;
          font-family: YouSheBiaoTiHei;
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 85.714% */
          letter-spacing: 0.56px;
        "
      >
        人脸识别二维码
      </div>
      <div style="font-size: 15px; color: #666; padding: 20px 0">
        手机扫一扫
      </div>
      <div class="codeIden_pic_wrap">
        <div v-if="failed" class="failTip">二维码已失效请刷新重试</div>
        <canvas id="QRCode_header" style="width: 224px; height: 224px"></canvas>
      </div>
      <div class="spaceStart">
        <div class="userC_subBtn2 spaceCenter" @click="refreshCodePic">
          刷新二维码
        </div>
      </div>
    </div>

    <!-- 身份证识别结果 -->
    <identifyDialog
      :visible="dialogVisible"
      :dialog-width="'523px'"
      class="identifyDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="identify_right_title">认证信息</span>
      </template>
      <template slot="content">
        <div class="identify_content_box">
          <el-form
            ref="idDetail_form"
            :model="idDetail"
            :rules="identify_rules"
            class="identify__form"
            label-width="70px"
            style="width: 100%"
          >
            <div>
              <el-form-item prop="userPicIdName" label="姓名">
                <el-input
                  v-model="idDetail.userPicIdName"
                  style="width: 322px"
                  type="text"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item prop="userPicIdNumber" label="身份证号">
                <el-input
                  v-model="idDetail.userPicIdNumber"
                  style="width: 322px"
                  type="text"
                  placeholder="请输入"
                />
              </el-form-item>
            </div>
          </el-form>
          <div class="dialog_realPerson_tip_review spaceCenter">
            请检查以上信息是否正确，如若有误请修改
          </div>
          <div class="spaceCenter">
            <div
              :class="
                idDetail && idDetail.userPicIdName && idDetail.userPicIdNumber
                  ? 'plDt_btn_face_color2'
                  : 'plDt_btn_face_color1'
              "
              class="spaceCenter plDt_btn_face"
              @click="dialogFaceStart('idDetail_form')"
            >
              人脸认证
            </div>
          </div>
        </div>
      </template>
    </identifyDialog>
    <!-- <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      class="payCode"
      title="认证信息"
      width="30%"
      center
    >
      <div class="spaceStart cutDt_pushWrap_i">
        <div class="cutDt_pushWrap_left">姓名：</div>
        <input
          v-model="idDetail.userPicIdName"
          class="cutDt_push_ipt_i"
          placeholder=""
          name=""
        />
      </div>
      <div class="spaceStart cutDt_pushWrap_i">
        <div class="cutDt_pushWrap_left">身份证号：</div>
        <input
          v-model="idDetail.userPicIdNumber"
          class="cutDt_push_ipt_i"
          placeholder=""
          name=""
        />
      </div>
      <div class="dialog_realPerson_tip_review">
        请检查以上信息是否正确，如若有误请修改
      </div>
      <div class="spaceCenter">
        <div
          class="spaceCenter plDt_btn_i"
          style="border-radius: 4px; margin-bottom: 20px"
          @click="faceStart"
        >
          人脸认证
        </div>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import uploadSingle from '@/components/uploadSingle/index';
import identifyDialog from '@/components/borderDialog/index2.vue';
import {
  getCertDetail,
  certAdd,
  initFaceVerify,
  describeFaceVerify,
} from '@/api/safeCenter';
import isLogin from '@/utils/isLogin';

import QRCode from 'qrcode';

export default {
  name: 'Identify',
  components: { uploadSingle, identifyDialog },
  data() {
    return {
      checked: false,
      idDetail: {
        userPicIdName: '',
        userPicIdNumber: '',
      },
      failed: false,
      showPageOne: true, // 第一个页面展示
      formJson: {
        userIdPic1: '',
        userIdPic2: '',
      },
      index: 2,
      dialogVisible: false,
      userDte: {},
      rules: {
        userIdPic1: [
          { required: true, message: '请上传身份证正面照片', trigger: 'blur' },
        ],
        userIdPic2: [
          { required: true, message: '请上传身份证反面照片', trigger: 'blur' },
        ],
      },
      identify_rules: {
        userPicIdName: [
          { required: true, message: '请输入正确的姓名', trigger: 'blur' },
        ],
        userPicIdNumber: [
          { required: true, message: '请输入正确的身份证号', trigger: 'blur' },
        ],
      },
      loading: false,
      maxtimeTimer: null,
      faceState: -2,
    };
  },
  mounted() {
    if (isLogin()) {
      describeFaceVerify().then((res) => {
        // -1:认证未成功
        // 0：认证中
        // 1：认证成功
        // -2:未认证
        if (res.code === 200) {
          this.faceState = res.data;
        }
      });
      this.initUser();
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },

  beforeDestroy() {
    this.maxtimeTimer = clearInterval(this.maxtimeTimer);
  },
  methods: {
    agreeMentFun() {
      this.$router.push({
        path: '/helpCenter?id=65',
      });
    },
    //协议选择框
    changChecked(flag) {
      this.checked = flag;
      if (flag) {
        this.checkedFlag = false;
      }
    },
    dialogClone() {
      this.dialogVisible = false;
    },
    picUpLoadSuc(url, key) {
      this.$set(this.formJson, key, url);
      // this.formJson[key] = url;
    },
    /**
     * 查询状态
     */
    countDownUser() {
      this.maxtimeTimer = setInterval(() => {
        this.describeFaceVerify();
      }, 5000);
    },
    describeFaceVerify() {
      describeFaceVerify().then((res) => {
        // -1:认证未成功
        // 0：认证中
        // 1：认证成功
        if (res.code === 200) {
          if (res.data === 1) {
            location.reload();
          } else if (res.data === -1) {
            this.failed = false;
            this.faceState = -1;
            this.maxtimeTimer = clearInterval(this.maxtimeTimer);
          }
        }
      });
    },
    // 生成认证二维码
    creatQrCode(picUrl) {
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 224, //宽
        height: 224, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
    // 刷新认证二维码
    refreshCodePic() {
      this.failed = false;
      this.faceStart();
    },
    initUser() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          const data = res.data || {};
          if (data.defaultStatus != 2) {
            this.$message.error('请先完成实名认证！');
            this.$router.push('/account/approve');
          }
          // if (data.faceStatus == 2 || data.faceStatus == 1) {
          //   this.formJson.userIdPic1 = null;
          //   this.formJson.userIdPic2 = null;
          // }

          this.formJson = data;
          if (this.formJson.faceStatus != 2) {
            this.showPageOne = true;
            clearInterval(this.maxtimeTimer);
          }
        }
      });
    },
    dialogFaceStart(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.faceStart();
        } else {
          return false;
        }
      });
    },
    // 人脸认证
    faceStart() {
      this.loading = true;
      // MetaInfo从入口 ali取
      initFaceVerify({
        metaInfo: window.MetaInfo,
        idName: this.idDetail.userPicIdName,
        idNumber: this.idDetail.userPicIdNumber,
        returnUrl: 'https://m.kkzhw.com',
      }).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.dialogVisible = false;
          // 接下来直接跳转CertifyUrl即可开始刷脸认证
          this.showPageOne = false;
          this.creatQrCode(res.data.certifyUrl);
          // 循环查询状态
          this.countDownUser();
        }
      });
    },
    // 保存提交身份证
    submitForm(formName) {
      if (this.formJson.defaultStatus != 2) {
        this.$message.error('请先完成实名认证！');
        return;
      }
      if (!this.checked) {
        this.$message.error('请阅读并同意网用户协议');
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitAdd();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    getIdDetail() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          this.idDetail = res.data;
          this.dialogVisible = true;
        }
      });
    },
    submitAdd() {
      this.loading = true;
      certAdd({
        type: 2,
        userIdPic1: this.formJson.userIdPic1,
        userIdPic2: this.formJson.userIdPic2,
      }).then((response) => {
        this.loading = false;
        if (response.code == 200) {
          this.getIdDetail();
        }
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style rel="stylesheet/scss" lang="scss">
.agreeText {
  cursor: pointer;
  color: #ffb74a;
}
.realPerson_tip2 {
  color: rgb(103, 194, 58);
  background: rgb(240, 249, 235);
}
.failTip {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  font-size: 16px;
  width: 100%;
  height: 100%;
  color: #fcfcfc;
  text-align: center;
  font-weight: 700;
  line-height: 221px;
}
.uploadSingle {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: 100%;
  opacity: 0;
  .picUpload_wrap {
    width: 100%;
    height: 100%;
    .el-upload--picture-card {
      width: 100%;
      height: 100%;
    }
  }
}
.codeIden_wrap {
  text-align: center;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  padding-top: 50px;
}
.codeIden_pic_wrap {
  position: relative;
  width: 260px;
  height: 260px;
  border-radius: 24px;
  box-sizing: border-box;
  padding: 10px;
  border: 3px solid #ffb74a;
  margin: 0 auto;
}
.codeIden_pic_wrap img {
  display: block;
  width: 100%;
  height: 100%;
}
.realPerson_tip {
  width: 644px;
  height: 39px;
  margin: 0 auto;
  margin-bottom: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  letter-spacing: 0.2px;
  font-family: YouSheBiaoTiHei;
  margin-top: -20px;
}
.realPerson_tip_error {
  background: linear-gradient(190deg, #fff0f0 3.42%, #ffdcdc 65.82%);
  color: #ff594a;
}
.realPerson_tip_review {
  background: linear-gradient(190deg, #fff8f0 3.42%, #ffe7d0 65.82%);

  color: #ff7a00;
}
.dialog_realPerson_tip_review {
  background: linear-gradient(190deg, #fff8f0 3.42%, #ffe7d0 65.82%);
  width: 100%;
  height: 30px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  border-radius: 8px;
  margin-top: 20px;
}
.realPerson_tip_success {
  color: #1c9211;
  background: linear-gradient(190deg, #f5fff0 3.42%, #aee9bd 65.82%);
}
.userC_form {
  width: 60%;
  margin: 0;
  padding-top: 0px;
}
.userC_identify {
  width: 348px;
  height: 202px;
  margin-top: 12px;
  border-radius: 9.6px;
  background: #fff;
  box-shadow: 1.3px 2.6px 3.9px 0px rgba(0, 0, 0, 0.05);
  position: relative;
  margin-left: 28px;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.userC_phone {
  font-size: 16px;
  color: #222222;
  font-weight: 400;
}
.userC_subBtn2 {
  // width: 146.547px;
  padding: 0px 48px;
  height: 46px;
  border-radius: 60px;
  font-size: 16px;
  color: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  // margin: 40px auto;
  // margin-left: 135.8345px;
  margin-top: 34.28px;
  margin: 0 auto;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
  margin-top: 20px;
}
.userC_subBtn1 {
  // width: 113.981px;
  padding: 0px 48px;
  height: 46px;
  border-radius: 60px;
  font-size: 16px;
  color: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;

  // margin: 40px auto;
  margin-left: 171px;
  margin-top: 10px;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
}
.userC_subBtn1:hover {
  background: var(--btn-background-gradient);
}
.userC_subBtn_active {
  background: var(--btn-background-gradient);
}
.userC_subBtn_not {
  background: rgba(0, 0, 0, 0.4);
}
.userC_subBtn1.disabled {
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
}
.cutDt_pushWrap_i {
  height: 40px;
  margin-bottom: 20px;
}
.cutDt_pushWrap_left {
  width: 80px;
}
.cutDt_push_ipt_i {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
  border: none;
  text-indent: 14px;
  font-weight: 500;
  background: transparent;
  width: 70%;
  outline: none;
  border: 1px solid #ff6716;
  border-radius: 4px;
}
.cutDt_push_ipt:focus {
  border: none;
}
.cutDt_push_btn {
  width: 80px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #ff6716;
  color: #fff;
  cursor: pointer;
}
.plDt_btn_i {
  margin-left: 20px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  margin-top: 20px;
}
.plDt_btn i {
  margin-right: 4px;
}
.individual_form_identify .el-form-item {
  margin-bottom: 33px;
}
.identify_right_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px; /* 100% */
}
.identify_content_box {
  padding: 40px 24px 41px 26px;
}
.identify__form .el-form-item__label {
  color: #1b1b1b;
  text-align: right;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.64px;
  padding: 0px;
  &::before {
    display: none;
  }
}
.identify__form .el-form-item__error {
  color: #ff7a00;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.28px;
  text-indent: 15px;
  margin-left: 20px;
}
.identify__form .el-form-item {
  margin-bottom: 30px;
}
.identify__form .el-input {
  margin-left: 20px;
  border-radius: 50px;
  height: 42.85px;
  background: var(--btn-background-gradient);
  border: none;
  position: relative;
  z-index: 1;
  .el-input__inner {
    background: transparent;
    border: none;
    position: relative;
    z-index: 3;
    height: 42.85px;
    color: #1b1b1b;

    /* 新/文本/小号描述文字 */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 171.429% */
    letter-spacing: 0.56px;
  }
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50px;
    background: #fff;
    z-index: 2;
    margin: 2px;
    position: absolute;
  }
}
.identifyDialog .dialogBk {
  width: 252.864px !important;
  height: 266px !important;
  top: 93px !important;
  right: 30.14px !important;
}
.identifyDialog .el-dialog__body {
  padding: 25px 32px 0px 29px !important;
}
.identifyDialog .doalog2_left_logo {
  width: 229px;
  height: 50px;
  flex-shrink: 0;
}
.plDt_btn_face {
  display: inline-flex;
  height: 46px;
  padding: 0px 48px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 60px;
  // border: 1px solid #969696;

  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  margin-top: 20px;
}
.plDt_btn_face_color1 {
  background: #969696;
}
.plDt_btn_face_color2 {
  background: var(--btn-background-gradient);
}
</style>
