<template>
  <div
    class="page_comStyle"
    style="padding: 29.13px 50.563px 181.684px 50.563px; border-radius: 20.56px"
  >
    <div class="spaceStart">
      <!-- <img src="../../../../static/user.png" class="colect_icon" /> -->
      <div class="colect_head_title">基本信息</div>
    </div>
    <div class="individual_box">
      <div class="title">个人信息</div>
      <div class="individual_form_box">
        <img
          class="individual_box_bg"
          src="../../../../static/imgs/login_main.png"
          alt=""
        />
        <!-- ../../../../static/imgs/account_bg_logo.png -->
        <el-form
          v-loading.fullscreen.lock="loading"
          ref="loginForm"
          :model="userInfo"
          :rules="rules"
          label-width="125px"
          auto-complete="on"
          element-loading-text="拼命加载中"
          class="individual_form baseNews_form_box"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div class="userC_form">
            <!--<el-form-item prop="" label="用户头像:">
          <div class="userC_avter cursor">
            <uploadSingle
              :need-water="false"
              class="uploadSingle"
              name-key="cover"
              @upSuccsessSingle="picUpLoadSuc"
            />
            <el-image
              v-if="userInfo.icon"
              :src="userInfo.icon"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
            <el-image
              v-else
              src="../../../../static/user_default.png"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
        </el-form-item>-->
            <el-form-item prop="username" label="昵称">
              <el-input
                :disabled="true"
                v-model="userInfo.nickname"
                type="text"
                placeholder="请输入用户昵称"
              />
            </el-form-item>
            <el-form-item prop="" label="手机号">
              <el-input :disabled="true" v-model="userInfo.phone" type="text" />
            </el-form-item>
            <el-form-item label="微信号">
              <el-input
                v-model="userInfo.vxname"
                type="text"
                placeholder="请输入用户微信号"
              />
            </el-form-item>
            <el-form-item label="微信绑定:">
              <span
                v-if="userInfo.isWxPush"
                class="wx-btn-text"
                style="color: red"
                @click="handelUnbindWx"
                >已绑定</span
              >
              <span v-else class="wx-btn-text" @click="handelBindWx"
                >去绑定</span
              >
            </el-form-item>
            <!-- <el-form-item prop="" label="邀请码:">
          <el-input
            :disabled="userInfo.invite_code ? true : false"
            v-model="invite_code"
            type="text"
            placeholder="请输入邀请码"
          />
        </el-form-item> -->

            <div class="">
              <div
                class="userC_subBtn spaceCenter"
                @click="submitForm('loginForm')"
              >
                保存修改
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <wechatDialog :visible="wechatDialogVisible" @dialogClone="dialogClone">
      <template slot="content">
        <div class="wechatDialog_box">
          <div class="title">扫码关注公众号</div>
          <!-- <img class="qRcode" src="" alt="" /> -->
          <div style="position: relative">
            <canvas
              id="wx_qRcode"
              style="width: 128px; height: 128px; border: 1px solid #ffb74a"
            >
            </canvas>
            <img
              style="
                width: 34.26px;
                height: 34.26px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              "
              src="../../../../static/imgs/qrcode_wechat.png"
              alt=""
            />
          </div>
          <div class="divider"></div>
          <div class="footer_title">开启提醒享受以下服务</div>
          <div class="footer_type">
            <div>审核通知</div>
            <div class="divide_right"></div>
            <div>上架通知</div>
            <div class="divide_right"></div>
            <div>交易通知</div>
          </div>
        </div>
      </template>
    </wechatDialog>
    <tipDialog
      :visible="UnbindWxVisible"
      :right_title="true"
      class="refoundBackDialog"
      @dialogClone="refoundBackDialogClone"
      @dialogSubmit="refoundBackSubmitClone"
    >
      <template slot="content">
        确认解绑微信公众号？ 如解绑您将无法收到订单及咨询消息</template
      >
      <template slot="button"> 确定 </template>
    </tipDialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import tipDialog from '@/components/borderDialog/index3.vue';
import isLogin from '@/utils/isLogin';
import uploadSingle from '@/components/uploadSingle/index';
import { updateInfo, getWxQrcode, unbindWeixin } from '@/api/index.js';
import wechatDialog from '@/components/borderDialog/index2.vue';
import QRCode from 'qrcode';
export default {
  name: 'Home',
  components: { uploadSingle, wechatDialog, tipDialog },
  data() {
    return {
      UnbindWxVisible: false,
      invite_code: '', // 邀请码
      wechatDialogVisible: false,
      index: 2,
      rules: {
        username: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
        ],
      },
      loading: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  mounted() {
    if (isLogin()) {
      //   this.initOssConfig();
      this.$store.dispatch('getUserInfoStore',{isForcible:true});
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
    // if (this.userInfo.invite_code) {
    //   this.invite_code = this.userInfo.invite_code;
    // }
  },
  methods: {
    refoundBackDialogClone() {
      this.UnbindWxVisible = false;
    },
    creatQrCode() {
      var that = this;
      const picUrl = this.picUrl;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 128, //宽
        height: 128, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };

      let msg = document.getElementById('wx_qRcode');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          that.$message.error('二维码加载失败');
        } else {
          const canvas = msg.getContext('2d');
          const logoWidth = 34.56;
          const logoHeight = 34.56;
          const logo = new Image();
          logo.src = '../../../../static/imgs/qrcode_wechat.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    dialogClone() {
      this.wechatDialogVisible = false;
    },
    handelBindWx() {
      //关注公众号
      getWxQrcode().then((res) => {
        this.wechatDialogVisible = true;
        this.$nextTick(() => {
          this.picUrl = res.data.url;
          this.creatQrCode();
        });
      });
    },
    handelUnbindWx() {
      this.UnbindWxVisible = true;
    },
    refoundBackSubmitClone() {
      unbindWeixin().then((res) => {
        if (res.code === 200) {
          const userInfo = this.$store.getters.userInfo;
          this.$store.dispatch(
            'setUserInfo',
            Object.assign(userInfo, {
              isWxPush: 0,
            })
          );
          this.UnbindWxVisible = false;
          this.$message({
            type: 'success',
            message: '解绑成功!',
          });
        }
      });
    },
    // 保存编辑
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 提交修改
          this.submitAdd();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    submitAdd() {
      this.loading = true;
      updateInfo({
        nickName: this.userInfo.nickname,
        vxName: this.userInfo.vxname,
        pic: this.userInfo.icon,
      }).then((res) => {
        this.loading = false;
        this.$store.dispatch('getUserInfoStore');
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        }
      });
      // updateInforApi({
      //   cover: this.userInfo.cover,
      //   username: this.userInfo.username,
      //   vxname: this.userInfo.vxname,
      //   invite_code: this.invite_code,
      // }).then((response) => {
      //   this.loading = false;
      //   if (response.code == 200) {
      //     this.$store.dispatch('getUserInfoStore');
      //     this.$message({
      //       type: 'success',
      //       message: '操作成功!',
      //     });
      //   }
      // });
    },
    picUpLoadSuc(url) {
      const userInfo = this.$store.getters.userInfo;
      this.$store.dispatch(
        'setUserInfo',
        Object.assign(userInfo, {
          icon: url,
        })
      );
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style scoped lang="scss">
.uploadSingle {
  width: 72px;
  height: 72px;
  overflow: hidden;
  position: absolute;
  .picUpload_wrap {
    width: 72px;
    height: 72px;
    .el-loading-spinner {
      display: none;
    }
  }
}
.userC_form {
  width: 60%;
  margin: 0;
  padding-top: 0px;
}
.userC_avter {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  margin-top: -16px;
  position: relative;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.userC_phone {
  font-size: 16px;
  color: #222222;
  font-weight: 400;
}
.userC_subBtn {
  width: 146.547px;
  height: 42.85px;
  border-radius: 51.42px;
  font-size: 16px;
  color: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  // margin: 40px auto;
  margin-left: 158px;
  margin-top: 34.28px;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
}
.userC_subBtn.disabled {
  background: #fff6eb;
  color: #222222;
}

.baseNews_form_box /deep/ .el-form-item__label {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: end;
}
.wx-btn-text {
  color: #ff7000;
  cursor: pointer;
  font-size: 16px;
  display: block;
  font-family: PingFang Sc;
  line-height: 52px;
  margin-left: 42px;

  &:hover {
    text-decoration: underline;
  }
}
.wechatDialog_box {
  text-align: center;
  margin-top: 35.137px;
  padding-bottom: 22.282px;
  // padding-bottom: 24px;
  // border-bottom: 1px solid #ffeed5;
  .title {
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
    font-size: 26px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 10.28px;
  }
  .qRcode {
    width: 109px;
    height: 109px;
    border: 1px solid #ffb74a;
  }
  .divider {
    width: 385.65px;
    height: 1px;
    background: #ffeed5;
    margin: 0 auto;
    margin-top: 24px;
  }
  .footer_title {
    margin-top: 14.5px;
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 190% */
  }
  .footer_type {
    height: 18px;
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    //
    .divide_right {
      display: block;
      height: 11px;
      margin: 0 10px;
      width: 1px;
      background: rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
