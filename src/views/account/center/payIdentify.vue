<template>
  <div
    class="page_comStyle"
    style="padding: 40px 45px 44px 40px; border-radius: 24px"
  >
    <div class="spaceStart">
      <div class="colect_head_title">包赔认证</div>
    </div>

    <!-- v-if="postForm.baopeiStatus == 2" -->
    <!-- <div class="realPerson_tip realPerson_tip2">温馨提示：资料审核已通过</div> -->
    <div class="individual_box">
      <div class="title">
        <div>包赔认证</div>
        <div v-if="postForm.baopeiStatus == 2" class="title_right">已认证</div>
      </div>
      <div style="padding-left: 74px" class="individual_form_box">
        <!--  -->
        <div
          v-if="postForm.baopeiStatus == 0"
          class="realPerson_tip realPerson_tip_review"
        >
          温馨提示：资料审核中
        </div>
        <!--   -->
        <div
          v-if="postForm.baopeiStatus == 1"
          class="realPerson_tip realPerson_tip_error"
        >
          温馨提示：资料审核不通过；<span v-if="postForm.region">{{
            postForm.region
          }}</span>
        </div>
        <img
          class="individual_box_bg"
          style="top: 501.207px"
          src="../../../../static/imgs/login_main.png"
          alt=""
        />
        <el-form
          v-loading.fullscreen.lock="loading"
          ref="postForm"
          :model="postForm"
          :rules="rules"
          label-width="125px"
          class="individual_form payIdentifyForm"
          auto-complete="on"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div class="userC_form">
            <el-form-item prop="username" label="本人电话">
              <el-input v-model="userInfo.phone" type="text" disabled />
            </el-form-item>
            <!-- <el-form-item prop="authCode" label="手机验证码:">
          <el-input
            v-model="postForm.authCode"
            type="tel"
            placeholder="请输入手机验证码"
            style="width: 70%"
          />
          <div class="code_wrap posionRight" @click="sendCode">
            {{ codeMsg }}
          </div>
        </el-form-item> -->
            <el-form-item prop="name" label="紧急联系人">
              <el-input
                v-model="postForm.name"
                type="text"
                placeholder="请输入紧急联系人"
              />
            </el-form-item>
            <el-form-item prop="phoneNumber" label="联系人电话">
              <el-input
                v-model="postForm.phoneNumber"
                type="text"
                placeholder="请输入紧急联系人电话"
              />
              <div class="rctip_box" style="margin-left: 40px; margin-top: 2px">
                <span style="color: rgb(84, 141, 212)">
                  1.请您如实填写以便审核快速、顺利完成。<br />
                  2.不可填写您本人的号码作为紧急联系人电话。<br />
                  3.紧急联系人信息必须真实有效，该紧急联系电话号码的实名需要和紧急联系人姓名一致。<br />
                  4.此处信息仅用于账号交易审核，看看账号网将严格保护您的隐私信息。
                </span>
              </div>
            </el-form-item>
            <!-- <el-form-item>
              <div class="rctip_box" style="margin-left: 40px">
                <span style="color: rgb(84, 141, 212)">
                  1.请您如实填写以便审核快速、顺利完成。<br />
                  2.不可填写您本人的号码作为紧急联系人电话。<br />
                  3.紧急联系人信息必须真实有效，该紧急联系电话号码的实名需要和紧急联系人姓名一致。<br />
                  4.此处信息仅用于账号交易审核，看看账号网将严格保护您的隐私信息。
                </span>
              </div>
            </el-form-item> -->
            <el-form-item label="网购或外卖订单">
              <uploadSingle
                :url-pic="postForm.userOrderPic"
                :need-water="false"
                style="margin-left: 40px"
                class="userOrderPic"
                name-key="userOrderPic"
                @upSuccsessSingle="picUpLoadSuc"
              />
            </el-form-item>
            <el-form-item>
              <div
                class="rctip_box"
                style="
                  margin-top: -68px;
                  margin-left: 40px;
                  padding: 20px 27px 26px 27px;
                "
              >
                <div>
                  <el-popover
                    popper-class="payldentify_popover"
                    placement="right"
                    width="400"
                    trigger="hover"
                  >
                    <img
                      style="width: 423px; height: 600px; margin-left: -35px"
                      src="../../../../static/imgs/waimai.png"
                    />
                    <!-- src="https://images2.kkzhw.com/mall/images/20240618/waimai.webp" -->
                    <el-button slot="reference" class="payldentify_popover_btn">
                      查看实例
                      <img
                        style="margin-top: -3px"
                        src="../../../../static/imgs/payIdentify_reference_icon.svg"
                        alt=""
                      />
                      <!-- <i
                        class="el-icon-question"
                        style="font-size: 18px"
                      ></i> -->
                    </el-button>
                  </el-popover>
                </div>
                <span style="color: rgb(84, 141, 212)">
                  1.至少提供一张3个月内已交易成功的订单截图，推荐提交外卖订单。<br />
                </span>
                <span
                  >2.截图须完整包含姓名、电话、地址、时间。<br />3.订单地址不能是网吧或酒店等公共地址，不能打码。<br
                /></span>
                <span>
                  系统提示：为了您能顺利通过审核，请按照上述要求进行提交</span
                >
              </div>
            </el-form-item>

            <el-form-item prop="" label="备注">
              <el-input
                v-model="postForm.detailAddress"
                class="payIdentify_textarea"
                style="margin-left: 40px; width: 358px; min-height: 114px"
                type="textarea"
                placeholder="请输入备注信息"
              />
              <!-- <el-input
                v-model="postForm.detailAddress"
                type="text"
                placeholder="请输入备注信息"
              /> -->
            </el-form-item>
            <!-- -->
            <div
              v-if="postForm.baopeiStatus !== 2 && postForm.faceStatus == 2"
              class="spaceStart"
            >
              <div
                class="userC_subBtn1 spaceCenter"
                @click="submitForm('postForm')"
              >
                认证
              </div>
              <!-- -->
              <div
                v-if="postForm.baopeiStatus == 2"
                class="userC_subBtn1 disabled spaceCenter"
              >
                已认证
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import '@/utils/yidun-captcha';
import { mapState } from 'vuex';
import uploadSingle from '@/components/uploadSingle/index';
import { sendPhoneCode } from '@/api/login.js';
import { getCertDetail, certAdd } from '@/api/safeCenter';
import isLogin from '@/utils/isLogin';

export default {
  name: 'PayIdentify',
  components: {
    uploadSingle,
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (/[^\u4e00-\u9fa5]+/.test(value)) {
        callback(new Error('请输入中文'));
      } else {
        callback();
      }
    };
    return {
      postForm: {
        // authCode: '',
        name: '',
        phoneNumber: '',
        userOrderPic: '',
        detailAddress: '',
      },
      rules: {
        code: [
          { required: true, message: '请输入手机验证码', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '请输入紧急联系人', trigger: 'blur' },
          { validator: validateName, trigger: 'blur' },
        ],
        phoneNumber: [
          { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
        ],
      },
      loading: false,
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  mounted() {
    if (isLogin()) {
      this.initUser();
      this.initCaptcha();
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },
  methods: {
    initCaptcha() {
      window.initNECaptchaWithFallback(
        {
          captchaId: '3455bd8a6484410ea146980a113839aa',
          width: '320px',
          mode: 'popup',
          apiVersion: 2,
          onVerify: (err, data) => {
            if (err) return;
            this.doSendSmsCode(data);
          },
        },
        (instance) => {
          this.captchaIns = instance;
        }
      );
    },
    picUpLoadSuc(url, key) {
      this.postForm[key] = url;
    },
    initUser() {
      this.$store.dispatch('getUserInfoStore');
      getCertDetail().then((res) => {
        if (res.code == 200) {
          if (res.data.faceStatus != 2) {
            this.$message.error('请先完成人脸认证！');
            this.$router.push('/account/identify');
          }
          // 保证userOrderPic响应式
          this.postForm = Object.assign(this.postForm, res.data || {});

          if (res.data.baopeiUpdateTime) {
            // 使用 moment.js 解析上次包赔时间
            const baopeiUpdateTime = moment(res.data.baopeiUpdateTime);

            // 获取当前时间
            const currentTime = moment();

            // 计算两个日期之间的差异（天数）
            const diffDays = currentTime.diff(baopeiUpdateTime, 'days');

            // 判断是否超过60天
            if (diffDays > 60) {
              this.postForm.baopeiStatus = null;
            }
          }
        }
      });
    },
    // 保存提交身份证
    submitForm(formName) {
      if (this.postForm.phoneNumber == this.userInfo.phone) {
        this.$message.error('不可填写您本人的号码作为紧急联系人电话');
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (!this.postForm.userOrderPic) {
          this.$message.error('请上传图片');
          return;
        }
        if (valid) {
          this.submitAdd();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    submitAdd() {
      this.loading = true;
      const data = {
        // authCode: this.postForm.authCode,
        detailAddress: this.postForm.detailAddress,
        name: this.postForm.name,
        phoneNumber: this.postForm.phoneNumber,
        type: 3,
        userOrderPic: this.postForm.userOrderPic,
      };
      certAdd(data).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.$message.success('认证已提交请耐心等待审核!');
          this.initUser();
        }
      });
    },
    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.userInfo.phone,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    /**
     * 获取验证码
     */
    sendCode() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      this.captchaIns && this.captchaIns.verify();
    },
    /**
     * 倒计时
     */
    countDown() {
      this.code -= 1;
      if (this.code == 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var _this = this;
      var interval = setTimeout(function () {
        _this.countDown();
      }, 1000);
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style lang="scss" scoped>
.userOrderPic /deep/ .picUpload_wrap .el-upload--picture-card {
  width: 146px;
  height: 146px;
  border-radius: 12px;
}
.userOrderPic /deep/ .picUpload_wrap {
  width: 146px;
  height: 146px;
  border-radius: 12px;
}
.payldentify_popover_btn {
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border: none;
  padding: 0px;
  margin-bottom: 6.856px;
}
.payIdentify_textarea /deep/ .el-textarea__inner {
  height: 114px;
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  resize: none;
}
.realPerson_tip {
  width: 644px;
  height: 39px;
  margin: 0 auto;
  margin-bottom: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  letter-spacing: 0.2px;
  font-family: YouSheBiaoTiHei;
  margin-top: -20px;
}
.realPerson_tip2 {
  color: rgb(103, 194, 58);
  background: rgb(240, 249, 235);
}
.realPerson_tip_error {
  background: linear-gradient(190deg, #fff0f0 3.42%, #ffdcdc 65.82%);
  color: #ff594a;
}
.realPerson_tip_review {
  background: linear-gradient(190deg, #fff8f0 3.42%, #ffe7d0 65.82%);

  color: #ff7a00;
}
.code_wrap {
  float: right;
  height: 40px;
  width: 120px;
  background: #fff;
  color: #ff6716;
  border: none;
  cursor: pointer;
  text-align: center;
  line-height: 40px;
  border-radius: 20px;
  font-weight: 500;
}
.rctip_box {
  width: 322px;
  background-color: #ecf3fe;
  border-radius: 20px;
  padding: 12px 27px;

  color: #4285f4;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.48px;
}
.tip_boxBei {
  color: #999;
  font-size: 12px;
  padding-bottom: 15px;
}
.userC_form {
  width: 70%;
  margin: 0;
  padding-top: 0px;
}
.userC_identify {
  width: 300px;
  height: 160px;
  border-radius: 50%;
  position: relative;
}
.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
.userC_phone {
  font-size: 16px;
  color: #222222;
  font-weight: 400;
}
.userC_subBtn1 {
  // width: 113.981px;
  padding: 0 48px;
  height: 50px;
  border-radius: 60px;
  font-size: 16px;
  color: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  // margin: 40px auto;
  margin-left: 179.5px;
  margin-top: 18px;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
}
.userC_subBtn1.disabled {
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
}
.cutDt_pushWrap_i {
  height: 40px;
  margin-bottom: 20px;
}
.cutDt_pushWrap_left {
  width: 80px;
}
.cutDt_push_ipt_i {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
  border: none;
  text-indent: 14px;
  font-weight: 500;
  background: transparent;
  width: 70%;
  outline: none;
  border: 1px solid #ff6716;
  border-radius: 4px;
}
.cutDt_push_ipt:focus {
  border: none;
}
.cutDt_push_btn {
  width: 80px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #ff6716;
  color: #fff;
  cursor: pointer;
}
.plDt_btn_i {
  margin-left: 20px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  margin-top: 20px;
}
.plDt_btn i {
  margin-right: 4px;
}
.payIdentifyForm /deep/ .is-error .el-input__inner {
  border-color: #ff720c !important;
}
.payIdentifyForm /deep/ .el-form-item__label {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
