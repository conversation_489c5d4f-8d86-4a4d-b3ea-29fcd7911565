<template>
  <div
    class="page_comStyle"
    style="padding: 29.13px 50.563px 181.684px 50.563px; border-radius: 20.56px"
  >
    <div class="spaceStart">
      <!-- <img src="../../../../static/user.png" class="colect_icon" /> -->
      <div class="colect_head_title">实名认证</div>
    </div>
    <div class="individual_box">
      <div class="title">
        <div>实名认证</div>
        <div v-if="postForm.defaultStatus == 2" class="title_right">已认证</div>
      </div>
      <div class="individual_form_box">
        <img
          class="individual_box_bg"
          src="../../../../static/imgs/login_main.png"
          alt=""
        />

        <el-form
          v-loading.fullscreen.lock="listLoading"
          ref="postForm"
          :model="postForm"
          :rules="rules"
          class="individual_form approve_form_box"
          label-width="125px"
          auto-complete="on"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div v-if="postForm.defaultStatus == 1" class="iden_wrap">
            待审核；请耐心等待~
          </div>

          <div class="userC_form">
            <el-form-item prop="userIdName" label="真实姓名">
              <el-input
                :disabled="verifyed"
                v-model="postForm.userIdName"
                type="text"
                placeholder="请输入真实姓名"
              />
            </el-form-item>
            <el-form-item prop="userIdNumber" label="身份证号">
              <el-input
                :disabled="verifyed"
                v-model="postForm.userIdNumber"
                type="text"
                placeholder="请输入身份证号"
              />
            </el-form-item>

            <div class="">
              <div
                v-if="postForm.defaultStatus !== 2"
                class="userC_subBtn spaceCenter"
                @click="submitIden('postForm')"
              >
                实名认证
              </div>
            </div>

            <div class="">
              <div
                v-if="postForm.defaultStatus == 2"
                class="userC_subBtn disabled spaceCenter"
              >
                已实名
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getCertDetail, certAdd } from '@/api/safeCenter';
import isLogin from '@/utils/isLogin';

export default {
  name: 'Home',
  components: {},
  data() {
    return {
      index: 2,
      postForm: {
        userIdName: '',
        userIdNumber: '',
        type: 1,
      },
      verifyed: false,
      rules: {
        userIdName: [
          { required: true, message: '请输入您的正式姓名', trigger: 'blur' },
        ],
        userIdNumber: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
        ],
      },
      listLoading: false,
    };
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.getCertDetail();
    // if (this.postForm.identify_state == 2) {
    //   this.postForm.realname = this.postForm.realname;
    //   this.postForm.idcard = this.postForm.idcard;
    // }
  },
  methods: {
    getCertDetail() {
      getCertDetail().then((res) => {
        if (res.code == 200) {
          this.postForm = Object.assign({}, this.postForm, res.data || {});
          if (this.postForm.defaultStatus === 2) {
            this.verifyed = true;
          }
        }
      });
    },
    submitIden(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitAdd();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 提交
    submitAdd() {
      this.listLoading = true;
      certAdd(this.postForm).then((res) => {
        this.listLoading = false;
        if (res.code == 200) {
          this.$message.success('认证成功！');
          this.getCertDetail();
        }
      });
    },
  },
};
</script>

<style>
@import url(../seller/orderTable.css);
</style>

<style rel="stylesheet/scss" lang="scss">
.userC_form {
  width: 60%;
  margin: 0;
  padding-top: 0px;
}
.userC_avter {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  margin-top: -16px;
}
.userC_phone {
  font-size: 16px;
  color: #222222;
  font-weight: 400;
}
.userC_subBtn {
  width: 146.547px;
  height: 42.85px;
  border-radius: 51.42px;
  font-size: 16px;
  color: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  // margin: 40px auto;
  margin-left: 158.5px;
  margin-top: 34.28px;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
}
.userC_subBtn.disabled {
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
}
.iden_wrap {
  display: block;
  background: #fff4ee;
  padding: 14px 20px;
  border-radius: 6px;
  font-size: 14px;
  color: #222;
  margin-bottom: 20px;
}
.approve_form_box .el-form-item__label {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
