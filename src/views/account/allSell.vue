<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <div class="gameListBk">
      <headerKk :active-index="index" />
      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px 17px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>我要卖</el-breadcrumb-item>
        </el-breadcrumb>

        <gameList :need-cash="true" @playPage="playPage">
          <template slot="top">
            <img class="guanggao" src="../../../static/n.png" />
          </template>
        </gameList>
      </div>
    </div>

    <!-- 游戏客服 -->
    <el-dialog
      :visible.sync="dialogVisibleInden"
      :before-close="handleClose"
      width="400px"
      center
      title="联系客服"
    >
      <div class="iden_text_sub" style="text-align: center">
        使用微信二维码扫一扫
      </div>
      <div class="spaceCenter">
        <canvas
          id="QRCode_header_kfwx"
          style="width: 150px; height: 150px"
        ></canvas>
      </div>
      <div class="iden_text_sub" style="text-align: center">
        扫码添加客服进行咨询，微信号： {{ gameKefu['微信号'] }}
      </div>
      <!-- <div class="iden_text_sub" style="text-align: center">
        {{ gameKefu.label }}
      </div> -->
    </el-dialog>

    <!-- 认证 -->
    <el-dialog
      :visible.sync="dialogVisibleIdentify"
      width="30%"
      center
      title="实名认证"
    >
      <div class="iden_text">
        尊敬的用户您好，根据相关法律法规要求，需完成实名认证方可进行发布。
      </div>
      <div class="spaceCenter">
        <div
          class="spaceCenter plDt_btn"
          style="margin-bottom: 20px; width: 90%"
          @click="indentifyGo"
        >
          去实名
        </div>
      </div>
    </el-dialog>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import gameItem from '@/components/gameItem/index';
import zimuList from '@/components/zimuList/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import { getCertDetail, certAdd } from '@/api/safeCenter';
import gameList from '@/components/gameList/index';
import { getMemberHisKFList, m2kfTalk, getKfList } from '@/api/kf.js';
import QRCode from 'qrcode';
export default {
  components: {
    gameItem,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    zimuList,
    gameList,
  },
  data() {
    return {
      dialogVisibleInden: false,
      dialogVisibleIdentify: false,
      index: 2,
      gameList: [],
      type: '',
      gameKefu: {}, // 弹窗客户
    };
  },
  // computed: {
  //   ...mapState({
  //     userInfo: (state) => state.userInfo,
  //   }),
  // },
  mounted() {
    if (isLogin()) {
      this.$store.dispatch('getUserInfoStore');
      getCertDetail().then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data || {};
        }
      });
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
    // this.initGame();
  },
  methods: {
    // 实名认证
    indentifyGo() {
      this.$router.push({
        path: '/account/approve',
      });
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    handleClose(done) {
      this.gameKefu = {};
      done();
    },
    createWx() {
      let qrcodeImg = `${this.gameKefu.wxurl}`;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 150, //宽
        height: 150, //高
        text: qrcodeImg, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_kfwx');
      QRCode.toCanvas(msg, qrcodeImg, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
    // 跳转-发布账号-根据游戏不同跳转不同
    playPage(date) {
      if (this.userInfo.defaultStatus != 2) {
        this.dialogVisibleIdentify = true;
        return;
      }
      // 0 客服发布模式
      // if (date.navStatus === 0) {
      //   getMemberHisKFList({
      //     cateId: date.id,
      //   }).then((res) => {
      //     if (res.code == 200) {
      //       if (res.data) {
      //         let findKf = res.data;
      //         const { nim, store } = window.__xkit_store__;
      //         const imcode = findKf;
      //         const sessionId = `p2p-${imcode}`;
      //         m2kfTalk({
      //           cateId: date.id,
      //           kfIM: imcode,
      //         });
      //         if (store.sessionStore.sessions.get(sessionId)) {
      //           store.uiStore.selectSession(sessionId);
      //         } else {
      //           store.sessionStore.insertSessionActive('p2p', imcode);
      //         }

      //         this.$store.dispatch('ToggleIM', true);
      //       } else {
      //         this.$store.dispatch('ToggleIM', true);
      //       }
      //     }
      //   });
      //   return;
      // }
      if (date.navStatus === 0) {
        getKfList({
          game: date.name,
          type: '咨询客服',
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data || [];
            if (list.length) {
              this.gameKefu = _.sample(list);
              this.dialogVisibleInden = true;
              this.$nextTick(() => {
                this.createWx();
              });
            }
          }
        });
      } else {
        const {custom} = date
        const customObj =  JSON.parse(custom||'{}')

        let URL = '/pushAccount'

        // 道具产品
        if(customObj.goodsType === 'vgoods'){
          URL = '/pushAccount3'
        }

        this.$router.push({
          path:
            URL + '?productCategoryId=' +
            date.id +
            '&attriCateId=' +
            date.attriCateId +
            '&type=push',
        });
      }
    },
  },
};
</script>

<style scoped>
.gameType_wrap {
  font-size: 16px;
  color: #909090;
}
.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}
.hotGame_wrap {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 24px;
}
.gameAll_wrap {
  flex-wrap: wrap;
}
.guanggao {
  cursor: pointer;
  width: 100%;
  margin-bottom: 14px;
}
.iden_text {
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  margin-bottom: 30px;
}
.iden_text_sub {
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  margin: 10px 0;
}
.plDt_btn {
  margin-left: 20px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.plDt_btn i {
  margin-right: 4px;
}
.wx_kefuCode {
  display: block;
  margin: 0 auto;
  width: 300px;
}
</style>
