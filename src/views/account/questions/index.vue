<template>
  <div style="padding: 40px 39px 56px 40px" class="page_comStyle">
    <span class="questions_head_title">完善常见问题</span>
    <div class="orderTable_body1">
      <div class="spaceBetween row-line">
        <div class="spaceStart">
          <div>商品编号：</div>
          <div style="color: #9a9a9a">
            {{ productDetail.productSn
            }}<IconFont
              :size="14"
              icon="copy"
              class="accountList_copy"
              style="cursor: pointer; margin-left: 3px"
              @click="copyVal(productDetail.productSn)"
            />
          </div>
        </div>
        <div class="spaceStart">
          <div>发布时间：</div>
          <!-- util.formatTime(orderDetail.createTime, 'YYYY-MM-DD HH:mm:ss') -->
          <div>
            {{
              util.formatTime(
                productDetail.pushStartTime,
                'YYYY-MM-DD HH:mm:ss'
              )
            }}
          </div>
        </div>
      </div>
      <div class="spaceBetween tableBody_box">
        <div
          class="spaceStartNotAi widthOne cursor"
          @click="palyPage(productDetail)"
        >
          <div class="orderShop_pic">
            <el-image
              :src="productDetail.pic"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
          <div>
            <div class="questions_title" style="text-indent: -10px">
              【{{ productDetail.productSn }}】
              <!-- <span
                >【{{ accountType }}】</span
              > -->
            </div>
            <div class="orderShop_tit text_linTwo">
              {{ productDetail.name }}
            </div>
            <div class="orderShop_subT">
              {{ productDetail.productCategoryName }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="questions">
      <el-form ref="form" :model="postForm" :rules="rules">
        <div
          v-for="(item, index) in productAttrs"
          :key="index"
          class="question_box"
        >
          <el-form-item
            v-if="item.type == 1"
            :prop="`${item.id}`"
            :label="item.name"
            class="is-required"
          >
            <el-input :placeholder="item.name" v-model="item.value"></el-input>
          </el-form-item>
          <el-form-item
            v-else-if="item.type == 3"
            :prop="`${item.id}`"
            :label="item.name"
            class="is-required"
          >
            <el-radio-group v-model="item.value">
              <el-radio
                v-for="ele in item.inputList"
                :key="ele"
                :label="ele"
                class="el-radio-custom"
              ></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-else-if="item.type == 4"
            :prop="`${item.id}`"
            :label="item.name"
            class="is-required"
          >
            <el-select v-model="item.value">
              <el-option
                v-for="ele in item.inputList"
                :key="ele"
                :label="ele"
                :value="ele"
                class="el-radio-custom"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.type == 2"
            :prop="`${item.id}`"
            :label="item.name"
            class="is-required"
          >
            <div class="tedian_item">
              <div class="right_tedian">
                <div class="searchIpt_done" @click="showChilde(item)">
                  <div
                    v-if="item.choosedList.length > 0"
                    class="spaceStart"
                    style="flex-wrap: wrap"
                  >
                    <div
                      v-for="(v, i) in item.choosedList"
                      :key="i"
                      class="tedian_Choose_small spaceCenter active"
                      @click.stop="chooseChoosed(v, item)"
                    >
                      {{ fakeName(v.name) }}
                      <img
                        v-if="iconFilter(v)"
                        :src="iconFilter(v)"
                        class="icon_tedian"
                      />
                    </div>
                  </div>
                  <span v-else style="color: #606266">&nbsp;请选择</span>
                  <i
                    v-if="item.showChild"
                    class="el-icon-arrow-up up_arror_tedian"
                  ></i>
                  <i v-else class="el-icon-arrow-down up_arror_tedian"></i>
                </div>
                <div v-if="item.showChild">
                  <el-input
                    v-model="item.iptSearchVal"
                    :id="'listNode' + item.id"
                    type="text"
                    placeholder="请输入搜索内容或者新游戏物品"
                    style="margin-top: 10px"
                    @input="tedianChange(item)"
                  >
                  </el-input>
                  <div
                    class="tedian_container spaceStart"
                    style="border-top: none"
                  >
                    <!-- 搜索的显示列表 -->
                    <div
                      v-if="item.searchList && item.searchList.length > 0"
                      class="searchList_wrap"
                    >
                      <div class="spaceStart" style="flex-wrap: wrap">
                        <div
                          v-for="(v, i) in item.searchList"
                          :key="i"
                          :class="v.checked ? 'active' : ''"
                          class="tedian_itemChoose spaceCenter"
                          @click="chooseTeDianItem(v, item)"
                        >
                          {{ fakeName(v.name) }}
                          <img
                            v-if="iconFilter(v)"
                            :src="iconFilter(v)"
                            class="icon_tedian"
                          />
                        </div>
                      </div>
                    </div>
                    <div
                      v-for="(v, i) in item.childList"
                      :key="i"
                      :class="v.checked ? 'active' : ''"
                      class="tedian_itemChoose spaceCenter"
                      @click="chooseTeDianItem(v, item)"
                    >
                      {{ fakeName(v.name) }}
                      <img
                        v-if="iconFilter(v)"
                        :src="iconFilter(v)"
                        class="icon_tedian"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="btn_box">
      <el-button class="btn_box_submit" type="primary" @click="onSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import _ from 'lodash';
import { getUpdateInfo, productUpdate2 } from '@/api/submitAccount';
import isLogin from '@/utils/isLogin';
import { getDetail } from '@/api/playDetail';
import util from '@/utils/index';
export default {
  components: {},
  data() {
    return {
      util,
      productAttributeList: [],
      productAttributeValueList: [],
      productAttrs: [],
      productDetail: {},
      productId: this.$route.query.productId,
      postForm: {},
      rules: {},
      accountType: '',
    };
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    this.getDetail();
    this.getQuestion();
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    chooseChoosed(date, arrlist) {
      arrlist.choosedList.forEach((v, i) => {
        if (v.name == date.name) {
          arrlist.choosedList.splice(i, 1);
        }
      });
      arrlist.childList.forEach((v, i) => {
        if (v.name == date.name) {
          v.checked = false;
        }
      });
    },
    chooseTeDianItem(date, arrlist) {
      if (!date.checked) {
        date.checked = true;
        arrlist.choosedList.push(date);
      } else {
        date.checked = false;
        arrlist.choosedList.forEach((v, i) => {
          if (v.name == date.name) {
            arrlist.choosedList.splice(i, 1);
          }
        });
      }
    },
    tedianChange(e) {
      if (e.iptSearchVal) {
        let reg = new RegExp(e.iptSearchVal);
        e.searchList = e.childList.filter((item) => reg.test(item.name));
      } else {
        e.searchList = [];
      }
    },
    iconFilter(v) {
      if (v.name.indexOf('[绝]') !== -1) {
        return '../../static/push/jue.png';
      } else if (v.name.indexOf('[钱]') !== -1) {
        return '../../static/push/price.png';
      } else if (v.name.indexOf('[核]') !== -1) {
        return '../../static/push/he.png';
      }
    },
    showChilde(date) {
      this.$set(date, 'showChild', !date.showChild);
      if (date.showChild) {
        this.$nextTick(() => {
          var id = 'listNode' + date.id;
          var userName = document.getElementById(id);
          userName.focus();
        });
      }
      if (date.showChild == false) {
        date.searchList = [];
        date.iptSearchVal = '';
      }
    },
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    getQuestion() {
      getUpdateInfo(this.productId).then((res) => {
        const { productAttributeList, productAttributeValueList, product } =
          res.data;
        this.productAttributeList = _.cloneDeep(productAttributeList);
        this.productAttributeValueList = _.cloneDeep(productAttributeValueList);
        this.postForm = product;
        this.formatProductAttrs();
      });
    },

    formatProductAttrs() {
      const filterAttr = this.productAttributeList.filter(
        (ele) => ele.type == 4
      );
      let tempList = [];
      filterAttr.forEach((ele) => {
        ele.inputList = ele.inputList ? ele.inputList.split(',') : [];
        const findIt = this.productAttributeValueList.find(
          (item) => item.productAttributeId === ele.id
        );
        let type = 1;
        let opetion = {
          ...ele,
        };
        if (
          ele.inputType !== 0 &&
          (ele.selectType === 1 || ele.selectType === 3)
        ) {
          if (ele.inputList.length === 2 && ele.selectType !== 3) {
            // 用redio
            type = 3;
            opetion.inputList = ele.inputList;
          } else {
            // 用下拉
            type = 4;
            opetion.professionDate = ele.inputList;
          }
        } else if (ele.inputType !== 0 && ele.selectType === 2) {
          type = 2;
          opetion.field_type = 0;
          opetion.searchList = [];
          opetion.choosedList = [];
          opetion.childList = ele.inputList.map((item) => {
            return {
              checked: false,
              icon: 2,
              name: item,
              value: item,
            };
          });
        }
        opetion.type = type;
        if (type == 1) {
          opetion.value = findIt ? findIt.value : '';
        } else if (type == 2 && findIt && findIt.value) {
          opetion.choosedList = findIt.value.split(',').map((ele) => {
            return { name: ele };
          });
          opetion.choosedList.forEach((c) => {
            const { name } = c;
            opetion.childList.forEach((c) => {
              if (c.name == name) {
                c.checked = true;
              }
            });
          });
        } else {
          opetion.value = findIt ? findIt.value : '';
        }
        opetion.sourceType = ele.type;
        opetion.id = ele.id;
        if (ele.name == '最低价格多少' && !opetion.value) {
          opetion.value = this.postForm.originalPrice || '';
        }
        tempList.push(opetion);
      });
      this.productAttrs = tempList;
      this.productAttrs.forEach((opetion) => {
        this.rules[`${opetion.id}`] = [
          {
            validator: (rule, value, callback) => {
              const { field } = rule;
              const { realv, findIt } = this.getValueByField(field);
              if (findIt.inputType == 0 && realv.length > 9) {
                // 如果是输入框，最多 9 位
                callback(new Error(`最多输入9位数字`));
              }
              if (findIt.type == 2) {
                let hasValue = findIt.choosedList && findIt.choosedList.length;
                if (!hasValue) {
                  callback(new Error(`请输入${findIt.name}`));
                } else {
                  callback();
                }
              } else {
                if (realv || realv === 0) {
                  callback(); // 没有错误
                } else {
                  callback(new Error(`请输入${findIt.name}`));
                }
              }
            },
            trigger: opetion.inputType === 0 ? 'blur' : 'change',
          },
        ];
      });
    },

    getValueByField(field) {
      const tempList = this.productAttrs;
      const findIt = tempList.find((ele) => {
        return ele.id == field;
      });
      // type 1,输入框，2 多选（不做必填），3 单选，4 下拉单选
      if (findIt) {
        return {
          realv: findIt.value,
          findIt,
        };
      }
    },
    postFormToData() {
      // let data = {
      //   gameAccountQufu: this.postForm.gameAccountQufu,
      //   pic: this.postForm.pic,
      //   albumPics: this.postForm.albumPics,
      //   description: this.postForm.description,
      //   gameCareinfoPhone: this.postForm.gameCareinfoPhone,
      //   gameCareinfoVx: this.postForm.gameCareinfoVx,
      //   gameCareinfoTime: this.postForm.gameCareinfoTime,
      //   price: this.postForm.price,
      //   originalPrice: this.postForm.originalPrice,
      //   productCategoryId: this.postForm.productCategoryId,
      //   productCategoryName: this.postForm.productCategoryName,
      //   productAttributeCategoryId: this.postForm.productAttributeCategoryId,
      //   gameGoodsSaletype: this.postForm.gameGoodsSaletype,
      //   gameGoodsYijia: this.postForm.gameGoodsYijia,
      //   gameGoodsYishou: this.postForm.gameGoodsYishou,
      // };

      let data = {};
      data.productAttributeValueList = this.getProductAttributeValueList();
      return data;
    },
    getProductAttributeValueList() {
      let tempList = [];

      this.productAttrs.forEach((ele) => {
        let findIt = this.productAttributeValueList.find(
          (item) => item.productAttributeId == ele.id
        );
        let value = ele.value;
        if (ele.type == 2) {
          if (ele.choosedList && ele.choosedList.length) {
            value = ele.choosedList.map((e) => e.name).join(',');
          } else {
            value = '';
          }
        }
        let obj = {
          productAttributeId: ele.id,
          value,
          attriName: ele.name,
          type: ele.sourceType,
        };
        if (findIt) {
          // 如果有旧值
          obj.id = findIt.id;
        }
        tempList.push(obj);
      });

      tempList.forEach((ele) => {
        const oldAttr = this.productAttributeList.find(
          (item) => item.id == ele.productAttributeId
        );
        ele.type = oldAttr.type;
        ele.attriName = oldAttr.name;
        ele.searchType = oldAttr.searchType;
      });

      // 界面没有编辑的，加上 type 再发回去
      // if (this.productAttributeValueList) {
      //   this.productAttributeValueList.forEach((ele) => {
      //     const findIt = tempList.find(
      //       (item) => item.productAttributeId == ele.productAttributeId,
      //     );
      //     const oldAttr = this.productAttributeList.find(
      //       (item) => item.id == ele.productAttributeId,
      //     );
      //     if (!findIt) {
      //       ele.searchType = oldAttr.searchType;
      //       tempList.push(ele);
      //     }
      //   });
      // }
      return tempList;
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let data = this.postFormToData();
          productUpdate2(this.productId, data).then((response) => {
            if (response.code == 200) {
              this.$message.success('提交成功');
              setTimeout(() => {
                this.$router.push({
                  path: '/account/accountList',
                });
              }, 500);
            }
          });
        }
      });
    },
    palyPage(date) {
      this.$router.push({
        path: '/gd/' + date.productSn,
      });
    },
    getDetail() {
      getDetail(this.productId).then((res) => {
        this.productDetail = res.data.product;

        // const findtss3 = res.data.productAttributeList.find((item) => {
        //   return item.name === '账号类型';
        // });
        // console.log(JSON.stringify(findtss3), 222222);
      });
    },
  },
};
</script>
<style>
@import url(../seller/push.css);
</style>

<style scoped lang="scss">
/deep/ .el-radio-custom {
  margin-bottom: 15px;
}
/deep/ .pushTedian {
  .el-form-item__label {
    display: none;
  }
  .teding_name {
    display: none;
  }
}

.btn_box {
  width: 100%;
  display: flex;
  justify-content: center;
}
.page_comStyle {
  background: #fff;
  border-radius: 24px;
}
.questions {
  .question_box {
    /deep/ .el-form-item__label {
      float: none;
      color: #1b1b1b;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
      letter-spacing: 0.64px;
    }
    /deep/ .el-radio__label {
      color: rgba(0, 0, 0, 0.4);
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.32px;
      padding-left: 8px;
    }
    /deep/ .el-form-item__label:before {
      background: var(--btn-background-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    /deep/ .el-input__inner {
      height: 52px;
      border-radius: 50px;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    /deep/ .el-form-item__error {
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      color: #ff7a00;
    }
    background: #fff;
    border-radius: 5px;
    // padding: 10px 0px;
    padding-bottom: 30px;
    /deep/ .el-form-item {
      margin: 0;
      // display: flex;
      // align-items: center;
    }
    .question_tit {
      padding-bottom: 10px;
    }
  }
}
.row-line {
  height: 42px;
  background: #fbf9f7;
  padding: 0px 24px;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  letter-spacing: 0.56px;
}

.baopei_box {
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
  height: 160px;
  padding-left: 16px;
}
.midline {
  border-top: 1px dashed #ccc;
  margin-top: 14px;
}
.order-info {
  padding-left: 16px;
  width: 410px;
  .column {
    height: 30px;
    font-size: 14px;
    margin-top: 14px;
    line-height: 30px;
    color: #565656;
    .price {
      color: #f7423f;
    }
  }
}
.title-box {
  display: inline-block;
  font-size: 17px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  position: relative;
}
.compensate_wrap {
  width: 340px;
  margin-right: 10px;
  box-sizing: border-box;
  padding: 15px;
  min-height: 106px;
  border: 1px solid #eeeeee;
  border-radius: 6px;
  font-size: 14px;
  color: #909090;
  transition: all 0.3s;
  .compensate_tit {
    font-size: 16px;
    color: #222222;
    padding-bottom: 18px;
  }
  .payPrice {
    font-size: 20px;
    font-weight: 600;
    color: #f7423f;
  }
}
.compensate_wrap.active {
  background: #fff8ef;
  border: 1px solid #ffdcba;
}
.compensate_wrap.active2 {
  background: #eff7ff;
  border: 1px solid #0082ff;
}

.orderTable_body1 {
  border-radius: 2px;
  // padding: 20px 15px;
  background: #fff;
  margin-bottom: 30px;
  margin-top: 31px;
}
.tableBody_box {
  border: 1px solid #e9e9e9;
  box-sizing: border-box;
  padding: 26px 0px 25px 26px;
  font-size: 14px;
  font-family: San Francisco Display;
  color: #222222;
}
.orderShop_pic {
  flex-shrink: 0;
  width: 110px;
  height: 110px;
  border-radius: 16px;
  overflow: hidden;
  margin-right: 12px;
}

.widthOne {
  flex-shrink: 0;
  width: 350px;
}
.widthTwo {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthThree {
  flex-shrink: 0;
  width: 110px;
  text-align: center;
}
.widthFour {
  flex-shrink: 0;
  width: 100px;
  text-align: center;
}
.widthFive {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}
.widthSix {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
}
.widthSeven {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
}
.light {
  color: #fc6116;
}
.orderShop_tit {
  line-height: 20px;
  width: 291px;
  margin-top: 3px;
  height: 48px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  letter-spacing: 0.56px;
}
.orderShop_subT {
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.56px;
  margin-top: 8px;
}
.questions_head_title {
  margin-bottom: 31px;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 38px;
}
.questions_title {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
}
.btn_box_submit {
  margin-top: 98px;
  height: 46px;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  border-radius: 60px;
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient);
  padding: 0px 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
