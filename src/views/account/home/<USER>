<template>
  <div class="dark_container" style="border-radius: 24px">
    <div
      class="page_comStyle approve_cent_box"
      style="padding: 0; padding-bottom: 0px"
    >
      <div class="cent_con spaceBetweenNoAi">
        <div class="spaceStartNotAi">
          <div class="cen_pic">
            <!-- <el-image
              v-if="userInfo.icon"
              :src="userInfo.icon"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image> -->
            <!-- v-else -->
            <el-image
              src="../../../../static/user_default.png"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
          <div>
            <div class="cen_name">{{ userInfo.nickname }}</div>
            <div class="spaceStart cen_phone">
              绑定手机：{{ phoneUnic() }}&nbsp;&nbsp;
              <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="pwdType ? 'eye' : 'eye-open'" />
              </span>
            </div>
            <div class="spaceStart cen_subtit">
              <!-- <img class="cen_type_pic" src="../../../../static/index.png" /> -->
              <div v-if="certDetail.defaultStatus == 2">您已完成实名认证</div>

              <router-link
                v-else-if="certDetail.defaultStatus != 1"
                to="/account/approve"
                >您还未完成实名认证 <span>立即认证</span></router-link
              >
            </div>
          </div>
        </div>
        <div class="cursor" style="text-align: right">
          <!-- <div class="spaceStart">
            绑定手机：{{ phoneUnic() }}&nbsp;&nbsp;
            <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="pwdType ? 'eye' : 'eye-open'" />
            </span>
          </div>
          <br />

          <div @click="deletAccount">账号注销</div> -->
        </div>
      </div>
      <!-- <div class="mainTit">交易详情</div> -->
      <!-- <div class="spaceAround cen_det borderBottom">
        <div class="cen_det_item spaceStart">
          <img class="cen_det_pic" src="../../../../static/center/one.png" />
          <div>
            <div class="cen_det_tit one">
              {{ pageDate.totalAmount || 0 }}
            </div>
            <div>总交易金额</div>
          </div>
        </div>
        <div class="cen_det_item spaceStart">
          <img class="cen_det_pic" src="../../../../static/center/two.png" />
          <div>
            <div class="cen_det_tit two">
              {{ pageDate.lastDealAmount || 0 }}
            </div>
            <div>最近一次交易</div>
          </div>
        </div>
        <div class="cen_det_item spaceStart">
          <img class="cen_det_pic" src="../../../../static/center/three.png" />
          <div>
            <div class="cen_det_tit three">
              {{ pageDate.totalOrderCount || 0 }}
            </div>
            <div>总交易订单</div>
          </div>
        </div>
      </div> -->
      <!-- 关注公众号二维码弹框 -->
      <div
        v-if="userInfo.isWxPush !== 1"
        style="
          padding: 40px 0px 0px 0px;
          width: 815px;
          margin: 0 auto;
          position: relative;
          border-top: 1px solid #ffeed5;
        "
      >
        <img
          style="width: 100%; height: 114px"
          src="../../../../static/imgs/account_home_member_footer.png"
          alt=""
        />
        <div class="home_menmber_btn" @click="goWechat"></div>
      </div>
      <div class="home_footer_box">
        <div class="mainTit">我是买家</div>
        <div class="spaceStart">
          <div class="border_box">
            <div>我购买的</div>
            <div class="spaceBetween border_box_foot">
              <div>
                {{ pageDate.memberStatisticsInfo.buyerOrderCount || 0 }}
              </div>
              <router-link class="border_box_btn" to="/account/buyerOrder"
                >查看</router-link
              >
            </div>
          </div>
          <!-- <div class="border_box">
            <div>我的收藏</div>
            <div class="spaceBetween border_box_foot">
              <div>
                {{ pageDate.collectionCount || 0 }}
              </div>
              <router-link class="border_box_btn" to="/account/collection"
                >查看</router-link
              >
            </div>
          </div> -->
          <div class="border_box">
            <div>发起的议价</div>
            <div class="spaceBetween border_box_foot">
              <div>{{ pageDate.memberStatisticsInfo.buyerNegoCount || 0 }}</div>
              <router-link class="border_box_btn" to="/account/myBargain"
                >查看</router-link
              >
            </div>
          </div>
        </div>

        <div
          style="
            margin-top: 40px;
            padding-top: 40px;
            border-top: 1px solid #ffeed5;
          "
          class="mainTit"
        >
          我是卖家
        </div>
        <div class="spaceStart">
          <!-- <div class="border_box">
            <div>总订单数/个</div>
            <div class="spaceBetween border_box_foot">
              <div>
                {{ pageDate.memberStatisticsInfo.sellerOrderCount || 0 }}
              </div>
              <router-link class="border_box_btn" to="/account/sellOrderList"
                >查看</router-link
              >
            </div>
          </div> -->
          <!-- <div class="border_box">
          <div>成交金额（元）</div>
          <div class="spaceBetween border_box_foot">
            <div>
              {{ pageDate.memberStatisticsInfo.sellerDetailAmount || 0 }}
            </div>
            <router-link class="border_box_btn" to="/account/sellOrderList"
              >查看</router-link
            >
          </div>
        </div> -->
          <div class="border_box">
            <div>我发布的</div>
            <div class="spaceBetween border_box_foot">
              <div>
                {{ pageDate.memberStatisticsInfo.sellerGoodsCount || 0 }}
              </div>
              <router-link class="border_box_btn" to="/account/accountList"
                >查看</router-link
              >
            </div>
          </div>
          <div class="border_box">
            <div>收到的议价</div>
            <div class="spaceBetween border_box_foot">
              <div>
                {{ pageDate.memberStatisticsInfo.sellerNegoCount || 0 }}
              </div>
              <router-link class="border_box_btn" to="/account/supplyBargain"
                >查看</router-link
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 注销账户 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      class="payCode"
      title="注销账户"
      width="30%"
      center
    >
      <div class="cut_cnver">注意事项</div>
      <div class="cut_conHtml">
        1、账户注销之后所有有关的用户数据将一并删除，不可恢复 <br />
        2、因删除后造成的数据丢失产生的一切问题均由用户承担 <br />
      </div>

      <div class="spaceCenter">
        <div
          class="spaceCenter plDt_btn"
          style="border-radius: 4px; margin-bottom: 20px"
          @click="deletFun"
        >
          确定删除
        </div>
      </div>
      <div class="spaceCenter agreeRegin">
        <el-checkbox v-model="checked" style="margin-right: 6px"></el-checkbox>
        <div>
          我已阅读并同意
          <router-link class="colorPrimay" to="/helpCenter?id=99"
            >《服务协议》</router-link
          >
        </div>
      </div>
    </el-dialog>
    <wechatDialog :visible="wechatDialogVisible" @dialogClone="dialogClone">
      <template slot="content">
        <div class="wechatDialog_box">
          <div class="title">扫码关注公众号</div>
          <!-- <img class="qRcode" src="" alt="" /> -->
          <div style="position: relative">
            <canvas
              id="wx_qRcode"
              style="width: 128px; height: 128px; border: 1px solid #ffb74a"
            >
            </canvas>
            <img
              style="
                width: 34.26px;
                height: 34.26px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              "
              src="../../../../static/imgs/qrcode_wechat.png"
              alt=""
            />
          </div>
          <div class="divider"></div>
          <div class="footer_title">开启提醒享受以下服务</div>
          <div class="footer_type">
            <div>审核通知</div>
            <div class="divide_right"></div>
            <div>上架通知</div>
            <div class="divide_right"></div>
            <div>交易通知</div>
          </div>
        </div>
      </template>
    </wechatDialog>
  </div>
</template>

<script>
import { getCertDetail } from '@/api/safeCenter';
import { memberStatics } from '@/api/index';
import { deletAccountApi } from '@/api/login';
import { getWxQrcode } from '@/api';
import QRCode from 'qrcode';
import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import wechatDialog from '@/components/borderDialog/index2.vue';

export default {
  name: 'Home',
  components: {
    wechatDialog,
  },
  data() {
    return {
      certDetail: {},
      wechatDialogVisible: false,
      dialogVisible: false,
      checked: false,
      index: 2,
      pageDate: {
        memberStatisticsInfo: {},
        sale: {
          total_price: 0,
          recent_total_price: 0,
          order_num: 0,
        },
        buyer: {
          dicker_num: 0,
          order_num: 0,
          total_price: 0,
        },
        seller: {
          account_num: 0,
          order_num: 0,
          total_price: 0,
        },
      },
      pwdType: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  mounted() {
    if (isLogin()) {
      this.$store.dispatch('getUserInfoStore',{isForcible:true});
      this.initDate();
    } else {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    }
  },
  methods: {
    creatQrCode() {
      var that = this;
      const picUrl = this.picUrl;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 128, //宽
        height: 128, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };

      let msg = document.getElementById('wx_qRcode');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          that.$message.error('二维码加载失败');
        } else {
          const canvas = msg.getContext('2d');
          const logoWidth = 34.56;
          const logoHeight = 34.56;
          const logo = new Image();
          logo.src = '../../../../static/imgs/qrcode_wechat.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    dialogClone() {
      this.wechatDialogVisible = false;
    },
    //关注公众号
    goWechat() {
      getWxQrcode().then((res) => {
        this.wechatDialogVisible = true;
        this.$nextTick(() => {
          this.picUrl = res.data.url;
          this.creatQrCode();
        });
      });
    },
    showPwd() {
      this.pwdType = !this.pwdType;
    },
    phoneUnic() {
      var phone = this.userInfo.phone;
      if (phone && !this.pwdType) {
        phone = phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      }
      return phone;
    },
    // 初始化统计数据
    initDate() {
      memberStatics().then((response) => {
        if (response.code == 200) {
          this.pageDate = response.data;
        }
      });
      getCertDetail().then((res) => {
        if (res.code === 200) {
          this.certDetail = res.data;
        }
      });
    },
    handleChange(val) {
      console.log(val);
    },
    // 账号注销
    deletAccount() {
      this.dialogVisible = true;
    },
    deletFun() {
      if (!this.checked) {
        this.$message.error('请阅读并同意服务协议');
        return;
      }
      deletAccountApi().then((response) => {
        this.dialogVisible = false;
        if (response.code == 200) {
          localStorage.removeItem('token');
          localStorage.removeItem('yximtoken');
          this.$message.success('账号注销成功！');
          location.reload();
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.approve_cent_box {
  border-radius: 24px !important;
  background: -moz-linear-gradient(0deg, #fff 63.81%, #ffe1c3 150.4%);
  background: linear-gradient(0deg, #fff 63.81%, #ffe1c3 150.4%);
  background: -webkit-gradient(
    linear-gradient(0deg, #fff 63.81%, #ffe1c3 150.4%)
  );
}
.cut_cnver {
  font-size: 16px;
  color: #222222;
  padding: 20px 0;
  text-align: center;
}
.cut_conHtml {
  background: #fff4ee;
  border: 1px solid #ff6716;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 15px;
  font-size: 14px;
  color: #222222;
  line-height: 32px;
  margin-bottom: 30px;
}
.plDt_btn {
  margin-left: 20px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.plDt_btn i {
  margin-right: 4px;
}
.cent_con {
  width: 100%;
  height: 180px;
  background: url(../../../../static/centerBg.png) no-repeat center top;
  background-size: 100% 100%;
  // border-radius-top: 24px;
  // border-radius: 24px 24px 0px 0px;
  // margin-bottom: 20px;
  box-sizing: border-box;
  padding: 42px 40px 0px 40px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
.cen_pic {
  width: 77px;
  height: 77px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 13px;
}
.cen_name {
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-top: 5px;
}
.cen_phone {
  margin-top: 5px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0.64px;
}
.cen_subtit {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.56px;
  span {
    margin-left: 11.141px;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
}
.cen_type_pic {
  width: 20px;
  margin-right: 6px;
}
.cen_det {
  box-sizing: border-box;
  padding: 40px 20px 40px;
}
.cen_det_item {
  font-size: 14px;
  color: #909090;
}
.cen_det_pic {
  width: 50px;
  margin-right: 10px;
}
.cen_det_tit {
  padding-bottom: 6px;
  font-size: 20px;
  font-weight: 500;
}
.cen_det_tit.one {
  color: #f7423f;
}
.cen_det_tit.two {
  color: #4777e8;
}
.cen_det_tit.three {
  color: #ffa500;
}
// .border_box {
//   width: 248px;
//   height: 110.5px;
//   background: #fbf9f7;

//   border-radius: 24px;
//   box-sizing: border-box;
//   padding: 17.14px 18px 17.14px 26.567px;
//   font-size: 16px;
//   color: rgba(0, 0, 0, 0.4);
//   /* 小字段落 */
//   font-family: 'PingFang SC';
//   margin-right: 38px;
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
//   letter-spacing: 0.64px;
//   line-height: 30px;
// }
.border_box {
  width: 290px;
  height: 129px;
  background: #fbf9f7;

  border-radius: 24px;
  box-sizing: border-box;
  padding: 20px 19px 18px 31px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  /* 小字段落 */
  font-family: 'PingFang SC';
  margin-right: 38px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  letter-spacing: 0.64px;
  line-height: 30px;
}
.border_box:nth-child(3n + 3) {
  margin-right: 0;
}
.border_box_foot {
  font-size: 26px;
  font-family: San Francisco Display;
  font-weight: 500;
  color: #000;

  /* Title/Large */
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  // line-height: 27.424px; /* 100% */
  // padding-top: 20px;
}
.border_box_btn {
  display: block;
  height: 36px;
  padding: 0px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #fff;
  font-family: 'PingFang SC';
  letter-spacing: 0.28px;
  font-weight: 500;
  border-radius: 24px;
  background: var(--btn-background-gradient);
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
}
.home_footer_box {
  padding: 51px 40px 40px 40px;
}
.mainTit {
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 27px;
  letter-spacing: 0.8px;
  line-height: normal;
}
.home_menmber_btn {
  position: absolute;
  top: 83px;
  right: 29.46px;
  width: 126px;
  height: 38px;
  cursor: pointer;
  border-radius: 24px;
}
.wechatDialog_box {
  text-align: center;
  margin-top: 35.137px;
  padding-bottom: 22.282px;
  // padding-bottom: 24px;
  // border-bottom: 1px solid #ffeed5;
  .title {
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
    font-size: 26px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 10.28px;
  }
  .qRcode {
    width: 109px;
    height: 109px;
    border: 1px solid #ffb74a;
  }
  .divider {
    width: 385.65px;
    height: 1px;
    background: #ffeed5;
    margin: 0 auto;
    margin-top: 24px;
  }
  .footer_title {
    margin-top: 14.5px;
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 190% */
  }
  .footer_type {
    height: 18px;
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    //
    .divide_right {
      display: block;
      height: 11px;
      margin: 0 10px;
      width: 1px;
      background: rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
