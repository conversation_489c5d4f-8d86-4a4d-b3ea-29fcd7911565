<template>
  <div class="dark_container">
    <div class="recyleList_box_bk">
      <headerKk :active-index="index" />

      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/recyle' }"
            >号商回收</el-breadcrumb-item
          >
          <el-breadcrumb-item>号商列表</el-breadcrumb-item>
        </el-breadcrumb>

        <div
          style="
            /* margin-bottom: 80px; */
            /* padding-bottom: 40px; */
            background: #fff;
            overflow: hidden;
          "
        >
          <div class="page_comStyle recyleList_box">
            <div class="recyleList_title recyle_position">号商回收</div>
            <div
              class="gamePlay_wrap spaceStart recyle_position"
              style="padding: 34.28px 0; border-bottom: 0.5px solid #ff7a00"
            >
              <div class="spaceStart" style="flex-shrink: 0">
                <div class="gamePlay_wrap_pic">
                  <img :src="jsonGame.icon" style="width: 100%; height: 100%" />
                </div>
                <div class="gamePlay_wrap_title">{{ jsonGame.name }}</div>
              </div>
              <div
                style="
                  font-size: 17.14px;
                  line-height: 28.14px;
                  color: #969696;
                  font-weight: 400;
                  font-family: PingFang SC;
                  padding-left: 34.28px;
                "
              >
                <span class=""
                  >平台号商均为合作第三方，并非看看账号网官方，请注意分辨。</span
                >
                <br />
                <span class=""
                  >若号商引导您至线下沟通交易，请及时举报，举报属实奖励1000R。</span
                >
                <br />
                <span class="">投诉电话：13208028882</span>
              </div>
            </div>
            <!-- 号商列表 -->
            <div class="spaceStart recyle_position" style="flex-wrap: wrap">
              <div
                v-for="(item, index) in recyleList"
                :key="index"
                class="cusAss_item recyleAss_item"
              >
                <div class="recyleSss_item_pic">
                  <el-image
                    v-if="item.icon"
                    :src="item.icon"
                    :preview-src-list="[item.icon]"
                    style="width: 100%; height: 100%; border-radius: 20.56px"
                    fit="cover"
                  ></el-image>
                  <el-image
                    v-else
                    style="width: 100%; height: 100%; border-radius: 20.56px"
                    src="../../../static/user_default.png"
                    fit="cover"
                  ></el-image>
                </div>
                <div>
                  <div class="cusAss_item_title">{{ item.nickname }}</div>
                  <div class="hasPush_mony spaceCenter">已缴纳保障金</div>
                  <div
                    v-if="item.imCode"
                    class="cusGo_btn ws-chat"
                    style="margin-top: 10px"
                    @click="goChat(item)"
                  >
                    快速咨询
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed />
  </div>
</template>

<script>
import { getBigMemberList, getTeamBigMember } from '@/api/kf.js';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

import isLogin from '@/utils/isLogin';

// import { recyleListApi, allGameaApi } from '@/api/index';
import { getProductCategory } from '@/api/search';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      index: 2,
      productCategoryId: '',
      flag_name: '',
      jsonGame: {}, // 游戏展示
      recyleList: [],
      // token: localStorage.getItem('txToken')
      //   ? localStorage.getItem('txToken')
      //   : '',
    };
  },
  computed: {
    // imok() {
    //   return this.$store.getters.imok;
    // },
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    if (this.$route.query.productCategoryId) {
      this.productCategoryId = this.$route.query.productCategoryId;
      //   this.flag_name = this.$route.query.name;
      this.initGameAll();
      this.initGame();
    }
  },
  methods: {
    goChat(item) {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      getTeamBigMember({
        bigMemberId: item.id,
        cateId: this.productCategoryId,
      }).then((res) => {
        if (res.code == 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          const sessionId = `team-${imcode}`;
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive('team', imcode);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    // 游戏-初始化所有游戏
    initGameAll() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code == 200) {
          const { data } = res;
          this.flag_name = data.name;
          this.jsonGame = data;
        }
      });
    },
    // 号商-初始化
    initGame() {
      getBigMemberList({
        categoryId: this.productCategoryId,
      }).then((res) => {
        if (res.code == 200) {
          this.recyleList = res.data;
        }
      });
      //   recyleListApi({
      //     productCategoryId: this.productCategoryId,
      //   }).then((response) => {
      //     if (response.code == 200) {
      //       this.recyleList = response.data;
      //     }
      //   });
    },
    // 跳转
    // playPage(date) {
    //   this.$router.push({
    //     path: '/recyleDetail?url=' + date.url,
    //   });
    // },
  },
};
</script>

<style type="text/css">
@import url(../assure/cusCss.css);
.el-image-viewer__img {
  width: 700px;
  height: 700px;
  border-radius: 24px;
}
</style>
<style scoped type="text/css">
.recyleList_box_bk {
  background: linear-gradient(
    180deg,
    #fff1e2 16.51%,
    #fff9f3 69.94%,
    #ffe1c3 200%
  );
  padding-bottom: 80px;
}
.recyle_position {
  position: relative;
  z-index: 2;
}
.recyleAss_item {
  width: 198.823px;
  height: 332.516px;
  /* background: #fffcf9; */
  /* border: 1px solid #dcdcdc; */
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  margin-top: 34.28px;
  margin-bottom: 0px;
  margin-right: 34.18px;
  border-radius: 25.71px;
  /* background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  border: 4px solid transparent;
  background-image: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  border-radius: 25.71px; */
  border: 1px solid transparent;

  background: linear-gradient(to right, #fffcf9, #fffcf9),
    radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);

  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
}
.cusAss_item :hover {
  /* border: none !important; */
}
/* .recyleAss_item::before {
  content: '' !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25.71px;
  background: red;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
  position: absolute;
} */
.recyleAss_item:nth-child(5n) {
  margin-right: 0px;
}

.recyleSss_item_pic {
  width: 198.823px;
  height: 198.823px;
  margin-bottom: 10.283px;
  margin-top: -1px;
  margin-left: -1px;
}
.hasPush_mony {
  width: 84.843px;
  height: 21.425px;
  background: #fff4ee;
  color: #ffb74a;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 10.284px;
  font-style: normal;
  font-weight: 400;
  border-radius: 8px;
  border: 1px solid#ffb74a;
  margin: auto;
  background: rgba(255, 183, 74, 0.1);
}
.bold_red {
  color: #fe5a1e;
  font-weight: 600;
}
.recyleList_box {
  border-radius: 25.71px;
  padding: 34.28px;
  background: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  position: relative;
}
.recyleList_box::before {
  content: '';
  position: absolute;
  background: linear-gradient(
    180deg,
    #fff 27.6%,
    rgba(255, 251, 247, 0.95) 98.76%
  );
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 3px;
  border-radius: 23.71px;
}
.recyleList_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px; /* 100% */
}
.gamePlay_wrap_pic {
  width: 102.84px;
  height: 102.84px;
  border-radius: 10.28px;
  margin-right: 17.14px;
}
.gamePlay_wrap_title {
  color: #222;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 20.56px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.48px;
}
.cusAss_item_title {
  color: #1b1b1b;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 20.567px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.cusGo_btn {
  width: 146.547px;
  height: 38.1px;
  border-radius: 51.42px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  margin: 0 auto;
  text-align: center;
  margin-top: 10.73px;
  /* background: url(../../../static/imgs/playDetail_qq_btn_bk.png);
  background-size: 100% 100%; */
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
}
.cusGo_btn:hover {
  /* border: 1px solid #ffddbe; */
}
</style>
