<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <div class="gameListBk">
      <headerKk :active-index="index" />

      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>号商回收</el-breadcrumb-item>
        </el-breadcrumb>

        <div
          style="
            /* margin-bottom: 80px; */

            /* border-radius: 6px; */
            overflow: hidden;
            background: var(--game-list-background-gradient);
          "
        >
          <div
            class="page_comStyle recyle_box"
            style="
              margin-bottom: 34.28px;
              padding: 34.28px;
              border-radius: 25.71px;
            "
          >
            <div class="recyle_title recyle_position">号商回收</div>
            <!-- 回收流程 -->
            <div
              style="display: flex; justify-content: center"
              class="spaceStart recyle_position"
            >
              <div class="proRecy_item one">
                <div class="proRecy_itemT">选择回收号商</div>
                <div>平台提供号商交易有保障</div>
                <img
                  class="proRecy_item_logo"
                  src="../../../static/imgs/logo_Bk.svg"
                  alt=""
                />
              </div>
              <!-- <img class="next_pic" src="../../../static/recyle/next.png" /> -->
              <IconFont
                :size="30.852"
                color="#969696"
                icon="padding-arrow_right"
                class="arrow_right_icon"
              />
              <div class="proRecy_item two">
                <div class="proRecy_itemT">官网咨询回收</div>
                <div>直接咨询详情价格自己定</div>
                <img
                  class="proRecy_item_logo"
                  src="../../../static/imgs/logo_Bk.svg"
                  alt=""
                />
              </div>
              <IconFont
                :size="30.852"
                color="#969696"
                icon="padding-arrow_right"
                class="arrow_right_icon"
              />

              <div class="proRecy_item three">
                <div class="proRecy_itemT">号商付款给平台</div>
                <div>平台负责保管交易更安心</div>
                <img
                  class="proRecy_item_logo"
                  src="../../../static/imgs/logo_Bk.svg"
                  alt=""
                />
              </div>
              <IconFont
                :size="30.852"
                color="#969696"
                icon="padding-arrow_right"
                class="arrow_right_icon"
              />
              <div class="proRecy_item four">
                <div class="proRecy_itemT">换绑账号信息</div>
                <div>在平台的监督下高效完成</div>
                <img
                  class="proRecy_item_logo"
                  src="../../../static/imgs/logo_Bk.svg"
                  alt=""
                />
              </div>
              <IconFont
                :size="30.852"
                color="#969696"
                class="arrow_right_icon"
                icon="padding-arrow_right"
              />
              <div class="proRecy_item five">
                <div class="proRecy_itemT">完成结算资金</div>
                <div>资金快速提现到您的账户</div>
                <img
                  class="proRecy_item_logo"
                  src="../../../static/imgs/logo_Bk.svg"
                  alt=""
                />
              </div>
            </div>
            <div class="notice_wrap recyle_position">
              <div class="notice_tit">
                <div class="title_tit">*</div>
                注意
                <!-- <img
                style="width: 52px"
                src="../../../static/imgs/recyle_notice_text.svg"
                alt=""
              /> -->
              </div>
              <div class="notice_tx">
                为满足部分用户快速脱坑需求，特开放号商回收专区，<span
                  class="bold_red"
                  >平台号商均为合作第三方，并非看看账号网官方，请注意分辨。</span
                >
                <br />
                为保证资金安全，所有聊天均要求通过平台沟通，<span
                  class="bold_red"
                  >若号商引导您至线下沟通交易，请及时举报，举报属实奖励1000R。</span
                >
                <br />
                合作号商均在平台缴纳高额保证金，谈好交易均通过平台交易。如号商有谩骂、引导线下、欺骗欺瞒等情况发生，均可带记录联系平台，投诉属实顶格赔付。<br />
                <span class="bold_red"
                  >投诉电话：<span class="bold_red_iphone"
                    >13208028882</span
                  ></span
                >
              </div>
            </div>
          </div>
          <gameList
            style="background: var(--game-list-background-gradient)"
            @playPage="playPage"
          >
          </gameList>
        </div>
      </div>
    </div>
    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import gameItem from '@/components/gameItem/index';
import zimuList from '@/components/zimuList/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import gameList from '@/components/gameList/index';
import { allGameaApi } from '@/api/index';

export default {
  components: {
    gameItem,
    zimuList,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    gameList,
  },
  data() {
    return {
      index: 2,
      gameList: [],
      type: '',
    };
  },
  mounted() {},
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 跳转
    playPage(date) {
      this.$router.push({
        path: '/recyleList?productCategoryId=' + date.id,
      });
    },
  },
};
</script>

<style type="text/css">
@import url(../assure/cusCss.css);
</style>
<style scoped>
.recyle_position {
  position: relative;
  z-index: 2;
}
.recyle_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  margin-bottom: 34.28px;
}
.gameType_wrap {
  font-size: 16px;
  color: #909090;
}
.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}
.gameAll_wrap {
  flex-wrap: wrap;
}
.proRecy_item {
  width: 185.97px;
  height: 90.842px;
  font-size: 12.22px;
  font-weight: 500;
  color: #969696;
  text-align: center;
  font-family: 'PingFang SC';
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  border-radius: 20px;
  position: relative;
}
.arrow_right_icon {
  margin: 0px 3.428px;
}
.proRecy_item div {
  position: relative;
  z-index: 2;
}
.proRecy_item_logo {
  position: absolute;
  width: 62.561px;
  height: 65.45px;
  top: 12.615;
  left: 11.36px;
  z-index: 1;
}
.proRecy_item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  margin: 1px;
  background: #fffcf9;
  position: absolute;
  border-radius: 19px;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border: none;
}
.recyle_box {
  background: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  position: relative;
}
.recyle_box::before {
  content: '';
  position: absolute;
  background: linear-gradient(
    180deg,
    #fff 27.6%,
    rgba(255, 251, 247, 0.95) 98.76%
  );
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 3px;
  border-radius: 23.71px;
}
/* .proRecy_item.one {
  background: url(../../../static/recyle/1.png) no-repeat center center;
  background-size: cover;
}
.proRecy_item.two {
  background: url(../../../static/recyle/2.png) no-repeat center center;
  background-size: cover;
}
.proRecy_item.three {
  background: url(../../../static/recyle/3.png) no-repeat center center;
  background-size: cover;
}
.proRecy_item.four {
  background: url(../../../static/recyle/4.png) no-repeat center center;
  background-size: cover;
}
.proRecy_item.five {
  background: url(../../../static/recyle/5.png) no-repeat center center;
  background-size: cover;
} */
.next_pic {
  width: 30.85px;
  margin: 0 4px;
}
.proRecy_itemT {
  color: #000;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 3.43px;
}
.notice_tit {
  /* padding-left: 20px; */
  color: #1b1b1b;
  font-size: 17.14px;
  font-weight: 400;
  /* background: url(../../../static/imgs/recyle_notice_text.svg) no-repeat left
    center; */
  /* background-size: 54px; */
  letter-spacing: 0.8px;
  font-family: 'PingFang SC';
  margin-bottom: 6.856px;
  display: flex;
  align-items: center;
}
.title_tit {
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  /* display: flex;
  align-items: center; */
  font-size: 24px;
  height: 28px;
  line-height: 34px;
  margin: 0px 2.8px;
}
.notice_tx {
  margin-left: 19.71px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}
.notice_wrap {
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin-top: 17.14px;
  margin-left: 23px;
  line-height: 28px;
}
.bold_red {
  color: #ff720c;
}
.bold_red_iphone {
  color: #ff720c;
  font-family: Inter;
}
</style>
