<template>
  <div class="dark_container">
    <div class="forgetPwdBk">
      <div class="loginHeadContent">
        <loginHead />
      </div>
      <headerKk :active-index="activeIndex" />
      <div
        class="safe_width"
        style="
          border-radius: 24px;
          overflow: hidden;
          margin-top: 20px;

          background: #fff;

          padding: 40px;
        "
      >
        <div class="regin_head spaceBetween">
          <div class="spaceStart">
            <!-- <i class="el-icon-user"></i>&nbsp;&nbsp; -->
            <span class="forgotPassword">忘记密码</span>
          </div>
          <div class="accountText spaceStart login_n">
            <div>已有账号？</div>
            <router-link to="/login" class="forgotPassword_right_text"
              >立即登录</router-link
            >
          </div>
        </div>
        <div class="forgetUnderscore">
          <div class="forgetUnderscoreContent"></div>
        </div>

        <div class="page_comStyle_login_box">
          <!-- <img class="formBk" src="../../../static/imgs/logo_Bk.png" alt="" /> -->
          <!-- <p style="text-align: center; color: #ff6700">
          如登录遇到问题，请联系官方<a
            href="https://work.weixin.qq.com/kfid/kfce65b2a89d0b1efb7"
            target="_black"
            style="text-decoration: underline"
            >微信客服</a
          >
        </p> -->
          <el-form
            v-loading.fullscreen.lock="loading"
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            label-width="130px"
            auto-complete="on"
            class="loginFormStyle"
            style="margin-top: 40px"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
          >
            <div class="reginForm_box">
              <el-form-item
                class="forget-pwd-input"
                prop="phone"
                label="手机号"
              >
                <el-input
                  v-model="loginForm.phone"
                  style="color: #1b1b1b !important"
                  type="text"
                  placeholder="请输入手机号码"
                />
              </el-form-item>

              <el-form-item
                class="forget-pwd-input"
                prop="password"
                label="密码"
              >
                <el-input
                  :type="pwdType"
                  v-model="loginForm.password"
                  style="color: #1b1b1b"
                  placeholder="请输入登录密码"
                />
                <span class="show-pwdR" @click="showPwd">
                  <svg-icon
                    :class="pwdType === 'password' ? '' : 'password1'"
                    :icon-class="pwdType === 'password' ? 'eye' : 'eye-open'"
                  />
                </span>
              </el-form-item>

              <el-form-item
                class="forget-pwd-input"
                prop="password"
                label="确认密码"
              >
                <el-input
                  :type="pwdTypeTwo"
                  v-model="loginForm.password2"
                  style="color: #1b1b1b"
                  placeholder="请输入登录密码"
                />
                <span class="show-pwdR" @click="showPwdTwo">
                  <svg-icon
                    :class="pwdTypeTwo === 'password' ? '' : 'password2'"
                    :icon-class="pwdTypeTwo === 'password' ? 'eye' : 'eye-open'"
                  />
                </span>
              </el-form-item>

              <el-form-item
                class="forget-pwd-input"
                prop="code"
                label="短信验证码"
              >
                <el-input
                  v-model="loginForm.code"
                  type="text"
                  placeholder="请输入验证码"
                  style="color: #1b1b1b"
                />
                <div
                  style="margin-right: 13px"
                  class="code_wrap posionRight"
                  @click="sendCode"
                >
                  {{ codeMsg }}
                </div>
              </el-form-item>

              <el-button
                :loading="loading"
                :class="buttonClass"
                type="primary"
                @click.native.prevent="handleLogin"
              >
                同意协议并修改密码
              </el-button>
              <div
                style="
                  padding-top: 20px;
                  margin-left: 83px;

                  display: flex;
                  align-items: center;
                "
                class="spaceAlignCenter agreeRegin"
              >
                <!-- <el-checkbox
                v-model="checked"
                style="margin-right: 6px"
              ></el-checkbox> -->
                <!-- <IconFont
                v-if="!checked"
                :size="20"
                style="margin-right: 8px; cursor: pointer"
                icon="unchecked"
                @click="changChecked(true)"
              /> -->
                <div
                  v-if="!checked"
                  style="
                    width: 20px;
                    height: 20px;
                    border-radius: 3px;
                    border: 2px solid #969696;
                    margin-right: 8px;
                    cursor: pointer;
                    margin-top: -2px;
                  "
                  icon="unchecked"
                  @click="changChecked(true)"
                ></div>
                <IconFont
                  v-if="checked"
                  :size="20"
                  style="
                    margin-right: 8px;
                    cursor: pointer;
                    color: #ff720c;
                    width: 20px;
                    height: 20px;
                  "
                  icon="checked"
                  @click="changChecked(false)"
                />
                <div
                  style="
                    font-size: 14px;
                    font-family: PingFang SC;
                    letter-spacing: 0.56px;
                    color: rgba(0, 0, 0, 0.4);
                  "
                >
                  我已阅读并同意
                  <span class="agree" @click="agreeMentFun"
                    >《看看账号网用户协议》</span
                  >
                </div>
              </div>
              <div v-if="checkedFlag" class="spaceCenter footerAgreementText">
                请阅读并同意登录注册协议～
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <div class="footerCt">
      <footerKk />
    </div>

    <borderDialog :visible="visible" type="edit" @dialogClone="dialogClone" />
  </div>
</template>

<script>
import footerKk from '@/components/footerKk/index';
import loginHead from '@/components/loginHead/index';
import headerKk from '@/components/headerKk/header';
import borderDialog from '@/components/borderDialog/index';
import { updatePwdApi } from '@/api/index';

import { sendPhoneCode } from '@/api/login';
import '@/utils/yidun-captcha';

export default {
  components: {
    footerKk,
    loginHead,
    headerKk,
    borderDialog,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('请输入登录手机号'));
      } else {
        callback();
      }
    };
    const validatePass = (rule, value, callback) => {
      if (value.length < 5) {
        callback(new Error('密码不能小于5位'));
      } else {
        callback();
      }
    };
    const validateCode = (rule, value, callback) => {
      if (value.length != 6) {
        callback(new Error('验证码为6位'));
      } else {
        callback();
      }
    };

    return {
      // 17788072597
      visible: false,
      loginType: 1,
      codeMsg: '获取验证码',
      code: 60,
      isDis: false,
      checkedFlag: false,
      codeNum: '',
      loginForm: {
        phone: '',
        password: '',
        password2: '',
        code: '',
        platform: 5,
      },
      loginRules: {
        phone: [
          {
            required: true,
            trigger: 'blur',
            validator: validateUsername,
          },
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePass,
          },
        ],
        code: [
          {
            required: true,
            trigger: 'blur',
            validator: validateCode,
          },
        ],
      },
      loading: false,
      pwdType: 'password',
      pwdTypeTwo: 'password',
      redirect: undefined,
      checked: false,
    };
  },
  computed: {
    buttonClass() {
      const emptyFields = Object.keys(this.loginForm).filter((key) => {
        const value = this.loginForm[key];
        return value === null || value === undefined || value === '';
      });
      return [
        'loginBtnPwd',
        'reginBtn',
        'btn_padding',

        emptyFields.length > 0 ? 'btnDefalut2x' : 'btnActive2x',
      ];
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
    // loginForm: {
    //   handler(newVal) {
    //     console.log(newVal, 11111);
    //     this.validateForm();
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    this.initCaptcha();
  },
  methods: {
    // validateForm() {
    //   this.$refs.loginForm.validate((valid) => {
    //     console.log(valid);
    //   });
    // },
    initCaptcha() {
      window.initNECaptchaWithFallback(
        {
          captchaId: '3455bd8a6484410ea146980a113839aa',
          width: '320px',
          mode: 'popup',
          apiVersion: 2,
          onVerify: (err, data) => {
            if (err) return;
            this.doSendSmsCode(data);
          },
        },
        (instance) => {
          this.captchaIns = instance;
        }
      );
    },
    agreeMentFun() {
      this.$router.push({
        path: '/helpCenter?id=65',
      });
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = '';
      } else {
        this.pwdType = 'password';
      }
    },
    showPwdTwo() {
      if (this.pwdTypeTwo === 'password') {
        this.pwdTypeTwo = '';
      } else {
        this.pwdTypeTwo = 'password';
      }
    },
    /**
     * 获取验证码
     */
    sendCode() {
      if (this.isDis == true) {
        this.$message.error('请稍后重试');
        return;
      }
      var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      if (this.loginForm.phone == '') {
        this.$message.error('请输入手机号码');
        return;
      }
      if (!myreg.test(this.loginForm.phone)) {
        this.$message.error('请输入正确的手机号码');
        return false;
      }
      this.captchaIns && this.captchaIns.verify();
    },

    doSendSmsCode(data) {
      this.loading = true;
      sendPhoneCode({
        telephone: this.loginForm.phone,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('验证码发送成功！');
            this.countDown();
          }
          this.loading = false;
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
    /**
     * 倒计时
     */
    countDown() {
      this.code -= 1;
      if (this.code == 0) {
        this.code = 60;
        this.codeMsg = '获取验证码';
        this.isDis = false;
        clearInterval(interval);
        return;
      }
      this.codeMsg = '重新获取' + this.code + 'S';
      this.isDis = true;
      var _this = this;
      var interval = setTimeout(function () {
        _this.countDown();
      }, 1000);
    },
    // 提交注册-验证
    handleLogin() {
      if (!this.checked) {
        // this.$message.error('请阅读并同意登录注册协议~');
        this.checkedFlag = true;
        return;
      }
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.reginFun();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //协议选择框
    changChecked(flag) {
      this.checked = flag;
      if (flag) {
        this.checkedFlag = false;
      }
    },
    reginFun() {
      if (this.loginForm.password != this.loginForm.password2) {
        this.$message.error('两次输入密码不一致~');
        return;
      }
      this.loading = true;
      let data = {
        authCode: this.loginForm.code,
        telephone: this.loginForm.phone,
        password: this.loginForm.password,
        username: this.loginForm.phone,
      };
      updatePwdApi(data).then((res) => {
        if (res.code == 200) {
          // this.$message.success('修改成功！快去登录吧~');
          localStorage.removeItem('token');
          localStorage.removeItem('yximtoken');
          this.$store.dispatch('clearUserInfo');
          this.visible = true;
          // setTimeout(() => {
          //   this.$router.push({
          //     path: '/login',
          //     query: {
          //       redirect: location.href,
          //     },
          //   });
          // }, 600);
        }
        this.loading = false;
      });
    },
    dialogClone() {
      this.visible = false;
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
    },
    firgetBtn() {
      this.visible = false;
    },
  },
};
</script>

<style scoped>
@import url(./login.css);
.page_comStyle_login_box {
  position: relative;
  padding: 0px;
  /* overflow: hidden; */
}
.formBk {
  width: 470px;
  height: 489.347px;
  position: absolute;
  top: 35.137px;
  right: -113.1px;
}
.reginForm_box /deep/ .el-input {
  width: 276px !important;
}
::v-deep .el-form-item__label:before {
  color: rgba(255, 122, 0, 1) !important; /* 修改星号的颜色 */
}
.loginFormStyle /deep/ .el-form-item__label {
  color: #1b1b1b;
  letter-spacing: 0.64px;
  font-size: 16px;
  /* margin-top: 7px; */
  font-family: 'PingFang SC';
  font-weight: 400;
  padding: 0px;
  margin-right: 20px;
}
.loginFormStyle /deep/.reginForm_box .el-form-item {
  margin-bottom: 30px !important;
}

.agree {
  cursor: pointer;
  color: #ffb74a;
}
.dark_container {
  background: #fdf3e7;
}

.reginForm_box {
  width: 487px;
}
.forget-pwd-input /deep/ .el-input__inner {
  width: 322px;
  border-radius: 50px;
  height: 52px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.56px;
  padding: 0px 20px;
  color: #1b1b1b;
  border-color: rgba(150, 150, 150, 0.4) !important;
}
.forget-pwd-input /deep/.el-form-item__error {
  margin-left: 50px;
}
.show-pwdR {
  right: 43px;
  top: 4px;
  /* margin-top: 3px; */

  /* right: 105px; */
}
.show-pwdR .svg-icon {
  width: 21.913px;
  height: 10.957px;
}
.show-pwdR .password1 {
  width: 21px;
  height: 21px;
  margin-top: 12px;
}
.show-pwdR .password2 {
  width: 21px;
  height: 21px;
  margin-top: 12px;
}
.posionRight {
  background-color: transparent;
}
.code_wrap {
  background-color: transparent;
}
.loginBtnPwd {
  /* width: 194.5px; */
  height: 50px;
  border-radius: 60px;
  /* padding: 18px 0; */
  font-size: 16px;
  font-family: 'PingFang SC';
  letter-spacing: 0.64px;
  font-weight: 500;
}

.accountText {
  color: #969696;
  font-size: 16px;
  font-family: 'PingFang SC';
  font-weight: 400;
  letter-spacing: 0.64px;
}
.regin_head {
  border-bottom: none;
}
.forgetUnderscore {
  width: 100%;
  background: #fff;
}
.forgetUnderscoreContent {
  width: 100%;
  height: 0.5px;
  background: #ff7a00;
  margin: 0 auto;
}

.reginBtn {
  margin-left: 115px;
}
.forgetPwdBk {
  background: linear-gradient(
    180deg,
    #fff1e2 16.51%,
    #fff9f3 44.06%,
    #fff9f3 74.95%,
    #ffe1c3 100%
  );
  padding-bottom: 80px;
}
</style>
