<template>
  <div class="dark_container">
    <div class="safe_width">
      <div class="login_header_wrap spaceBetween" style="padding-top: 10px">
        <div class="topPage_tit">您好，欢迎来到看看账号网游戏交易平台！</div>

        <div class="spaceEnd login_header_nav">
          <router-link to="/">返回首页</router-link>
        </div>
      </div>
    </div>

    <div class="downLoad_container">
      <div class="safe_width spaceBetween">
        <img src="../../../static/Papp.png" style="width: 100%" />
        <div>
          <canvas
            id="QRCode_download"
            style="width: 190px; height: 190px"
          ></canvas>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import footerKk from '@/components/footerKk/index';
import loginHead from '@/components/loginHead/index';

export default {
  name: 'Login',
  components: {
    footerKk,
    loginHead,
  },
  data() {
    return {
      loading: false,
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style rel="stylesheet/scss" scoped>
.dark_container {
  background: linear-gradient(#fdf3e0, #eca87f);
  height: 100%;
}
.downLoad_container {
  width: 100%;
  height: 100%;
  background-size: auto 100%;
  box-sizing: border-box;
  padding: 80px 0;
}
.down_left {
  width: 600px;
}
.down_right {
  width: 420px;
  font-size: 46px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
}
.subD_tit {
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;
  padding-top: 12px;
}
.codeD_wrap {
  width: 200px;
  border-radius: 10px;
  background: #fff;
  box-sizing: border-box;
  padding: 15px;
}
.codeD_wrap > img {
  display: block;
  width: 100%;
  height: 100%;
}
.down_btn {
  width: 186px;
  height: 50px;
  background: #ffffff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #222222;
  box-sizing: border-box;
  padding: 0 18px;
  cursor: pointer;
}
.down_btn img {
  width: 20px;
}
</style>
