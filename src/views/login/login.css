.regin_head {
  border-bottom: 1px solid #dcdcdc;
  /* padding: 20px; */
  background: #fff;
  font-size: 18px;
  font-weight: 400;
  color: #222222;
  padding-bottom: 19px;
}
.login_n {
  font-size: 14px;
}
.forgotPassword {
  font-size: 32px;
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.forgotPassword_right_text {
  font-size: 16px;
  color: #ff720c;
  font-family: 'PingFang SC';
  font-weight: 400;
  background: var(--btn-background-gradient);
  letter-spacing: 0.64px;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
}
.forget-pwd-input /deep/.el-form-item__error {
  /* background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  color: transparent;
  -webkit-background-clip: text;
   background-clip: text; */
  color: #ff720c;
  height: 20px;
  font-family: 'PingFang SC';
  padding: 0px;
  margin-top: 3px;
  font-weight: 400;
}
.footerAgreementText {
  font-size: 12px;
  margin-top: 8.57px;
  /* color: #ff720c; */
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  color: transparent;
  -webkit-background-clip: text; /* background-clip: text; */
  height: 20px;
  font-family: 'PingFang SC';

  font-weight: 400;
}
.forget-pwd-input /deep/.el-input__inner {
  border-color: rgba(150, 150, 150, 0.4);
}
.forget-pwd-input /deep/:focus-within .el-input__inner {
  border: none;
  background: url(../../../static/imgs/border_medium_big_bk.png);
  background-size: 100% 100%;
  z-index: 99px;
  border-radius: 0px !important;
  padding: 0px 21px;
}

.is-error /deep/ .el-input__inner {
  border: none;
  background: url(../../../static/imgs/border_medium_big_bk.png);
  background-size: 100% 100%;
  z-index: 99px;
  border-radius: 0px !important;
  padding: 0px 21px !important;
}
.code_wrap {
  float: right;
  height: 52px;
  width: 120px;
  background: #fff;
  color: #ff7a00;
  border: none;
  cursor: pointer;
  text-align: center;
  line-height: 52px;
  border-radius: 20px;
  font-weight: 500;
}

.reginForm_box {
  width: 60%;
  box-sizing: border-box;
  /* padding: 30.28px 0px; */
  margin: 0 auto;
}

.loginBtn {
  width: 100%;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  color: #fff;
  border-color: #ff9600;
  margin-top: 40px;
  padding: 18px 0;
}
.loginBtn:hover {
  background: linear-gradient(90deg, #ff9600, #ff6700);
}
.reginBtn {
  /* width: 70%; */
  /* margin: 8.57px auto 0; */
  margin-left: 130.5px;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
}

.show-pwdR {
  position: absolute;
  /* right: 20px; */
  right: 70px;
  top: 0px;
  font-size: 16px;
  color: #909090;
  cursor: pointer;
  user-select: none;
}
.posionRight {
  position: absolute;
  right: 0;
  top: 0;
  background: #ff7a00;
  color: #ff7a00;
  font-family: 'PingFang SC';
  font-size: 14px;

  height: 49px;
  letter-spacing: 0.56px;
  line-height: 49px;
  border-radius: 4px;
  margin-top: 2px;
}
.agreeRegin {
  padding-top: 30px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

/* .reginBtn.el-button--primary:focus,
.reginBtn.el-button--primary:hover {
  border-color: transparent;
  color: #fff;
  background: linear-gradient(90deg, #ff9600, #ff6700);
} */
.reginForm_box .el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid rgba(150, 150, 150, 0.4);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;

  display: inline-block;
  font-size: inherit;
  height: 50px;
  line-height: 50px;
  outline: 0;
  padding: 0 15px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
/* .reginForm_box .el-form-item__error {
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  color: transparent;
  -webkit-background-clip: text;
  height: 20px;
  font-family: 'PingFang SC';
  line-height: 20px;
  padding: 0px;
} */
.loginHeadContent {
  width: 100%;
  background: #fff;
}
.btn_padding {
  padding: 18px 48px;
}
