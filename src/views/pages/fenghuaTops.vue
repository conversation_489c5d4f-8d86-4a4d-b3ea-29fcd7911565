<template>
  <div class="dark_container">
    <headerKk :active-index="index" />

    <div
      class="safe_width tops_container"
      style="align-items: flex-start; position: relative"
    >
      <div class="ImButton" @click="goIm"></div>
      <div>
        <div class="bottom_tops">
          数据更新时间：2023年7月9日 如对数据如有异议请联系专区客服
        </div>
        <div
          class="tableTops_wrap spaceBetween borderBottom"
          style="margin-bottom: 8px"
        >
          <div style="width: 10%" class="">排名</div>
          <div class="">角色名</div>
          <div class="">区服</div>
          <div class="">帮会</div>
          <div style="width: 15%" class="">风华评分</div>
          <!-- <div v-if="showShare" class="spaceStart cursor" @click="shareDailog">
            分享<img
              style="width: 32px; margin-left: 6px"
              src="../../../static/fenxiang.png"
            />
          </div> -->
        </div>
        <div
          v-for="(item, index) in dateList"
          :key="index"
          class="tableTops_wrap spaceBetween"
        >
          <div style="width: 10%" class="">{{ index + 1 }}</div>
          <div class="">{{ item.characterName }}</div>
          <div class="">{{ item.server }}</div>
          <div class="">{{ item.guild }}</div>
          <div style="width: 15%" class="">{{ item.totalScore }}</div>
        </div>
      </div>
    </div>

    <!-- 分享 -->
    <el-dialog
      :visible.sync="dialogVisibleShare"
      width="32%"
      center
      title="全区全服十大高手"
    >
      <div>
        <img :src="wxUrlCode" style="width: 100%" />
      </div>
    </el-dialog>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import util from '@/utils/index';
import QRCode from 'qrcode';

import { topsApi } from '@/api/index2.js';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      showShare: false,
      dialogVisibleShare: false,
      wxUrlCode: '',
      index: 0,
      dateList: [], // 列表
    };
  },
  mounted() {
    if (this.$route.query.productCategoryId) {
      this.wxUrlCode =
        'https://images2.kkzhw.com/mall/images/20240624/n6ax5z_1719217644398.webp';
      this.showShare = true;
    }
    this.initGame();
  },
  methods: {
    goIm() {
      util.goImPage('https://kkzhw.com/Im?cateId=76');
    },
    shareDailog() {
      this.dialogVisibleShare = true;
    },
    // 列表初始化
    initGame() {
      topsApi(this.$route.query.productCategoryId).then((response) => {
        if (response.code == 200) {
          this.dateList = response.data;
        }
      });
    },
    // 跳转
    playPage(date) {
      this.$router.push({
        path: '/playList?productCategoryId=' + date.id,
      });
    },
  },
};
</script>

<style scoped>
.tops_container {
  width: 100%;
  height: 1080px;
  background: url(../../../static/imgs/fenghuabj.jpg) no-repeat center top;
  /* background-size: 1920px auto; */
  background-size: 100% 100%;
  padding-top: 300px;
  margin-top: 20px;
  position: relative;
}
.tableTops_wrap {
  font-family: Helvetica Neue, Helvetica, Arial, Microsoft Yahei UI,
    Microsoft YaHei, SimHei, '\5B8B\4F53', simsun, sans-serif;
  font-size: 1.2vw;
  color: #fff;
  font-weight: 500;
  /* text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff,
    1px 1px 0 #fff; */
  padding: 11px 0;
  width: 40%;
  margin: 0 auto;
}
.tableTops_wrap > div {
  width: 25%;
  text-align: center;
  flex-shrink: 0;
}
.tableTops_wrap.borderBottom {
  border-bottom: 2px solid #f9f5e3;
}
.clod_Bottom {
  width: 1920px;
  height: auto;
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -960px;
  z-index: 1;
}
.topsTit_pic {
  width: 580px;
  display: block;
  margin: 0 auto;
  margin-bottom: 20px;
}
.bottom_tops {
  text-align: center;
  padding-top: 0px;
  margin-top: -40px;
  margin-bottom: 20px;
  font-family: Helvetica Neue, Helvetica, Arial, Microsoft Yahei UI,
    Microsoft YaHei, SimHei, '\5B8B\4F53', simsun, sans-serif;
  color: #fff;
  font-size: 20px;
}
.ImButton {
  width: 120px;
  height: 120px;
  position: absolute;
  bottom: 45px;
  right: 13vw;
  cursor: pointer;
}
</style>
