<template>
  <div style="position: relative">
    <headerKk :active-index="index" />
    <div class="sweepBox">
      <div class="boxContent">
        <div class="priceBox">
            <img 
                v-for="(digit, index) in digitCount" 
                :key="index" 
                class="digit" 
                :src="currentDigits[index]" 
                :style="{ transform: `translateY(${offsetY}px)` }"
                />
            <img class="unitImg" src="../../../static/imgs/sweepUnit.png" alt="">
        </div>
      </div>
      <div class="boxFoolter">
        <div class="textBox">饭撒饭撒饭</div>
      </div>
    </div>
  </div>
</template>
<script>
import headerKk from '@/components/headerKk/index';
export default {
  components: {
    headerKk,
  },
  data() {
    return {
      startVal: 0,
      total_over: 30000,
      duration: 3000,
      digitCount: 5,      // 显示位数
      targetNumber: 12345, // 目标数值
      duration: 3000,     // 动画时长(ms)
      currentDigits: [],   // 当前显示的图片路径
      offsetY: 0,         // Y轴偏移量
      digitImages: Array(10).fill().map((_, i) => require(`../../../static/imgs/${i}.png`)) // 预加载图片
    };
  },
  mounted() {
    this.initDigits();
    this.$nextTick(() => {
      setTimeout(this.animateNumber, 500); // 延迟启动确保DOM渲染完成
    });
  },
  methods: {
    initDigits() {
      this.currentDigits = Array(this.digitCount).fill(this.digitImages[0]);
    },
    animateNumber() {
      const startTime = Date.now();
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / this.duration, 1);
        const currentVal = Math.floor(progress * this.targetNumber);
        const paddedVal = String(currentVal).padStart(this.digitCount, '0');
        
        // 更新数字图片和动画偏移
        this.currentDigits = paddedVal.split('').map(d => this.digitImages[d]);
        this.offsetY = 10 * (1 - progress);
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          console.log('Animation completed');
        }
      };
      
      requestAnimationFrame(animate);
    }
  },
};
</script>
<style lang="scss">
.sweepBox {
  background: url(../../../static/imgs/sweepstakesBg.png);
  width: 100%;
  height: auto;
  background-size: 100% auto; /* 或 contain */
  background-repeat: no-repeat;
  /* background-position: center;  */
  margin-top: -88px;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.nav_container {
  position: relative !important;
}
.boxContent {
  background: url(../../../static/imgs/sweepStakesNumber.png);
  width: 1200px;
  height: 675px;
  background-size: 100% auto; /* 或 contain */
  background-repeat: no-repeat;
  margin-top: 109px;
  .priceBox {
    font-size: 106px;
    width: 100%;
    text-align: center;
    margin-top: 396px;
    display: flex;
    align-items: center;
    justify-content: center;
    .unitImg{
        margin-top: 99px;
        margin-right: -26px;
        margin-left: 10px;
    }
  }
}
.boxFoolter {
  background: url(../../../static/imgs/sweepstakesText.png);
  width: 1200px;
  height: 276px;
  background-size: 100% auto; /* 或 contain */
  background-repeat: no-repeat;
  margin-top: 20px;
  .textBox {
    width: 100%;
    height: auto;
    padding: 0px 60px;
    margin-top: 90px;
    font-size: 18px;
    font-weight: 560;
  }
  margin-bottom:100px;
}

.number-scroll {
  display: flex;
  gap: 5px;
  height: 160px;
}
.digit {
  width: 120px;
  height: 166px;
  object-fit: contain;
  transition: transform 0.3s ease;
}
</style>
