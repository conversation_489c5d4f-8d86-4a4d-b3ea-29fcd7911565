<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <div class="gameListBk">
      <headerKk :active-index="index" />

      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>账号估价</el-breadcrumb-item>
        </el-breadcrumb>

        <gameList :need-cash="true" @playPage="playPage">
          <template slot="top">
            <img class="guanggao" src="../../../static/n.png" />
          </template>
        </gameList>
      </div>
    </div>
    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import gameItem from '@/components/gameItem/index';
import zimuList from '@/components/zimuList/index';
import cashGame from '@/components/cashGame/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
// import { allGameaApi, getGujiaBannerListApi } from '@/api/index';
import gameList from '@/components/gameList/index';
export default {
  components: {
    gameItem,
    zimuList,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    cashGame,
    gameList,
  },
  data() {
    return {
      index: 4,
      gameList: [],
      GameBannerList: [],
      type: '',
      str: '',
    };
  },
  mounted() {
    if (this.$route.query.str) {
      this.str = this.$route.query.str;
    }
    this.initGame();
    this.getGameBannerlist(); //获取banner列表
  },
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 游戏-初始化
    initGame() {
      // allGameaApi({
      //   type: this.type,
      //   letter: this.letter == '全部游戏' ? '' : this.letter,
      // }).then((response) => {
      //   if (response.code == 200) {
      //     this.gameList = response.data.list;
      //   }
      // });
    },
    // 筛选
    choseLetter(date) {
      this.letter = date;
      this.initGame();
    },
    // 分类
    chooseType(num) {
      num ? (this.type = num) : (this.type = '');
      this.initGame();
    },
    // 跳转
    playPage(date) {
      this.$router.push({
        path: '/appraList?productCategoryId=' + date.id + '&name=' + date.name,
      });
    },
    //获取banner图
    getGameBannerlist() {
      // getGujiaBannerListApi({ flag_id: 3 }).then((res) => {
      //   if (res.code == 200) {
      //     this.GameBannerList = res.data;
      //   }
      // });
    },
    // 轮播跳转
    pageGo(date) {
      if (date.pc_path) {
        if (date.type == 2) {
          window.open(date.pc_path);
        } else {
          this.$router.push({
            path: date.pc_path,
          });
        }
      }
    },
  },
};
</script>

<style scoped>
.gameListBk {
  background: var(--game-list-background-gradient) !important;
}
.gameType_wrap {
  font-size: 16px;
  color: #909090;
}
.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}
.hotGame_wrap {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 24px;
}
.gameAll_wrap {
  flex-wrap: wrap;
  align-items: flex-start;
}
.guanggao {
  cursor: pointer;
  width: 100%;
  margin-bottom: 14px;
}
</style>
