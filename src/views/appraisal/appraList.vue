<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <div class="gameListBk">
      <headerKk :active-index="index" />

      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/appraisal' }"
            >账号估价</el-breadcrumb-item
          >
          <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
            flag_name
          }}</el-breadcrumb-item>
        </el-breadcrumb>

        <div class="assureListContent">
          <div class="header_custorm">
            <div>账号估价</div>
          </div>
          <div
            style="
              padding: 40px 40px 59px 40px;
              position: relative;
              z-index: 2;
              background: transparent;
            "
            class="page_comStyle"
          >
            <div class="spaceStart" style="flex-wrap: wrap">
              <div
                v-for="(item, index) in assureList"
                :key="index"
                class="cusAss_item"
              >
                <div class="cusSss_item_pic">
                  <el-image
                    v-if="item.pic"
                    :src="item.pic"
                    style="width: 100%; height: 100%"
                    fit="cover"
                  ></el-image>
                  <el-image
                    v-else
                    style="width: 100%; height: 100%"
                    src="../../../static/user_default.png"
                    fit="cover"
                  ></el-image>
                </div>
                <div>
                  <div class="title">{{ item.name }}</div>
                  <div class="time">{{ item.online_time }}</div>
                </div>
                <div class="cusGo_btn ws-chat" @click="goChat(item)">
                  快速咨询
                </div>
              </div>
            </div>

            <div class="cus_descWrap">
              <div>*</div>
              交易说明
            </div>
            <div style="margin-left: 21px">
              <div class="descWrap_tit">【免费估价】</div>
              <div class="descWrap_body">
                1.逆水寒手游需要向客服提供完整的人物面板、背包、打造、内功、衣柜及坐骑祥瑞、群侠图片。
                也可以直接上架联系客服查看编号进行估价。<br />
                2.逆水寒端游需要联系客服预约扫码上号估价。<br />
                3.其他游戏请直接联系客服进行估价。<br />
                4.估价仅供参考，价格是客服根据近期同类型账号成交的价格估算的，不代表账号的具体价值。<br />
                特别提醒：只有账号本人才能享受估价服务，暂不对非本人账号提供该服务。<br />
              </div>
              <!--  <div class="descWrap_tit">【交易流程】</div>
                        <div class="descWrap_body">
                            1：买卖双方协商好价格确认账号状态，联系客服提供双方平台注册手机号，客服会在APP内拉取交易群。<br>
                            2：群内客服发交易须知/交易流程，买卖方告知客服游戏类型/号款金额/是否走包赔等。<br>
                            3：买家付账号全款后，卖家发账号密码在交易群，届时买家上号验证。<br>
                            4：买家需仔细核对账号是否与卖家描述相符（验号期间与描述不符合，双方协商取消，全额退款），验号无误后进行换绑。<br>
                            5：放款时间在账号交付完成以后3小时内进行放款。<br>
                            6：账号进入换绑后协商一致同意可取消，中介费不退。<br>
                            7：订单交易完成。
                        </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed :flag-id="productCategoryId" @goPageTop="backTopPage" />
  </div>
</template>

<script>
import { getKfList, m2kfTalk } from '@/api/kf.js';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

import isLogin from '@/utils/isLogin';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      index: 4,
      productCategoryId: '',
      flag_name: '',
      assureList: [],
    };
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }
    if (this.$route.query.productCategoryId) {
      this.productCategoryId = this.$route.query.productCategoryId;
      this.flag_name = this.$route.query.name;
      this.initGame();
    }
  },
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    goChat(item) {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      const { nim, store } = window.__xkit_store__;
      const imcode = item.IM;
      const sessionId = `p2p-${imcode}`;
      m2kfTalk({
        cateId: this.$route.query.productCategoryId,
        kfIM: imcode,
      });
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imcode);
      }
      this.$store.dispatch('ToggleIM', true);
    },
    // 游戏-初始化
    initGame() {
      getKfList({
        game: this.flag_name,
        type: '咨询客服',
      }).then((res) => {
        if (res.code == 200) {
          this.assureList = res.data;
        }
      });
    },
    // 跳转
    playPage(date) {
      this.$router.push({
        path: '/playList?productCategoryId=' + date.id,
      });
    },
  },
};
</script>

<style scoped>
@import url(./cusCss.css);
</style>
