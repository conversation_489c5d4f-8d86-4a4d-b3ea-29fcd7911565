<template>
    <div ref="bodyScroll" class="dark_container scrollPageSmoth">
      <headerKk :active-index="index" />
  
      <div class="safe_width">
        <el-breadcrumb
          style="padding: 20px 0px"
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>{{activeName}}</el-breadcrumb-item>
        </el-breadcrumb>
  
        <div
          class="safe_width  spaceBetween"
          style="align-items: flex-start;justify-content:center; margin-bottom: 60px"
        >
          <div
            style="padding: 48.849px 67.703px 60px 55.705px"
            class="right_container page_comStyle"
          >
            <div class="pageDate" v-html="detailHtml"></div>
          </div>
        </div>
      </div>
  
      <safeNews />
      <div class="footerCt">
        <footerKk />
      </div>
      <navigation-fixed @goPageTop="backTopPage" />
    </div>
  </template>
  
  <script>
  import headerKk from '@/components/headerKk/index';
  import footerKk from '@/components/footerKk/index';
  import safeNews from '@/components/safeNews/index';
  import navigationFixed from '@/components/navigationFixed/index';
  import { getHelpDetail } from '@/api/help';
  import { getGameList } from '@/api/index2.js';
    
    // import { helpListApi, helpDtApi } from '@/api/help';
    const dateList = [
      {
        id: 1,
        name: '常见问题',
        icon: 'help',
        children: [
          {
            id: 65,
            name: '用户协议',
          },
          {
            id: 99,
            name: '服务协议',
          },
          {
            id: 107,
            name: '法律保护',
          },
        ],
      },
      {
        id: 2,
        name: '卖家相关',
        icon: 'buyer',
        children: [
          {
            id: 63,
            name: '中介担保',
          },
          {
            id: 68,
            name: '如何寄售账号',
          },
          {
            id: 324,
            name: '卖家注意事项',
          },
        ],
      },
      {
        id: 3,
        name: '买家相关',
        icon: 'sale',
        children: [
          {
            id: 103,
            name: '买家收费标准',
          },
          {
            id: 154,
            name: '如何购买',
          },
          {
            id: -1,
            name: '游戏资费',
          },
          {
            id: 97,
            name: '儿童保护',
          },
          {
            id: 323,
            name: '买家注意事项',
          },
        ],
      },
      {
        id: 4,
        name: '交易流程',
        icon: 'shopping',
        children: [
          {
            id: 98,
            name: '投诉建议',
          },
        ],
      },
    ];
    export default {
      components: {
        headerKk,
        footerKk,
        safeNews,
        navigationFixed,
      },
      data() {
        return {
          index: 0,
          defaultActive: '65',
          dateList, // 列表
          activeId: '65',
          type: '',
          detailHtml: '',
          activeName:'活动',
          showzf: false,
          pageDate: [],
        };
      },
      watch: {
        '$route.query.id'(toVal, fromVal) {
          this.open();
        },
      },
      mounted() {
        this.open();
      },
      methods: {
        changeId(id) {
          if (id == 98) {
            this.$router.push({
              path: `/suggest`,
            });
            return;
          }
          this.$router.replace({
            path: `/helpCenter?id=${id}`,
          });
        },
        open() {
          if (this.$route.query.id) {
            this.activeId = this.$route.query.id;
            this.defaultActive = this.activeId;
          }
          if (this.$route.query.name) {
              this.activeName=this.$route.query.name
          }
          this.handleOpen(this.activeId);
        },
        handleOpen(id) {
          if (id == '-1') {
            this.showzf = true;
            getGameList().then((res) => {
              if (res.code == 200) {
                this.pageDate = res.data;
              }
            });
          } else {
            this.showzf = false;
            getHelpDetail(id).then((res) => {
              this.detailHtml = res.data.detailHtml;
            });
          }
        },
        backTopPage() {
          let scrollEl = this.$refs.bodyScroll;
          scrollEl.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        },
        // 列表初始化
        chooseHelp(date) {
          this.activeId = date.id;
          this.initDetail();
        },
      },
    };
    </script>
    
    <style scoped lang="scss">
    ::v-deep .el-menu {
      border: none !important;
    }
    .pageDate {
      width: 100%;
      height: 100%;
      object-fit: cover;
      /deep/ img {
        width: 100%;
        height: 100%;
      }
    }
    .expen_item {
      width: 48%;
      background: #fff;
      box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.19);
      border-radius: 10px;
      margin-bottom: 20px;
      padding: 20px;
      cursor: pointer;
      flex-shrink: 0;
    }
    .expe_logo {
      width: 60px;
      height: 60px;
      margin-right: 20px;
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;
    }
    .expe_logo img {
      width: 100%;
    }
    .expe_intro {
      font-size: 16px;
      font-weight: 300;
      color: #5b5b5b;
      line-height: 24px;
    }
    .expe_heade {
      border-bottom: 1px solid #f3f3f3;
      padding-bottom: 15px;
    }
    .expe_foot {
      padding-top: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #5b5b5b;
    }
    .right_container {
      width: 902.43px;
      border-radius: 20.567px;
      box-sizing: border-box;
    }
    .help_tit {
      height: 55px;
      width: 100%;
      line-height: 55px;
      font-size: 14px;
      font-weight: 500;
      color: #5b5b5b;
      box-sizing: border-box;
      padding: 0 10px 0 30px;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: all 0.3s;
    }
    .help_tit:hover,
    .help_tit.active {
      border-color: #ff6716;
      color: #ff6716;
      background: #fff4ee;
    }
    </style>
    