<template>
  <div class="app-container">
    <el-tabs type="border-card">
      <!-- 号商管理 -->
      <el-tab-pane label="号商管理">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="号商状态">
            <el-select v-model="formInline.status" placeholder="号商状态">
              <el-option label="全部" value=""></el-option>
              <el-option label="上架中" :value="1"></el-option>
              <el-option label="下架中" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchFun"
              >查询</el-button
            >
          </el-form-item>
          <el-form-item style="float: right">
            <el-button type="success" @click="addNotice">新建号商</el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="initDate"
          border
          style="width: 100%"
          v-loading.fullscreen.lock="listLoading"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <el-table-column type="index" fixed :index="indexMethod">
          </el-table-column>
          <!-- <el-table-column prop="id" label="号商id" width="100"></el-table-column> -->
          <el-table-column
            prop="name"
            label="号商名称"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="content"
            label="号商描述"
            width="200"
          ></el-table-column>
          <el-table-column prop="gameList" label="关联游戏" width="200">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.gameList" :key="index">
                {{ item.title }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="号商状态" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 1" type="success"
                >上架中</el-tag
              >
              <el-tag v-if="scope.row.status == 0" type="danger">下架中</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="updated_at"
            label="更新时间"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="created_at"
            label="创建时间"
            width="180"
          ></el-table-column>

          <el-table-column fixed="right" label="操作" width="260">
            <template slot-scope="scope">
              <el-button
                @click="handleClick(scope.row.id)"
                type="primary"
                size="small"
                >详情/编辑</el-button
              >
              <el-button
                size="small"
                type="success"
                v-if="scope.row.status == 0"
                @click="handleNew(scope.row.id, 1)"
                >上架</el-button
              >
              <el-button
                size="small"
                type="danger"
                v-if="scope.row.status == 1"
                @click="handleNew(scope.row.id, 0)"
                >下架</el-button
              >
              <el-button
                v-if="scope.row.status == 0"
                type="danger"
                size="small"
                icon="el-icon-delete"
                circle
                @click="deletBanner(scope.row.id)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          style="margin-top: 20px"
          background
          layout="prev, pager, next,jumper"
          @current-change="pageFun"
          :total="totalPage"
        >
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getArticaleList,
  articaleHanleApi,
  articaleDeletApi,
} from '@/api/index';

export default {
  data() {
    return {
      page: 1, // 当前页码
      listLoading: false,
      totalPage: 10, // 总页码
      formInline: {
        keyword: '',
        status: '',
      },
      initDate: [],
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    /**
     * 初始化数据
     */
    fetchData() {
      this.listLoading = true;
      var json = {
        type: 1,
        page_index: this.page,
        page_size: 20,
        keyword: this.formInline.keyword,
        status: this.formInline.status,
      };
      getArticaleList(json).then((response) => {
        if (response.code == 200) {
          this.initDate = response.data.list;
          this.totalPage = response.data.pages * 10;
        } else {
          this.$message.error(response.msg);
        }
        this.listLoading = false;
      });
    },
    /**
     * 搜索
     */
    searchFun() {
      this.page = 1;
      this.fetchData();
    },

    /**
     * 公告状态
     */
    handleNew(id, num) {
      this.$confirm('您将改变此号商的上架状态, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        var json = {
          id: id,
          status: num,
        };
        articaleHanleApi(json).then((response) => {
          if (response.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.fetchData();
          }
          this.listLoading = false;
        });
      });
    },
    /**
     * 公告删除
     */
    deletBanner(id) {
      this.$confirm('您将删除此号商, 删除后不可恢复, 是否继续?', '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.listLoading = true;
        articaleDeletApi({
          id: id,
        }).then((response) => {
          if (response.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.fetchData();
          }
          this.listLoading = false;
        });
      });
    },
    /**
     * 分页
     */
    pageFun(val) {
      this.page = `${val}`;
      this.fetchData();
    },
    indexMethod(index) {
      return index + 1;
    },
    /**
     * 新建号商
     */
    addNotice() {
      this.$router.push({
        path: '/game/addMerchant',
      });
    },
    /**
     * 号商详情
     */
    handleClick(id) {
      this.$router.push({
        path: '/game/merchantDt',
        query: {
          id: id,
        },
      });
    },
  },
};
</script>

<style scoped>
.line {
  text-align: center;
}
</style>
