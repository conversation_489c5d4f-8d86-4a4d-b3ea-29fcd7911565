<template>
  <div>
    <!-- 区服的级联数据要展示成2行，增加一行服务器 -->
    <template
      v-if="
        currentItem &&
        currentItem.ename === 'gameAccountQufu' &&
        currentItem.selectType === 3
      "
    >
      <!-- 区服父级数据 -->
      <div :class="isExpand ? 'expand' : ''" class="spaceStart playSearch_wrap">
        <div class="playSearch_tit">{{ groupName }}</div>
        <div style="flex: 1">
          <div class="spaceStart item-box">
            <div class="spaceStart flexWrap item-list" style="flex: 1">
              <div
                :class="!parentValue ? 'active' : ''"
                class="playSearch_item"
                @click.stop="clearValue"
              >
                不限
              </div>
              <div
                v-for="(iteminner, index) in currentItem.pOptionList"
                :class="parentValue === iteminner ? 'active' : ''"
                :key="index"
                class="playSearch_item"
                @click.stop="handelParentClick(iteminner)"
              >
                {{ iteminner }}
              </div>
            </div>
            <div
              v-if="currentItem.pOptionList.length > 8"
              class="more_btn"
              @click="toggel"
            >
              {{ isExpand ? '收起' : '更多' }}
              <i
                :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                style="font-size: 12px; cursor: pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 服务器数据 -->
      <div
        :class="isSubExpand ? 'expand' : ''"
        class="spaceStart playSearch_wrap"
      >
        <div class="playSearch_tit">服务器</div>
        <div style="flex: 1">
          <div class="spaceStart item-box">
            <div class="spaceStart flexWrap item-list" style="flex: 1">
              <div
                :class="
                  !currentItem.selectValue ||
                  !currentItem.selectValue.find((v) => v.includes('|'))
                    ? 'active'
                    : ''
                "
                class="playSearch_item"
                @click.stop="clearValue('服务器')"
              >
                不限
              </div>
              <div
                v-for="(iteminner, index) in serverOptions"
                :class="
                  currentItem.selectValue.includes(
                    `${parentValue}|${iteminner}`
                  )
                    ? 'active'
                    : ''
                "
                :key="index"
                class="playSearch_item"
                @click.stop="choose(iteminner)"
              >
                {{ iteminner }}
              </div>
            </div>
            <div
              v-if="serverOptions.length > 8"
              class="more_btn"
              @click="toggelSub"
            >
              {{ isSubExpand ? '收起' : '更多' }}
              <i
                :class="isSubExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                style="font-size: 12px; cursor: pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 正常数据处理 -->
    <div
      v-else
      :class="isExpand ? 'expand' : ''"
      class="spaceStart playSearch_wrap"
    >
      <div class="playSearch_tit">{{ groupName }}
        <div class="searchTypeBox" @click="searchTypeClick(currentItem,currentItem.valueSearchType)" v-if="isType(list)">
              {{currentItem.valueSearchType=='must'?'全部都要有':'有一个就行'}}
              <IconFont
                :size="20"
                color="#ff720c"
                style="margin-left: -6px;"
                icon="other-server"
              />
            </div>
      </div>
      <div style="flex: 1">
        <!-- 父级数据 -->

        <div v-if="list.length > 1" class="spaceStart flexWrap">
          <!-- <div
          :class="!attrName ? 'active' : ''"
          class="playSearch_item_parent"
          @click.stop="clearParentValue"
          >
            不限
          </div> -->
          
          <div
            v-for="(iteminner, index) in list"
            :class="attrName === iteminner.name ? 'active' : ''"
            :key="index"
            class="playSearch_item_parent"
            @click.stop="handelAttrClick(iteminner.name)"
          >
            {{ iteminner.name }}
          </div>
        </div>

        <!-- 数据 -->
        <div class="spaceStart item-box">
          <div class="spaceStart flexWrap item-list" style="flex: 1">
      
            <div
              v-if="currentItem.hasClear"
              :class="
                !currentItem.selectValue || !currentItem.selectValue.length
                  ? 'active'
                  : ''
              "
              class="playSearch_item"
              @click.stop="clearValue"
            >
              不限
            </div>
            <div
              v-for="(iteminner, index) in currentItem.optionList"
              :class="
                currentItem.selectValue.includes(iteminner) ? 'active' : ''
              "
              :key="index"
              class="playSearch_item"
              @click.stop="choose(iteminner)"
            >
              {{ iteminner }}
            </div>
          </div>
          <div
            v-if="currentItem.optionList && currentItem.optionList.length > 8"
            class="more_btn"
            @click="toggel"
          >
            {{ isExpand ? '收起' : '更多' }}
            <i
              :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              style="font-size: 12px; cursor: pointer"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    groupName: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      isExpand: false,
      isSubExpand: false,

      parentValue: '', // 大区父级值

      attrName: '', // 属性筛选对应名称
    };
  },
  computed: {
    currentItem() {
      const obj = this.list.filter((e) => e.name === this.attrName)[0] || {};
      return obj;
    },
    // 服务器数据
    serverOptions() {
      const { optionList } = this.currentItem;
      const options = optionList[this.parentValue] || [];
      return options;
    },
  },
  mounted() {
    // if(this.list.length===1){
    const { name } = this.list[0];
    this.attrName = name; // 默认选中第一项
    // }
  },
  methods: {
    isType(arr){
      let flag=true
      arr.forEach(item=>{
        if(item.selectType!=2){
          flag=false
        }
      })  
      
      return flag
    },
    getSearchType(list){
      return list[0].valueSearchType
    },
    searchTypeClick(list,v){
      console.log(v,22222)
      this.$emit('searchTypeClick', list,v=='must'?'should':'must');
    },
    handelParentClick(v) {
      this.parentValue = v;
      this.choose(v);
    },
    handelAttrClick(v) {
      this.attrName = v;
    },
    toggel() {
      this.isExpand = !this.isExpand;
    },
    toggelSub() {
      this.isSubExpand = !this.isSubExpand;
    },
    clearParentValue() {},
    clearValue(str) {
      if (str === '服务器') {
        // 不能清除区服的数据
        this.$emit('change', [this.parentValue], this.currentItem);
        return;
      }

      this.parentValue = '';

      if (this.currentItem.selectValue && this.currentItem.selectValue.length) {
        this.$emit('change', [], this.currentItem);
      }
    },
    // 数据点击
    choose(value) {
      const selectValue = this.currentItem.selectValue || [];
      let v = JSON.parse(JSON.stringify(selectValue));

      const { filterType, selectType, ename } = this.currentItem;

      // 区服的数据如果子级点击要把父层带上
      if (
        ename === 'gameAccountQufu' &&
        selectType === 3 &&
        this.parentValue !== value
      ) {
        value = `${this.parentValue}|${value}`;
      }

      // 单选处理
      if (filterType !== 1) {
        if (selectValue.includes(value)) {
          v = [];
        } else {
          v = [value];
        }
      } else {
        // 多选处理 filterType=1 表示多选
        if (selectValue.includes(value)) {
          v = selectValue.filter((e) => e !== value);
        } else {
          v.push(value);
        }
      }
      this.$emit('change', v, this.currentItem);
    },
  },
};
</script>
<style lang="scss" scoped>
.flexWrap {
  flex-wrap: wrap;
}
.playSearch_wrap {
  padding: 8px 0;
  align-items: baseline;
  border-bottom: dashed 1px #dcdcdc;

  .item-list {
    height: 36px;
    overflow: hidden;
  }

  &.expand {
    .item-list {
      height: fit-content;
      overflow: hidden;
    }
  }
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-left: 10px !important;
  margin-top: 11px !important;
}
.playSearch_item_parent {
  cursor: pointer;
  color: #909090;
  margin-right: 12px;
  transition: all 0.3s;
  margin-top: 8px;
  flex-shrink: 0;

  background: #f7f7f7;
  padding: 0px 8px;
  border-radius: 16px;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  line-height: 26px !important;
  height: 26px !important;
  border: 1px solid #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    border: 1px solid #ff6716;
    color: #ff6716;
    background: #fff;
  }
}
.playSearch_item {
  cursor: pointer;
  padding: 0px 8px;
  background: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 16px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  color: #909090;
  margin-right: 12px;
  transition: all 0.3s;
  margin-top: 8px;
  line-height: 30px !important;
  height: 30px !important;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  &:hover {
    background: #fff4ee;
    color: #ff6716;
  }
}
.playSearch_item.active {
  color: #ff6716;
  background: #fff4ee;
  // border: 1px solid #ff4f11;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 130px;
  font-size: 16px;
  color: #2d2d2d;
  font-weight: 400;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}

.more_btn {
  width: 60px;
  cursor: pointer;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  text-align: center;
  border-radius: 60px;
  padding: 6px 0;
  font-size: 12px;

  &:hover {
    background: #fcfcfc;
  }
}

.item-box {
  align-items: baseline;
}
.searchTypeBox{
  font-size: 14px;
  cursor: pointer;
  color: #ff720c;
  // margin-right: 10px;
  margin-top: 16px;
  // font-family: PingFang Sc;
  // font-weight: 600
}
</style>
