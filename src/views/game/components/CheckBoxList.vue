<template>
  <div>
    <!-- 区服的数据要展示成2行，增加一行服务器 -->
    <template v-if="item.name === '区服'">
      <!-- 区服父级数据 -->
      <div :class="isExpand ? 'expand' : ''" class="spaceStart playSearch_wrap">
        <div class="playSearch_tit">{{ item.name }}</div>
        <div style="flex: 1">
          <div class="spaceStart item-box">
            <div class="spaceStart flexWrap item-list" style="flex: 1">
              <div
                :class="!parentValue ? 'active' : ''"
                class="playSearch_item"
                @click.stop="clearValue"
              >
                不限
              </div>
              <div
                v-for="(iteminner, index) in item.pOptionList"
                :class="parentValue === iteminner ? 'active' : ''"
                :key="index"
                class="playSearch_item"
                @click.stop="handelParentClick(iteminner)"
              >
                {{ iteminner }}
              </div>
            </div>
            <div v-if="item.pOptionList.length > 8" class="more_btn" @click="toggel">
              {{ isExpand ? '收起' : '更多' }}
              <i
                :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                style="font-size: 12px; cursor: pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 服务器数据 -->
      <div :class="isSubExpand ? 'expand' : ''" class="spaceStart playSearch_wrap">
        <div class="playSearch_tit">服务器</div>
        <div style="flex: 1">
          <div class="spaceStart item-box">
            <div class="spaceStart flexWrap item-list" style="flex: 1">
              <div
                :class="
                  !item.selectValue || !item.selectValue.find(v=>v.includes('|')) ? 'active' : ''
                "
                class="playSearch_item"
                @click.stop="clearValue('服务器')"
              >
                不限
              </div>
              <div
                v-for="(iteminner, index) in options"
                :class="item.selectValue.includes(`${parentValue}|${iteminner}`) ? 'active' : ''"
                :key="index"
                class="playSearch_item"
                @click.stop="choose(iteminner)"
              >
                {{ iteminner }}
              </div>
            </div>
            <div v-if="options.length > 8" class="more_btn" @click="toggelSub">
              {{ isSubExpand ? '收起' : '更多' }}
              <i
                :class="isSubExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                style="font-size: 12px; cursor: pointer"
              ></i>
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 正常数据处理 -->
    <div
      v-else
      :class="isExpand ? 'expand' : ''"
      class="spaceStart playSearch_wrap"
    >
      <div class="playSearch_tit">{{ item.name }}</div>
      <div style="flex: 1">
        <!-- 父级数据 -->
        <div v-if="hasParent" class="spaceStart flexWrap">
          <div
            v-if="item.hasClear"
            :class="!parentValue ? 'active' : ''"
            class="playSearch_item_parent"
            @click.stop="clearValue"
          >
            不限
          </div>
          <div
            v-for="(iteminner, index) in item.pOptionList"
            :class="parentValue === iteminner ? 'active' : ''"
            :key="index"
            class="playSearch_item_parent"
            @click.stop="handelParentClick(iteminner)"
          >
            {{ iteminner }}
          </div>
        </div>

        <!-- 数据 -->
        <div v-if="options.length" class="spaceStart item-box">
          <div class="spaceStart flexWrap item-list" style="flex: 1">
            <div
              v-if="item.hasClear && !hasParent"
              :class="
                !item.selectValue || !item.selectValue.length ? 'active' : ''
              "
              class="playSearch_item"
              @click.stop="clearValue"
            >
              不限
            </div>
            <div
              v-for="(iteminner, index) in options"
              :class="item.selectValue.includes(iteminner) ? 'active' : ''"
              :key="index"
              class="playSearch_item"
              @click.stop="choose(iteminner)"
            >
              {{ iteminner }}
            </div>
          </div>
          <div v-if="options.length > 8" class="more_btn" @click="toggel">
            {{ isExpand ? '收起' : '更多' }}
            <i
              :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              style="font-size: 12px; cursor: pointer"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isExpand: false,
      isSubExpand:false,

      parentValue: '',
    };
  },
  computed: {
    // 是否级联数据
    hasParent() {
      return this.item.selectType === 3;
    },
    // 选项展示
    options() {
      const { optionList } = this.item;
      let options = optionList;

      // 级联数据，根据父级选项返回对应的自己列表
      if (this.hasParent) {
        options = optionList[this.parentValue] || [];
      }
      return options;
    },
  },
  mounted() {
    const {pOptionList,name} = this.item
    if (pOptionList && name!=='区服') {
      this.parentValue = pOptionList[0];
    }
  },
  methods: {
    handelParentClick(v) {
      this.parentValue = v;

      if(this.item.name==='区服'){ // 大区点击也需要进行查询
        this.choose(v)
      }
    },
    toggel() {
      this.isExpand = !this.isExpand;
    },
    toggelSub() {
      this.isSubExpand = !this.isSubExpand;
    },
    clearValue(str) {
      if(str==='服务器'){ // 不能清除区服的数据
        this.$emit('change', [this.parentValue]);
        return
      }

      this.parentValue = '';
      
      if(this.item.selectValue && this.item.selectValue.length){
        this.$emit('change', []);
      }
    },
    // 数据点击
    choose(value) {
      const selectValue = this.item.selectValue || [];
      let v = JSON.parse(JSON.stringify(selectValue));

      const {name,filterType} = this.item

      // 区服的数据如果子级点击要把父层带上
      if(name==='区服' && this.parentValue!==value){
        value=`${this.parentValue}|${value}`
      }

      // 单选处理
      if (filterType !== 1) {
        if (selectValue.includes(value)) {
          v = [];
        } else {
          v = [value];
        }
      } else { // 多选处理 filterType=1 表示多选
        if (selectValue.includes(value)) {
          v = selectValue.filter((e) => e !== value);
        } else {
          v.push(value);
        }
      }
      this.$emit('change', v);
    },
  },
};
</script>
<style lang="scss" scoped>
.flexWrap {
  flex-wrap: wrap;
}
.playSearch_wrap {
  padding: 8px 0;
  align-items: baseline;
  border-bottom: dashed 1px #dcdcdc;

  .item-list {
    height: 36px;
    overflow: hidden;
  }

  &.expand {
    .item-list {
      height: fit-content;
      overflow: hidden;
    }
  }
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-left: 10px !important;
  margin-top: 11px !important;
}
.playSearch_item_parent {
  cursor: pointer;
  color: #909090;
  margin-right: 12px;
  transition: all 0.3s;
  margin-top: 8px;
  flex-shrink: 0;

  background: #f7f7f7;
  padding: 0px 8px;
  border-radius: 16px;
  font-size: 12px;
  line-height: 26px !important;
  height: 26px !important;
  border: 1px solid #f7f7f7;

  &.active {
    border: 1px solid #ff6716;
    color: #ff6716;
    background: #fff;
  }
}
.playSearch_item {
  cursor: pointer;
  padding: 0px 8px;
  background: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 8px;
  font-size: 14px;
  color: #909090;
  margin-right: 12px;
  transition: all 0.3s;
  margin-top: 8px;
  line-height: 30px !important;
  height: 30px !important;
  flex-shrink: 0;

  &:hover {
    background: #fff4ee;
    color: #ff6716;
  }
}
.playSearch_item.active {
  color: #ff6716;
  background: #fff4ee;
  // border: 1px solid #ff4f11;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 130px;
  font-size: 14px;
  color: #222222;
  font-weight: 600;
}

.more_btn {
  width: 60px;
  cursor: pointer;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  text-align: center;
  border-radius: 60px;
  padding: 6px 0;
  font-size: 12px;

  &:hover {
    background: #fcfcfc;
  }
}

.item-box {
  align-items: baseline;
}
</style>
