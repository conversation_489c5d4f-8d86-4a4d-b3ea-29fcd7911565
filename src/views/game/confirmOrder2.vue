<template>
  <div
    v-loading="loading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="pdTopBottom">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>确认订单</el-breadcrumb-item>
      </el-breadcrumb>

      <div class="page_comStyle">
        <div class="main_tit">确认订单信息</div>
        <div>
          <div class="table_self">
            <div class="spaceBetween table_header">
              <div class="table_one">商品信息</div>
              <div class="table_two">单价</div>
              <div class="table_three">编号</div>
            </div>
            <div class="spaceBetween table_body">
              <div class="table_one spaceStart">
                <div class="goodsOrder_pic">
                  <img
                    :src="gameDt.productPic"
                    style="width: 100%; height: 100%"
                  />
                </div>
                <div class="goodsOrder_text">
                  <div class="text_linTwo">
                    {{ gameDt.productSubTitle | tedianFilter }}
                  </div>
                  <!-- <div class="goodsOrder_subTit">
                    <span
                      v-for="(itemS, indexS) in gameDt.details"
                      :key="indexS"
                      >{{ itemS.lable }}:{{ itemS.value }}；</span
                    >
                  </div> -->
                </div>
              </div>
              <div class="table_two">{{ gameDt.price }}</div>
              <div class="table_three">
                {{ gameDt.productSn }}
              </div>
            </div>
          </div>

          <div v-if="baseBPList && baseBPList.length" class="table_self">
            <div class="table_header">
              <span class="color_red">基础 </span>包赔服务<span
                >（单选，97%人已选择）</span
              >
            </div>
            <div class="table_body spaceStart">
              <div
                v-for="(item, index) in baseBPList"
                :key="index"
                :class="baojiaIndex == index ? 'active' : ''"
                class="compensate_wrap"
                @click="chooseBao(index, item)"
              >
                <div class="spaceBetween compensate_tit">
                  <div class="spaceBetween bp_note_top">
                    <div>{{ item.value }}</div>
                    <div>
                      最高赔付<span class="payPrice"
                        >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</span
                      >
                    </div>
                  </div>
                  <div class="spaceStart">
                    <i
                      class="iconfontnew icon-icon-question"
                      @click="doQuestion(index, item, $event)"
                    ></i>
                  </div>
                </div>
                <div class="spaceBetween itemstart">
                  <div class="bp_note">
                    {{ item.ruler }}
                  </div>
                  <div class="spaceStart">
                    <div>¥{{ getPrice2(item) }}</div>
                    <div
                      v-if="baojiaIndex == index"
                      class="iconfontnew icon-lijiqueren active_red"
                    ></div>
                    <div v-else class="noactive_red"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="addBPList && addBPList.length" class="table_self">
            <div class="table_header">
              <span class="color_blue">增值 </span>包赔服务<span
                ><span>（多选，95%人已选择）</span></span
              >
            </div>
            <div class="table_body spaceStart">
              <div
                v-for="(item, index) in addBPList"
                v-if="comparePrice(item)"
                :key="index"
                :class="hasAdd(item) ? 'active2' : ''"
                class="compensate_wrap"
                @click="chooseBaoAdd(item)"
              >
                <div class="spaceBetween compensate_tit">
                  <div class="spaceBetween bp_note_top">
                    <div>{{ item.value }}</div>
                    <div>
                      最高赔付
                      <span v-if="item.topRepay == -1" class="payPrice2"
                        >无上限</span
                      >
                      <span v-else class="payPrice2"
                        >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</span
                      >
                    </div>
                  </div>
                  <div class="spaceStart">
                    <i
                      class="iconfontnew icon-icon-question"
                      @click="doQuestionAdd(index, item, $event)"
                    ></i>
                  </div>
                </div>
                <div class="spaceBetween itemstart">
                  <div class="bp_note">
                    {{ item.ruler }}
                  </div>
                  <div class="spaceStart">
                    <div>¥{{ getPrice2(item) }}</div>
                    <div
                      v-if="hasAdd(item)"
                      class="iconfontnew icon-huiyishiqueren_huabanfuben active_blue"
                    ></div>
                    <div v-else class="noactive_blue"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="type == 1" class="dingjin_pay">
            订单总价大于200000元，因账号金额限制，无法支付全款，将按定金金额200000元先行支付。
          </div>
        </div>
        <div :class="priceDetailShow ? 'active' : ''" class="footer_box">
          <div
            :class="priceDetailShow ? 'confirm_footer_show' : ''"
            class="confirm_footer"
          >
            <div class="title">订单明细</div>
            <div class="content">
              <div class="spaceBetween confirm_footer_item">
                <div class="textRight">号价：</div>
                <div class="conOrder_right">¥ {{ gameDt.price }}</div>
              </div>
              <div v-if="baojiaItem" class="spaceBetween confirm_footer_item">
                <div class="textRight">{{ baojiaItem.value }}</div>
                <div class="conOrder_right">
                  ¥ {{ getBaopeiPrice(baojiaItem) }}
                </div>
              </div>
              <div
                v-for="(item, index) in baojiaAddList"
                :key="index"
                class="spaceBetween confirm_footer_item"
              >
                <div class="textRight">{{ item.value }}</div>
                <div class="conOrder_right">¥ {{ getBaopeiPrice(item) }}</div>
              </div>
              <div class="spaceBetween confirm_footer_item">
                <div class="textRight">总价：</div>
                <div class="conOrder_right">¥ {{ totalPrice }}</div>
              </div>
            </div>
          </div>
          <div class="spaceEnd">
            <div class="agree_box">
              <div class="spaceStart aggree_pay">
                <el-checkbox v-model="checked"></el-checkbox>
                <div class="cursor">
                  <span @click="changeChecked">我同意阅读并愿意遵守</span>
                  <router-link to="/helpCenter?id=323" style="color: #ff6716"
                    >《买家交易规则》</router-link
                  >
                </div>
              </div>
              <div class="spaceEnd">
                <div class="textRight">共1件 合计：</div>
                <div class="payPrice">¥ {{ totalPrice }}</div>
              </div>
            </div>
            <div class="ddDetail spaceCenter" @click="toggleDetail">
              订单详情
            </div>
            <div class="plDt_btn spaceCenter" @click="payChoose">提交订单</div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      class="baopeiDialog"
      title="包赔服务"
      width="680px"
    >
      <div class="baopei_box">
        <div>
          <div><i class="iconfontnew icon-qiandun-32"></i>包赔规则</div>
          <div class="baopei_content" v-html="dialogVisibleItem.detail"></div>
        </div>
        <div>
          <div><i class="iconfontnew icon-tanhao"></i>注意事项</div>
          <div class="baopei_content" v-html="dialogVisibleItem.note"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBaopei">选择此赔付服务</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisibleAdd"
      class="baopeiDialog"
      title="包赔服务"
      width="680px"
    >
      <div class="baopei_box">
        <div>
          <div><i class="iconfontnew icon-qiandun-32"></i>包赔规则</div>
          <div
            class="baopei_content"
            v-html="dialogVisibleAddItem.detail"
          ></div>
        </div>
        <div>
          <div><i class="iconfontnew icon-tanhao"></i>注意事项</div>
          <div class="baopei_content" v-html="dialogVisibleAddItem.note"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBaopeiAdd"
          >选择此赔付服务</el-button
        >
      </span>
    </el-dialog>

    <!-- 责任须知 -->
    <!-- <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible.sync="popupSw"
      title="责任须知"
      width="50%"
      center
    >
      <div class="zeren_needKnow">
        <p>普通包赔：号价10%，账号如发生找回未追回，赔付100%号价。</p>
        <p>
          双倍包赔：号价20%，账号如发生找回未追回，赔付200%号价。因为普通包赔只赔付号款，双倍包赔是针对想对账号进行大额投入的买家（如购买天赏，大额充值，代练代肝）特地推出的，可以保障您后续的心血投入。
        </p>
        <p>如不选择包赔服务，买家请知晓并同意以下风险：</p>
        <p>
          1.账号如发生找回，因未签订合同，涉及个人隐私，平台方无法提供卖家的详细信息，但会指导帮助您该如何维权。
        </p>
        <p>
          2.如有期望在平台二次出售该账号，新买家若有购买包赔意向，账号如发生找回，将由您承担责任。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="popupTime > 0" type="success" disabled>
          我知道了{{ popupTime > 0 ? '（' + popupTime + '秒）' : '' }}
        </el-button>
        <el-button v-else type="success" @click="popupHide">我知道了</el-button>
      </span>
    </el-dialog> -->

    <safeNews />
    <footerKk />
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import util from '@/utils/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

import { getProductCategory } from '@/api/search.js';
import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import {
  generateKKConfirmOrderMyAssess,
  generateKKOrderMyAssess2,
} from '@/api/myAssess.js';
import {
  generateKKConfirmOrder,
  generateKKOrder2,
} from '@/api/confirmOrder.js';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      dialogVisibleAdd: false,
      dialogVisible: false,
      // pmsSkuStockList: [],
      popupTime: 3,
      popupTimer: null,
      // popupSw: false, // 协议弹框
      index: 1,
      loading: false,
      checked: false, // 统一协议
      productCategoryId: '',
      productId: '',
      gameDt: {}, // 商品数据
      totalPrice: 0, // 总价
      baojiaIndex: null, // 保价选择的下标
      baojiaItem: null,
      // baojiaAddIndex: null,
      ensure: 1, // 是否报价,1保价 0不保价
      ensure_price: 0, // 保价的金额
      ensure_price_add: 0,
      type: 2, // 1 预定 2 全款
      from: '',
      negoId: '',
      priceDetailShow: false,
      dialogVisibleIndex: 0,
      dialogVisibleItem: '',
      // dialogVisibleAddIndex: 0,
      dialogVisibleAddItem: '',
      baseBPList: [],
      addBPList: [],
      baojiaAddList: [],
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  mounted() {
    if (isLogin()) {
      this.$store.dispatch('getUserInfoStore');
    }
    this.productCategoryId = this.$route.query.productCategoryId;
    this.from = this.$route.query.from;
    if (this.from === 'myAssess') {
      this.negoId = this.$route.query.negoId;
      this.getGenerateKKConfirmOrderMyAssess();
    } else if (this.$route.query.productId) {
      this.productId = this.$route.query.productId;
      this.initGame();
    }
  },
  methods: {
    hasAdd(item) {
      const findIt = this.baojiaAddList.find((ele) => ele.id === item.id);
      return !!findIt;
    },
    addBaopei() {
      this.dialogVisible = false;
      if (this.dialogVisibleIndex == this.baojiaIndex) {
        return;
      }
      this.chooseBao(this.dialogVisibleIndex, this.dialogVisibleItem);
    },
    doQuestion(index, item, event) {
      event.stopPropagation();
      this.dialogVisibleIndex = index;
      this.dialogVisibleItem = item;
      this.dialogVisible = true;
    },
    addBaopeiAdd() {
      this.dialogVisibleAdd = false;
      const findIt = this.baojiaAddList.find(
        (ele) => ele.id === this.dialogVisibleAddItem.id,
      );
      if (findIt) {
        return;
      }
      this.chooseBaoAdd(this.dialogVisibleAddItem);
    },
    doQuestionAdd(index, item, event) {
      event.stopPropagation();
      // this.dialogVisibleAddIndex = index;
      this.dialogVisibleAddItem = item;
      this.dialogVisibleAdd = true;
    },
    toggleDetail() {
      this.priceDetailShow = !this.priceDetailShow;
    },
    getGenerateKKConfirmOrderMyAssess() {
      generateKKConfirmOrderMyAssess({ negoId: this.negoId }).then((res) => {
        if (res.code == 200) {
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    changeChecked() {
      this.checked = !this.checked;
    },
    getBaopeiPrice(ele) {
      let ratio = ele.price;
      let price = util.times(this.gameDt.price, ratio);
      return price;
    },
    getPrecent(item) {
      return util.times(item.topRepay, 100, 0);
    },
    getPrice(item) {
      let ratio = item.topRepay;
      return this.accMul(this.gameDt.price, ratio);
    },
    getPrice2(item) {
      return util.times(this.gameDt.price, item.price);
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    comparePrice(item) {
      const result = util.comparedTo(this.gameDt.price, item.lowprice);
      if (result === -1) {
        return false;
      } else {
        return true;
      }
    },
    // 责任须知
    // popupShow() {
    //   this.popupSw = true;
    //   clearInterval(this.popupTimer);
    //   this.popupTimer = setInterval(() => {
    //     if (this.popupTime <= 0) {
    //       clearInterval(this.popupTimer);
    //     } else {
    //       this.popupTime--;
    //     }
    //   }, 1000);
    // },
    popupHide() {
      this.popupTime = 0;
      this.popupSw = false;
      clearInterval(this.popupTimer);
    },
    // 创建订单
    payChoose() {
      if (!this.checked) {
        this.$message.error('请勾选买家交易规则!');
        return;
      }
      if (this.from === 'myAssess') {
        this.payAssess();
      } else {
        this.payNormal();
      }
    },
    payAssess() {
      this.loading = true;
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));
      let data = {
        buyType: baopeiTypes.length ? 1 : 0,
        negoId: this.negoId,
        baopeiTypes: baopeiTypes.join(','),
        sourceType: 3,
      };
      generateKKOrderMyAssess2(data).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.$confirm(
            `支付金额 <span style="color:#ff6700;">¥${this.totalPrice}</span>`,
            '立即支付',
            {
              closeOnClickModal: false,
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'success',
              dangerouslyUseHTMLString: true,
            },
          )
            .then(() => {
              this.$router.push({
                path: '/payOrder2?orderId=' + res.data.id,
              });
            })
            .catch(() => {
              this.$router.push({
                path: '/account/orderDetail?orderId=' + res.data.id,
              });
            });
        }
      });
    },
    payNormal() {
      this.loading = true;
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));
      let data = {
        buyType: baopeiTypes.length ? 1 : 0,
        baopeiTypes,
        productId: this.productId,
        sourceType: 3,
      };
      generateKKOrder2(data).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.$confirm(
            `支付金额<span style="color:#ff6700;">¥${this.totalPrice}</span>`,
            '立即支付',
            {
              closeOnClickModal: false,
              confirmButtonText: '去支付',
              cancelButtonText: '取消',
              type: 'success',
              dangerouslyUseHTMLString: true,
            },
          )
            .then(() => {
              this.$router.push({
                path: '/payOrder2?orderId=' + res.data.id,
              });
            })
            .catch(() => {
              this.$router.push({
                path: '/account/orderDetail?orderId=' + res.data.id,
              });
            });
        }
      });
    },
    formatRes(res) {
      this.gameDt = res.data.cartPromotionItemList[0];
    },
    /**
     * 初始化-商品数据
     */
    getBaopei() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code == 200) {
          if (res.data.custom) {
            let custom = JSON.parse(res.data.custom);
            this.baseBPList = [];
            this.addBPList = [];
            if (custom.baopei && custom.baopei.length) {
              custom.baopei.forEach((ele) => {
                if (ele.type == 'BASIC_COMPENSATION') {
                  this.baseBPList.push(ele);
                }
                if (ele.type == 'VALUE_ADD_COMPENSATION') {
                  this.addBPList.push(ele);
                }
              });
            }
            let item = this.baseBPList[0];
            if (item) {
              this.chooseBao(0, item);
            } else {
              this.ensure_price_add = 0;
              this.ensure_price = 0;
              this.ensure = 0;
            }
            this.computePrice();
            // this.popupShow();
          }
        }
      });
    },
    initGame() {
      generateKKConfirmOrder({
        buyType: 0, // 随便传个值
        productId: this.productId,
      }).then((res) => {
        if (res.code == 200) {
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    // 总价计算
    computePrice() {
      this.totalPrice = util.add(this.gameDt.price, this.ensure_price || 0);
      this.totalPrice = util.add(this.totalPrice, this.ensure_price_add || 0);
      if (this.totalPrice > 200000) {
        this.type = 1;
      } else {
        this.type = 2;
      }
    },
    chooseBaoAdd(item) {
      if (this.baojiaIndex == null) {
        this.$message.error('须选择基础包赔后才可加购增值包赔');
        return;
      }
      const findIndex = this.baojiaAddList.findIndex(
        (ele) => ele.id === item.id,
      );
      if (findIndex !== -1) {
        this.baojiaAddList.splice(findIndex, 1);
      } else {
        this.baojiaAddList.push(item);
      }
      this.ensure_price_add = 0;

      this.baojiaAddList.forEach((ele) => {
        let ratio = ele.price;
        let price = util.times(this.gameDt.price, ratio);
        this.ensure_price_add = util.add(this.ensure_price_add, price);
      });
      this.computePrice();
    },
    // 保价选择
    chooseBao(num, item) {
      if (this.baojiaIndex == num) {
        this.baojiaIndex = null;
        this.baojiaItem = null;
        this.ensure_price = 0;
        this.ensure_price_add = 0;
        this.ensure = 0;
        this.baojiaAddList = [];
      } else {
        this.baojiaIndex = num;
        this.baojiaItem = item;
        let ratio = item.price;
        this.ensure = parseInt(num) + 1;
        this.ensure_price = this.accMul(this.gameDt.price, ratio);
      }
      this.computePrice();
    },
    /**
     * 乘法函数，用来得到精确的乘法结果
     * 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
     * 调用：accMul(arg1,arg2)
     * 返回值：arg1乘以 arg2的精确结果
     **/
    accMul(arg1, arg2) {
      return util.times(arg1, arg2);
    },
  },
};
</script>

<style lang="scss" scoped>
.itemstart {
  align-items: start;
}
.active_red {
  color: #e60f0f;
  font-size: 16px;
  margin-left: 3px;
  line-height: 16px;
}
.noactive_red {
  height: 16px;
  width: 16px;
  border: 1px solid #ccc;
  border-radius: 50%;
  margin-left: 3px;
}
.active_blue {
  color: #0082ff;
  font-size: 20px;
  line-height: 16px;
  height: 16px;
  width: 16px;
  margin-right: 3px;
  margin-left: 3px;
}
.noactive_blue {
  border: 1px solid #ccc;
  height: 15px;
  width: 15px;
  margin: 0 2px 0px 5px;
}
.color_blue {
  font-weight: 700;
  color: #0082ff;
}
.color_red {
  font-weight: 700;
  color: #e60f0f;
}
.baopeiDialog {
  /deep/ .el-dialog__header {
    background: #fe5a1e;
    .el-dialog__title {
      color: #fff;
    }
    .el-icon-close {
      color: #fff;
    }
    .el-icon-close:hover {
      color: #fff;
    }
    text-align: center;
  }
  /deep/ .el-dialog__footer {
    text-align: center;
  }
  .baopei_box {
    font-size: 18px;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 20px 10px;
  }
  .baopei_content {
    padding: 10px 0;
    line-height: 28px;
  }
  .icon-qiandun-32,
  .icon-tanhao {
    margin-right: 6px;
    position: relative;
    top: 2px;
  }
}

.icon-icon-question {
  font-size: 18px;
  margin-left: 5px;
}
.plDt_btn {
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.page_comStyle {
  margin-bottom: 60px;
  padding-top: 30px;
  padding-bottom: 100px;
  position: relative;
}
.ddDetail {
  border-radius: initial;
  width: 148px;
  background: #000;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  border: 0;
}
.ddDetail:hover {
  border: 0;
}
.main_tit {
  font-size: 20px;
  font-weight: 500;
  color: #222222;
}
.table_self {
  border-radius: 6px;
  border: 1px solid #eeeeee;
  margin-top: 20px;
}
.table_header {
  box-sizing: border-box;
  padding: 14px 20px;
  background: #fbfbfb;
  font-size: 16px;
  color: #222222;
  border-bottom: 1px solid #eeeeee;
}
.table_body {
  box-sizing: border-box;
  padding: 0 0 20px;
  font-size: 14px;
  color: #222222;
  flex-wrap: wrap;
}
.table_one {
  flex-shrink: 1;
  width: 700px;
}
.table_two {
  flex-shrink: 1;
  width: 150px;
  text-align: center;
}
.table_three {
  flex-shrink: 1;
  width: 150px;
  text-align: center;
}
.goodsOrder_pic {
  width: 147px;
  height: 82px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 20px;
  flex-shrink: 0;
}
.goodsOrder_text {
  font-size: 16px;
  color: #222222;
  line-height: 22px;
}
.goodsOrder_subTit {
  font-size: 14px;
  color: #909090;
  padding-top: 8px;
}
.baojiaPrice {
  font-weight: 500;
  color: #ff6700;
}
.compensate_wrap {
  width: 32%;
  min-height: 106px;
  box-sizing: border-box;
  padding: 15px;
  border: 1px solid #eeeeee;
  border-radius: 6px;
  font-size: 13px;
  color: #909090;
  transition: all 0.3s;
  cursor: pointer;
  margin-top: 20px;
  margin-left: 10px;
}
.compensate_wrap:first-child {
  // margin-left: 10px;
}
.compensate_wrap.active {
  background: #ffebeb;
  border: 1px solid #d4d4d4;
}
.compensate_wrap.active2 {
  background: #eff7ff;
  border: 1px solid #0082ff;
}

.compensate_tit {
  font-size: 14px;
  color: #222222;
  padding-bottom: 18px;
}
.aggree_pay {
  font-size: 14px;
  color: #909090;
}
.agree_box {
  padding: 0 10px;
}
.el-checkbox {
  margin-right: 10px;
}
.footer_box {
  margin-top: 30px;
  position: absolute;
  width: 650px;
  right: 0;
  bottom: 10px;
  padding: 20px;
  background: #fff;
}
.footer_box.active {
  box-shadow: 0 0 10px 0 #999;
}
.footer_box {
  .confirm_footer_show {
    max-height: 600px;
  }
}

.confirm_footer {
  font-size: 14px;
  color: #909090;
  bottom: 41px;
  width: 100%;
  border-radius: 5px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  .title {
    font-weight: 700;
    padding-left: 10px;
    margin-bottom: 20px;
    font-size: 18px;
    position: relative;
    padding-left: 15px;
  }
  .title::before {
    background-color: #fe5a1e;
    content: '';
    height: 15px;
    left: 0;
    position: absolute;
    top: 2px;
    width: 6px;
  }
  .content {
    background: #f3f3f3;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    color: #000;
  }
}
.confirm_footer_item {
  padding-bottom: 18px;
}
.textRight {
  text-align: right;
}
.conOrder_right {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}
.payPrice {
  font-size: 14px;
  font-weight: 600;
  color: #f7423f;
}
.payPrice2 {
  font-size: 14px;
  font-weight: 600;
  color: #0082ff;
}
.bp_note_top {
  flex: 1;
}
.bp_note {
  max-width: 280px;
}
.dingjin_pay {
  font-size: 20px;
  font-weight: 600;
  color: #f7423f;
  padding: 10px 30px 30px 20px;
}
.zeren_needKnow {
  font-size: 14px;
  color: #333;
  line-height: 26px;
}
</style>
