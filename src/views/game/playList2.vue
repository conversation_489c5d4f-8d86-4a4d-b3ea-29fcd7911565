<template>
  <div
    ref="mianscroll"
    class="dark_container scrollBody listContentBk"
    style="width: 100%; height: 100vh; overflow-y: scroll;"
  >
    <headerKk :active-index="index" @changeproductCategoryId="changeFlagList" />
    <div :style="backgroundStr" class="paTopCom" style="margin-bottom: 80px">
      <div class="safe_width">
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          class="pdTopBottom"
          style="padding: 34.28px 0 20.567px 0px"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/gameList' }"
            >全部游戏</el-breadcrumb-item
          >
          <el-breadcrumb-item>{{ jsonGame.name }}</el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 筛选条件区域 -->
        <div ref="offsetHeightNeed" class="page_comStyle" style="">
          <!-- 搜索选项 -->
          <el-input
            v-model="keyword2"
            placeholder="请输入内容"
            prefix-icon="el-icon-search"
            style="width: 300px; margin-bottom: 20px"
            clearable
            @input="handelOptsSearch"
          />
          <div
            v-if="keyword2 && (!optsSearchResult || !optsSearchResult.length)"
          >
            暂无符合条件的筛选项
          </div>
          <div>
            <span
              v-for="item in optsSearchResult"
              :key="item.value"
              :class="getOptIsCheck(item) ? 'active' : ''"
              class="opt-item"
              @click="handelOptClick(item)"
              >{{ item.value }}</span
            >
          </div>

          <CheckBoxList
            v-for="(item, index) in newCheckBoxAttributeList"
            :key="item.name"
            :item="item"
            :index="index"
            @change="(v) => handelCheckboxAttrChange(index, v)"
          />
          <div
            v-if="checkBoxAttributeList.length > 8"
            class="more_btn"
            style="margin: 10px auto 20px auto"
            @click="toggelOptExpand"
          >
            {{ isExpand ? '收起' : '展开'
            }}<i
              :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              style="font-size: 12px; cursor: pointer"
            ></i>
          </div>
          <div class="spaceStart flexWrap sxbox">
            <InputNumber
              v-for="(item, index) in inputAttributeList"
              :item="item"
              :key="index"
              @change="(v, i) => handelInputAttrChange(index, v, i)"
            />
          </div>

          <div class="keyword_box">
            <div class="spaceBetween">
              <div class="spaceStart">
                <div class="playSearch_tit">关键词</div>
                <el-input
                  v-model="keyword"
                  class="search_keyword"
                  placeholder="请输入您要查找的账号/关键词"
                  @keyup.enter.native="searchListFun"
                ></el-input>
              </div>
              <div
                class="goodsItem_btn_search playListBtnBk"
                @click="shaixuanFun"
              >
                立即筛选
              </div>
            </div>
          </div>
          <!-- 展示您已选择 -->
          <div
            v-if="selectValueList.length"
            class="spaceStart"
            style="align-items: baseline"
          >
            <span class="playSearch_tit" style="font-weight: 600"
              >您已选择：</span
            >
            <div class="spaceStart flexWrap" style="flex: 1">
              <span
                v-for="item in selectValueList"
                :key="item.value"
                class="opt-item"
                @click="handelOptClick(item)"
              >
                {{ item.value }}&nbsp;<i
                  class="el-icon-close"
                  style="font-size: 14px; cursor: pointer"
                ></i>
              </span>
            </div>
          </div>

          <!-- sort -->
          <div
            id="sort_container"
            ref="piediv"
            class="sort_container spaceBetween"
          >
            <div class="spaceStart">
              <!-- <div class="spaceStart" style="margin-right: 10px">
                <el-select
                  v-model="stockValue"
                  style="width: 80px"
                  class="zhpxbox"
                  @change="stockValueChange"
                >
                  <el-option
                    v-for="(item, index) in stockOpts"
                    :label="item.name"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</el-option
                  >
                </el-select>
              </div> -->
              <div class="spaceStart">
                <div
                  v-for="(item, index) in comprehensiveData"
                  :class="item.selected != '' ? 'active' : ''"
                  :key="index"
                  class="spaceStart sort_item"
                  @click="sortChos(item)"
                >
                  <div>{{ item.sortName }}</div>
                  <IconFont
                    v-if="item.selected == '' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="sort"
                  />

                  <IconFont
                    v-if="item.selected == 'asc' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="asc"
                  />
                  <IconFont
                    v-if="item.selected == 'desc' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="desc"
                  />
                </div>
              </div>
            </div>

            <div class="spaceStart">
              <div
                style="cursor: pointer; margin-right: 41px"
                class="spaceStart"
                @click="changeListStyle"
              >
                {{ lietStyleName }}
                <IconFont :size="24" style="margin-left: 3px" icon="switch" />
                <!-- <i
                  class="el-icon-sort"
                  style="transform: rotate(90deg); font-weight: 600"
                ></i> -->
              </div>
              <!-- clearSearch -->
              <div class="spaceStart cursor" @click="cleanAllChoose">
                <!-- <i class="el-icon-delete" style="font-size: 18px"></i>&nbsp; -->
                <!-- <IconFont :size="24" color="#FF720C" icon="detail" /> -->
                <div>清空筛选项</div>
                <img src="../../../static/imgs/list_detail.svg" alt="" />
                <!-- <IconFont :size="24" style="margin-left: 3px" icon="detail" /> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="safe_width">
        <el-carousel
          v-if="GameBannerList && GameBannerList.length > 0"
          :loop="true"
          height="162px"
          indicator-position="none"
          style="margin: 20px 0px; border-radius: 24px; position: relative"
        >
          <el-carousel-item
            v-for="(item, index) in GameBannerList"
            :key="index"
          >
            <div v-if="item.url" class="pageGoBtn" @click="pageGo(item)">
              点击进入
            </div>
            <el-image
              :src="item.pic"
              style="width: 100%; height: 100%; cursor: pointer"
              fit="fill"
            ></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- 列表 -->
      <div class="safe_width">
        <div class="play_List_page_comStyle">
          <!-- style="margin-bottom: 60px; padding-bottom: 40px" -->
          <div
            v-loading.fullscreen.lock="listLoading"
            element-loading-text="加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(255, 255, 255, 0.8)"
            customClass="fixeIndex"
          >
            <div v-if="accountShopList && accountShopList.length">
              <div
                v-for="(item, index) in accountShopList"
                :key="index"
                :class="isNoPic ? 'single_word' : ''"
                class="spaceBetween goodsList_item"
                style="position: relative"
                @click="palyPage(item)"
              >
                <div style="width: 100%" class="spaceStartNotAi">
                  <div class="goodsItem_pic">
                    <el-image
                      :src="item.pic"
                      style="width: 100%; height: 100%; border-radius: 16px"
                      fit="cover"
                    ></el-image>
                    <el-image
                      v-if="item.stock == 0 || item.stock == 1"
                      class="soled_pic"
                      style="width: 100%; height: 100%; border-radius: 16px"
                      src="../../../static/soled.jpg"
                      fit="cover"
                    ></el-image>
                    <!-- <div
                      v-if="item.tssnum"
                      :class="getTssClazz(item)"
                      class="tssnum"
                    >
                      <span class="innernum">{{ item.tssnum }}</span>
                    </div> -->
                    <div v-if="item.tssnum" class="tssnum">
                      <span class="innernum">
                        <img
                          style="width: 20px; height: 20px"
                          src="../../../static/imgs/tssnum_icon.svg"
                          alt=""
                        />
                        {{ item.tssnum }}天赏</span
                      >
                    </div>
                    <div
                      v-else-if="item.tagsKKList && item.tagsKKList.length"
                      class="hot_pic"
                    >
                      {{ item.tagsKKList[0] }}
                    </div>
                  </div>
                  <div
                    :class="[
                      isNoPic
                        ? 'goodsItem_center_is_pic'
                        : 'goodsItem_center_is_not_pic',
                    ]"
                    class="goodsItem_center"
                  >
                    <div>
                      <div
                        class="goodsItem_center_title"
                        style="padding-bottom: 12px"
                      >
                        【{{ item.productSn }}】
                        <span v-if="item.accountType"
                          >【{{ item.accountType }}】</span
                        >
                        <span v-if="item.careers">【{{ item.careers }}】</span>
                      </div>

                      <el-tooltip
                        v-if="isNoPic == false"
                        :visible-arrow="false"
                        popper-class="tooltip_list"
                        class="item"
                        effect="dark"
                        placement="bottom"
                      >
                        <div slot="content">
                          <div class="topTips_tit">商品详情</div>
                          <div class="topTips_tit_content">
                            <span>{{ item.productSn }}</span>
                            <span>{{ item.gameAccountQufu }}</span>
                          </div>
                          <div
                            class="topTips_con light topTips_content"
                            v-html="tedianFilter(item.subTitle, item)"
                          ></div>
                        </div>
                        <div class="topTips_con text_linTwo list_infoWord">
                          <span v-if="item.profession"
                            >【{{ item.profession }}】</span
                          >
                          <div
                            class="light goodsItem_center_content"
                            v-html="
                              tedianFilter(`账号描述：${item.subTitle}`, item)
                            "
                          ></div>
                        </div>
                      </el-tooltip>
                      <div v-else class="topTips_con">
                        <span v-if="item.profession"
                          >【{{ item.profession }}】</span
                        >
                        <div
                          class="light content_is_pic"
                          v-html="tedianFilter(item.subTitle, item)"
                        ></div>
                      </div>
                      <div class="spaceStart goodsItem_center_address">
                        <div class="spaceStart">
                          <!-- <i class="el-icon-alarm-clock" style=""></i>&nbsp; -->
                          <div style="color: #969696">发布时间：</div>
                          <div>{{ item.publishTime | formatTime }}</div>
                        </div>
                        <div class="spaceStart" style="margin-left: 32.566px">
                          <div class="spaceStart">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoReadcount || 0 }}</div>
                          </div>
                          <div class="spaceStart" style="margin-left: 18.854px">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoCollectcount || 0 }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 游戏名+区服 -->
                    <!-- <div class="goodsItem_center_address textOneLine">
                      {{
                        `${item.productCategoryName}|${item.gameAccountQufu}`
                      }}
                    </div> -->

                    <div
                      class="spaceEnd"
                      style="justify-content: space-between; margin-top: 9px"
                    >
                      <!-- v-if="topAccount" -->
                      <div>
                        <img
                          v-if="item.topAccount == '顶级账号'"
                          style="width: 90px; height: 30px"
                          src="../../../static/imgs/playList_topAccount.svg"
                          alt=""
                        />
                      </div>
                      <div
                        v-if="item.price && item.price != 0"
                        class="goodsItem_price"
                      >
                        ¥ {{ item.price }}
                      </div>
                      <!-- <div v-else class="goodsItem_price">联系客服</div> -->

                      <!-- <div class="goodsItem_btn">查看详情</div> -->
                    </div>
                  </div>
                </div>
                <div
                  v-if="index === accountShopList.length - 1"
                  style="
                    text-align: center;
                    padding: 36px 0px 15px 0px;
                    position: relative;
                    z-index: 99;
                  "
                  @click.stop
                >
                  <el-pagination
                    :total="totalPage"
                    style="padding: 0"
                    layout=" pager,jumper"
                    class="playList_search_page_pagination"
                    @current-change="pageFun"
                  >
                  </el-pagination>
                  <!-- layout="prev, pager, next,jumper" -->
                </div>
                <!-- <img
                  style="
                    width: 1200px;
                    height: 1px;
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                  "
                  class="playListDavie"
                  src="../../../static/imgs/divider.png"
                  alt=""
                /> -->
              </div>
            </div>
            <div v-else class="sorry">
              <img
                style="width: 54px; height: 56px"
                src="../../../static/imgs/null.png"
                alt=""
              />
              <div style="margin-left: 15.85px">
                <!-- <div class="sorry_title">抱歉..</div> -->
                <img
                  style="width: 63px; height: 36px"
                  src="../../../static/imgs/sorry_text.svg"
                  alt=""
                />
                <div class="sorry_text">暂时没有搜索到您要的账号</div>
              </div>
              <!-- 抱歉，暂时没有搜索到您要的账号 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <footerKk />
    <navigation-fixed
      :product-category-id="searchParam.productCategoryId"
      @goPageTop="backTopPage"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import { getCategoryAdverList } from '@/api/kf.js';

import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import CheckBoxList from './components/CheckBoxList.vue';
import InputNumber from './components/InputNumber.vue';
import {
  searchProductList2,
  getProductAttribute,
  getProductCategory,
} from '@/api/search.js';
// import { getBannerListApi } from '@/api/index';
import { mapState } from 'vuex';
import util from '@/utils/index';
import { re } from 'semver';
const STOCKQUERYALL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 9,
    },
  ],
};
const STOCKQUERYOVER = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 0,
    },
  ],
};

const STOCKQUERYOL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 1,
      max: 9,
    },
  ],
};

const STOCKQRYMAP = {
  'all': STOCKQUERYALL,
  'ol': STOCKQUERYOL,
  'over': STOCKQUERYOVER,
};

const DEFSTOCKOPTS = [
  {
    name: '全部',
    id: 'all',
  },
  {
    name: '在售',
    id: 'ol',
  },
  {
    name: '已售',
    id: 'over',
  },
];

export default {
  metaInfo: {
    title: '看看账号网',
    titleTemplate: null,
  },
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    CheckBoxList,
    InputNumber,
  },
  data() {
    return {
      keyword2: '',

      keyword: '', // 账号搜索
      searchParam: {
        productCategoryId: this.$route.query.productCategoryId,
        pageNum: 1,
        pageSize: 10,
      },
      inputAttributeList: [],
      checkBoxAttributeList: [],
      isExpand: false,

      optsSearchResult: [],

      stockOpts: DEFSTOCKOPTS,

      comprehensiveDataSort: '',
      comprehensiveDataOrder: '',
      comprehensiveData: [],

      lietStyleName: '切换文字版',
      isNoPic: false, // 是否不展示图片模式
      background: '',
      backgroundImg: '',
      index: 1,
      jsonGame: {}, // 游戏展示
      totalPage: 10,

      accountShopList: [], // 账号数据
      GameBannerList: [],
      listLoading: false,

      stockValue: 'ol',
      stockQuery: STOCKQUERYOL,
    };
  },
  watch: {
    '$route.fullPath'() {
      this.doInit();
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    backgroundStr: function () {
      if (this.backgroundImg) {
        return (
          'background: url(' +
          this.backgroundImg +
          ') no-repeat center top / 100% auto;'
        );
      } else if (this.background) {
        return (
          'background: linear-gradient(' +
          this.background +
          ' 0%, rgb(255, 255, 255, 0) 50%);'
        );
      }
    },
    selectValueList() {
      let a = [];
      this.checkBoxAttributeList.forEach((e) => {
        const arr = e.selectValue.map((item) => ({
          name: e.name,
          value: item,
          parent: e.name === '区服' ? item.split('|')[0] : '',
        }));
        a = a.concat(arr);
      });
      return a;
    },
    newCheckBoxAttributeList() {
      return this.isExpand
        ? this.checkBoxAttributeList
        : this.checkBoxAttributeList.slice(0, 8);
    },
  },
  created() {},
  mounted() {
    this.doInit();
  },
  methods: {
    // 判断筛选项是否选中
    getOptIsCheck({ name, parent, value }) {
      if (name === '区服') {
        value = parent + '|' + value;
      }
      return this.selectValueList.find((sV) => {
        return sV.value === value && sV.name === name;
      });
    },
    // 筛选项搜索
    handelOptsSearch(v) {
      let result = [];
      if (v) {
        this.checkBoxAttributeList.forEach((e) => {
          if (e.selectType === 3) {
            // 级联数据
            for (let key in e.optionList) {
              const childList = e.optionList[key];
              childList.forEach((child) => {
                if (child.includes(v)) {
                  result.push({
                    name: e.name,
                    parent: key,
                    value: child,
                  });
                }
              });
            }
          } else {
            result = result.concat(
              e.optionList
                .filter((opt) => opt.includes(v))
                .map((opt) => ({ name: e.name, value: opt }))
            );
          }
        });
      }
      this.optsSearchResult = result;
      console.log(v, result);
    },
    // 筛选项点击
    handelOptClick({ name, parent, value }) {
      console.log('handelOptClick', name, parent, value);
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === name
      );
      const { selectValue, filterType } =
        this.checkBoxAttributeList[index] || {};

      let newV = selectValue;

      if (name === '区服' && !value.includes('|')) {
        value = `${parent}|${value}`;
      }

      // 单选处理
      if (filterType !== 1) {
        if (selectValue.includes(value)) {
          newV = [];
        } else {
          newV = [value];
        }
      } else {
        // 多选处理 filterType=1 表示多选
        if (selectValue.includes(value)) {
          newV = selectValue.filter((e) => e !== value);
        } else {
          newV.push(value);
        }
      }
      this.$set(this.checkBoxAttributeList[index], 'selectValue', newV);
      this.$forceUpdate();
      this.searchByCate();
    },
    // 筛选区域展开收起
    toggelOptExpand() {
      this.isExpand = !this.isExpand;
    },
    // 筛选输入框区域值变化
    handelInputAttrChange(index, v) {
      this.$set(this.inputAttributeList[index], 'selectValue', v);
    },
    // 筛选复选框区域值变化
    handelCheckboxAttrChange(index, v) {
      this.$set(this.checkBoxAttributeList[index], 'selectValue', v);
      this.searchByCate();
    },

    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    getTssClazz(item) {
      let l = item.tssnum.toString().length;
      return `tssnum${l}`;
    },
    stockValueChange(value) {
      this.stockQuery = STOCKQRYMAP[value];
      this.searchByCate();
    },
    doInit() {
      const { keyword, productCategoryId } = this.$route.query;
      if (keyword) {
        this.keyword = keyword;
      }
      if (productCategoryId) {
        // 获取游戏具体信息
        getProductCategory(productCategoryId).then((res) => {
          if (res.code == 200) {
            this.jsonGame = res.data || {};

            // 获取游戏bannar
            this.getGameBanner(this.jsonGame);
          }
        });

        // 获取动态配置的搜索属性后，直接进行一次搜索
        this.loadCateList(productCategoryId).then(() => {
          this.searchByCate();
        });
      }
    },
    getGameBanner({ id, custom }) {
      custom = JSON.parse(custom || '{}');
      const findKey = Object.keys(custom).find(
        (ele) => ele.indexOf('advertise') == 0
      );
      if (findKey) {
        // const type = findKey.split('_')[1];
        getCategoryAdverList({
          categoryId: id,
          type: 0,
        }).then((res) => {
          if (res.code == 200) {
            this.GameBannerList = res.data;
          }
        });
      }
    },
    resetPage() {
      this.searchParam.pageNum = 1;
      this.totalPage = 0;
    },
    checkInputSearch() {
      // 根据输入框设置排序规则
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((ele) => {
        ele.selected = '';
      });
      const inputHasValueList = this.inputAttributeList.filter((ele) => {
        return (
          ele.selectValue &&
          ele.selectValue.length === 2 &&
          (ele.selectValue[0] != '' || ele.selectValue[1] != '')
        );
      });
      if (inputHasValueList.length == 1) {
        let item = inputHasValueList[0];
        let findIt;
        if (item.name == '价格') {
          findIt = this.comprehensiveData.find(
            (ele) => ele.sortName == item.name
          );
        } else {
          findIt = this.comprehensiveData.find((ele) => ele.name == item.name);
        }
        if (item.selectValue[0] != '' && item.selectValue[1] == '') {
          // 低到高
          if (findIt) {
            findIt.selected = 'asc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'asc';
          }
        } else if (item.selectValue[0] == '' && item.selectValue[1] != '') {
          if (findIt) {
            findIt.selected = 'desc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'desc';
          }
        }
      }
    },
    searchByCate(isPageNum, shaixuan) {
      if (isPageNum != 1) {
        this.resetPage();
      }

      if (shaixuan == 'shaixuan') {
        this.checkInputSearch();
      }

      // 搜集复选框搜索条件
      const checkBoxParams = this.checkBoxAttributeList.map((e) => {
        return {
          ...e,
          value: e.selectValue.join(','),
        };
      });

      // 搜集输入框条件,价格是公用条件要单独拎出
      let queryIntParams = undefined;
      const inputParams = this.inputAttributeList
        .map((e) => {
          let [min, max] = e.selectValue || [];

          // 价格是公共属性，得放到queryIntParams
          if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
            queryIntParams = [
              {
                min: !isNaN(min) ? parseInt(min, 10) : undefined,
                key: 'price',
                max: !isNaN(max) ? parseInt(max, 10) : undefined,
              },
            ];
          }
          return {
            ...e,
            value:
              !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
          };
        })
        .filter((e) => e.name !== '价格');

      // 如果是32306，有ename，放入queryStrParams里,同时属性里要剔除。否则不用管。pc端不像h5端，按属性正常处理的，不用加回来
      // 比如区服的数据
      let queryStrParams = [];
      const findIndex = checkBoxParams.findIndex((ele) => {
        return ele.sort === 32306 && ele.ename;
      });
      if (findIndex !== -1) {
        let { value, ename } = checkBoxParams[findIndex];
        if (value) {
          queryStrParams.push({
            key: ename,
            value,
          });
        }
        checkBoxParams.splice(findIndex, 1);
      }

      // if (this.stockQuery) {
      //   let queryIntParams = data.queryIntParams;
      //   if (queryIntParams) {
      //     data.queryIntParams = data.queryIntParams.concat(
      //       this.stockQuery.queryIntParams,
      //     );
      //   } else {
      //     data.queryIntParams = this.stockQuery.queryIntParams;
      //   }
      // }
      this.listLoading = true;

      const searchParam = {
        ...this.searchParam, //分页等条件
        memberId: this.userInfo.id,
      };
      const params = {
        attrValueList: checkBoxParams.concat(inputParams),
        keyword: this.keyword, // 关键词搜索
        sort: this.comprehensiveDataSort,
        order: this.comprehensiveDataOrder,
        queryStrParams,
        queryIntParams, // 价格搜索条件
      };

      searchProductList2(searchParam, params).then((response) => {
        this.listLoading = false;
        if (response.code == 200) {
          let list = response.data.list || [];

          if (this.checkSn(list)) {
            return;
          }
          this.totalPage = response.data.total;
          this.accountShopList = list;
          this.accountShopList.forEach((ele) => {
            const findtss = ele.attrValueList.find((item) => {
              return item.name === '已使用天赏石';
            });
            const findtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用天赏石';
            });
            const findtss3 = ele.attrValueList.find((item) => {
              return item.name === '账号专区';
            });
            const findtss4 = ele.attrValueList.find((item) => {
              return item.name === '账号类型';
            });
            const findtss5 = ele.attrValueList.find((item) => {
              return item.name === '职业';
            });
            ele.tssnum = 0;
            ele.topAccount = '';
            ele.accountType = '';
            ele.careers = '';
            if (findtss) {
              ele.tssnum = findtss.intValue;
            }
            if (findtss2) {
              ele.tssnum = ele.tssnum + findtss2.intValue;
            }
            if (findtss3) {
              ele.topAccount = findtss3.value;
            }
            if (findtss4) {
              ele.accountType = findtss4.value;
            }
            if (findtss5) {
              ele.careers = findtss5.value;
            }
          });
        }
      });
    },
    checkSn(list) {
      return false;
      let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      if (
        reg.test(this.keyword) &&
        list.length === 1 &&
        this.searchParam.pageNum === 1
      ) {
        const { productCategoryId, id, productSn } = list[0];
        this.keyword = '';
        this.$router.push({
          path: `/gd/${productSn}`,
        });
        return true;
      }
      return false;
    },
    loadCateList(productCategoryId) {
      return getProductAttribute(productCategoryId).then((response) => {
        if (response.code == 200) {
          // 再根据sort排序
          const cateList = response.data
            .filter((ele) => ele.type !== 0 && ele.searchType !== 0)
            .sort((a, b) => {
              return b.sort - a.sort;
            });

          // 复选框属性
          this.checkBoxAttributeList = cateList
            .filter((e) => e.inputType !== 0) // 过滤掉输入框属性
            .map((e) => {
              const { inputList, selectType, name, searchType } = e;
              let optionList = inputList.split(','); // 子级数据
              let pOptionList = null; // 父级数据

              // 级联数据
              if (selectType === 3) {
                optionList = {};
                const treeData = JSON.parse(inputList);

                pOptionList = treeData.map((e) => e.parent_name);
                treeData.forEach((e) => {
                  const { parent_name, childList } = e;
                  optionList[parent_name] = childList;
                });
              }
              // 处理默认值
              let value = [];
              if (name === '账号专区') {
                value = ['在售专区'];
              }

              return {
                ...e,
                selectValue: value,
                childValue: {}, // 只有级联数据才有
                hasClear: searchType !== 3,
                optionList,
                pOptionList,
                defaultValue: value, // 清空选项时 可以设回初识默认值
              };
            });

          // 输入框属性搜索列表
          const inputAttributeList = cateList
            .filter((e) => e.inputType === 0)
            .map((e) => {
              return {
                ...e,
                selectValue: [],
                defaultValue: [], // 清空选项时 可以设回初识默认值
              };
            });

          // 价格公共属性插入
          this.inputAttributeList = [
            {
              'name': '价格',
              'selectType': 0,
              'inputType': 0,
              'inputList': '',
              'sort': 0,
              'filterType': 0,
              'searchType': 2,
              'type': 1,
              'searchSort': 0,
              selectValue: [],
              defaultValue: [],
            },
          ].concat(inputAttributeList);

          // 排序数据
          this.comprehensiveData = [
            // {
            //   sortName: '综合排序',
            //   selected: '',
            //   searchSort: 'score',
            //   value: '',
            // },
            // {
            //   sortName: '最多人看',
            //   selected: '',
            //   searchSort: 'gameSysinfoReadcount',
            // },
            {
              sortName: '上架时间',
              selected: '',
              searchSort: 'publishTime',
            },
            {
              sortName: '价格',
              selected: '',
              searchSort: 'price',
            },
          ].concat(
            cateList
              .filter((e) => e.searchSort)
              .map((e) => {
                return {
                  sortName: e.name,
                  selected: '',
                  searchSort: e.searchSort,
                };
              })
          );
          return true;
        }
      });
    },
    //分割线
    changeListStyle() {
      if (this.isNoPic == false) {
        this.isNoPic = true;
        this.lietStyleName = '切换图文版';
      } else {
        this.isNoPic = false;
        this.lietStyleName = '切换文字版';
      }
    },
    // 滚动到顶部
    backTopPage() {
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 滚动回到固定位置
    scrollTop() {
      var heightNum = this.$refs.offsetHeightNeed.offsetHeight + 230;
      // var topDistance = this.$refs.piediv.getBoundingClientRect().top;
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: heightNum,
        behavior: 'smooth',
      });
    },
    // 顶部搜索框 搜索游戏ID变化
    changeFlagList(val) {
      if (val) {
        this.doInit();
      }
    },
    // 点击筛选按钮
    shaixuanFun() {
      this.searchByCate(null, 'shaixuan');
    },
    // 筛选排序
    sortChos(date) {
      if (date.sortName == '综合排序') {
        this.comprehensiveDataOrder = '';
        this.comprehensiveDataSort = '';
        this.comprehensiveData.forEach((item, index) => {
          if (index == 0) {
            item.selected = '综合排序';
          } else {
            item.selected = '';
          }
        });
      } else {
        this.comprehensiveData.forEach((item, index) => {
          if (item.sortName == date.sortName) {
            if (date.selected == '') {
              date.selected = 'desc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'desc';
            } else if (date.selected == 'desc') {
              date.selected = 'asc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'asc';
            } else if (date.selected == 'asc') {
              date.selected = '';
              this.comprehensiveDataOrder = '';
              this.comprehensiveDataSort = '';
            }
          } else {
            item.selected = '';
          }
        });
      }
      //   this.changePageFirst();
      this.searchByCate();
    },
    // 清空所有筛选
    cleanAllChoose() {
      this.keyword = '';
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((item, index) => {
        item.selected = '';
      });

      this.inputAttributeList = this.inputAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.checkBoxAttributeList = this.checkBoxAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.$forceUpdate();

      this.serverDateSub = [];
      this.searchByCate();
    },
    // 分页
    pageFun(val) {
      this.searchParam.pageNum = `${val}`;
      this.searchByCate(1);
      this.scrollTop();
    },
    // 搜索账号
    searchListFun() {
      // if (!this.keyword) {
      //   this.$message.error('请输入搜索内容');
      //   return;
      // }
      this.changePageFirst();
      this.searchByCate();
    },
    changePageFirst() {
      this.searchParam.pageNum = 1;
    },
    // 账号详情
    palyPage(date) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${date.productSn}`,
      });
      window.open(routeUrl.href, '_blank');
    },
    // 轮播跳转
    pageGo(date) {
      const { url = '' } = date;
      if (url.indexOf('http') == 0) {
        window.open(url);
      } else {
        this.$router.push({
          path: url,
        });
      }
    },
  },
};
</script>

<style type="text/css">
@import url(./playList.scss);
.light span {
  color: #fe5a1e;
}
.topSearch_clas {
  margin-right: 20px;
  border-radius: 30px;
}
.el-select.topSearch_clas .el-input__inner {
  border-radius: 10px;
  border-color: #ff6716;
  text-align: center;
  width: 150px;
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-left: 10px !important;
  margin-top: 11px !important;
}
.el-collapse-item__wrap {
  overflow: visible !important;
}
</style>
<style lang="scss" scoped>
.keyword_box {
  border-bottom: 0.5px solid #ff7a00;
  margin: 0 0 34.28px 0;
  padding-bottom: 34.28px;
  .search_keyword {
    width: 265px;
    margin-right: 10px;
    /deep/ .el-input__inner {
      border-radius: 50px !important;
    }
  }
}
/deep/.playList_search_page_pagination {
  position: relative;
  .el-pagination__jump {
    position: absolute;
    right: 56.5px;
    color: #2d2d2d;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    .el-input__inner {
      height: 20px;
      border-radius: 20px;
      border: none;
      background: #f6f6f6;
    }
  }
}
.playList_search_page_pagination /deep/ ul {
  li {
    color: rgba(0, 0, 0, 0.4);
    min-width: 24px;
    padding: 0px 12px;
  }
  .active {
    color: #2d2d2d !important;
  }
}
/deep/ .zhpxbox {
  .el-input__inner {
    border: 0;
    background: #f4f4f4;
    font-size: 16px;
    color: #222;
  }
}
.stock_box {
  padding: 20px 0;
  border-bottom: 1px solid #dcdcdc;
}
.scrollBody::-webkit-scrollbar {
  width: 2px;
}
.scrollBody::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}
.playHead_wrap {
  border-bottom: 1px solid #dcdcdc;
  padding-bottom: 25px;
}
// .playSearch_tit {
//   flex-shrink: 0;
//   width: 100px;
//   font-size: 14px;
//   font-weight: 400;
//   color: #222222;
// }
.hight_level_box {
  margin-top: 10px;
  align-items: flex-start;
  position: relative;
  .playSearch_tit {
    color: #222;
    margin-top: 14px;
  }
}
/deep/ .hight_level_choose {
  width: 240px;
  margin: 10px 10px 0 0;
}
.playSearch_wrap {
  padding-top: 8px;
  align-items: baseline;
}
.flexWrap {
  flex-wrap: wrap;
}
.more-btn-highLevel {
  position: absolute;
  right: 20px;
  top: 18px;
}
.choose_dl {
  display: flex;
  margin-right: 5px;
  .more-btn {
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    color: #999;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    margin-bottom: 20px;
    user-select: none;
    width: 90px;
  }
}
.choose_dl_close {
  height: 44px;
  overflow: hidden;
  .choose_label_checkbox {
    height: 44px;
  }
}
.choose_dl_open {
  height: inherit;
  .choose_label_checkbox {
    height: inherit;
  }
}
.choose_label_box {
  display: flex;
  align-items: baseline;
}
.choose_label_checkbox {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.playSearch_item {
  cursor: pointer;
  padding: 0px 25px;
  background: #ffffff;
  border: 1px solid #dcdcdc;
  border-radius: 8px;
  font-size: 14px;
  color: #909090;
  margin-right: 12px;
  transition: all 0.3s;
  margin-top: 8px;
  line-height: 32px !important;
  height: 32px !important;
  flex-shrink: 0;
}
.playSearch_item.active {
  color: #ff6716;
  background: #fff4ee;
  border: 1px solid #ff4f11;
}

.sortArr_pic {
  width: 10px;
  height: auto;
  margin-left: 8px;
}

.goodsItem_btn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 14px;
  color: #ffffff;
  cursor: pointer;
  white-space: nowrap;
  height: 40px;
  line-height: 40px;
  padding: 0 26px;
  border-radius: 20px;
}
.chooseSelect {
  margin-right: 15px;
  margin-top: 15px;
  width: 240px;
}

.playList_icon {
  position: absolute;
  left: -160px;
  top: -20px;
}
.tagPic_acc {
  position: absolute;
  z-index: 10;
  left: 0;
  top: 0;
  width: 110px;
  height: auto;
}
.hotPic_list {
  position: absolute;
  width: 64px;
  z-index: 10;
  right: -10px;
  top: -35px;
}

/*********************************************  列表模式切换  *******************************************************/

.more_btn {
  width: 60px;
  cursor: pointer;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  text-align: center;
  border-radius: 60px;
  padding: 6px 0;
  font-size: 12px;

  &:hover {
    background: #f7f7f7;
  }
}

.opt-item {
  cursor: pointer;
  padding: 0px 8px;
  background: #f7f7f7;
  border: 1px solid #f7f7f7;
  border-radius: 8px;
  font-size: 14px;
  color: #222;
  margin-right: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
  line-height: 30px !important;
  height: 30px !important;
  flex-shrink: 0;
  font-weight: 500;

  &.active {
    background: #fff4ee;
    color: #ff6716;
    border-color: #fff4ee;
  }
}
</style>
