{"heroList": [{"heroId": "505", "heroType": "辅助", "name": "瑶"}, {"heroId": "106", "heroType": "法师", "name": "小乔"}, {"heroId": "141", "heroType": "法师", "name": "貂蝉"}, {"heroId": "176", "heroType": "法师", "name": "杨玉环"}, {"heroId": "508", "heroType": "射手", "name": "伽罗"}, {"heroId": "132", "heroType": "射手", "name": "马可波罗"}, {"heroId": "159", "heroType": "辅助", "name": "朵莉亚"}, {"heroId": "523", "heroType": "法师", "name": "西施"}, {"heroId": "519", "heroType": "射手", "name": "敖隐"}, {"heroId": "148", "heroType": "法师", "name": "姜子牙"}, {"heroId": "506", "heroType": "刺客", "name": "云中君"}, {"heroId": "111", "heroType": "射手", "name": "孙尚香"}, {"heroId": "196", "heroType": "射手", "name": "百里守约"}, {"heroId": "152", "heroType": "法师", "name": "王昭君"}, {"heroId": "157", "heroType": "刺客", "name": "不知火舞"}, {"heroId": "199", "heroType": "射手", "name": "公孙离"}, {"heroId": "139", "heroType": "战士", "name": "老夫子"}, {"heroId": "129", "heroType": "战士", "name": "典韦"}, {"heroId": "162", "heroType": "刺客", "name": "娜可露露"}, {"heroId": "531", "heroType": "刺客", "name": "镜"}, {"heroId": "524", "heroType": "射手", "name": "蒙犽"}, {"heroId": "110", "heroType": "法师", "name": "嬴政"}, {"heroId": "118", "heroType": "辅助", "name": "孙膑"}, {"heroId": "518", "heroType": "战士", "name": "马超"}, {"heroId": "564", "heroType": "战士", "name": "姬小满"}, {"heroId": "563", "heroType": "法师", "name": "海诺"}, {"heroId": "136", "heroType": "法师", "name": "武则天"}, {"heroId": "123", "heroType": "战士", "name": "吕布"}, {"heroId": "533", "heroType": "坦克", "name": "阿古朵"}, {"heroId": "109", "heroType": "法师", "name": "妲己"}, {"heroId": "142", "heroType": "法师", "name": "安琪拉"}, {"heroId": "312", "heroType": "法师", "name": "沈梦溪"}, {"heroId": "195", "heroType": "刺客", "name": "百里玄策"}, {"heroId": "154", "heroType": "战士", "name": "花木兰"}, {"heroId": "511", "heroType": "坦克", "name": "猪八戒"}, {"heroId": "545", "heroType": "射手", "name": "莱西奥"}, {"heroId": "121", "heroType": "法师", "name": "芈月"}, {"heroId": "540", "heroType": "法师", "name": "金蝉"}, {"heroId": "125", "heroType": "刺客", "name": "元歌"}, {"heroId": "108", "heroType": "法师", "name": "墨子"}, {"heroId": "182", "heroType": "法师", "name": "干将莫邪"}, {"heroId": "140", "heroType": "战士", "name": "关羽"}, {"heroId": "504", "heroType": "法师", "name": "米莱狄"}, {"heroId": "175", "heroType": "辅助", "name": "钟馗"}, {"heroId": "135", "heroType": "坦克", "name": "项羽"}, {"heroId": "127", "heroType": "法师", "name": "甄姬"}, {"heroId": "197", "heroType": "法师", "name": "弈星"}, {"heroId": "173", "heroType": "射手", "name": "李元芳"}, {"heroId": "194", "heroType": "辅助", "name": "苏烈"}, {"heroId": "190", "heroType": "法师", "name": "诸葛亮"}, {"heroId": "113", "heroType": "辅助", "name": "庄周"}, {"heroId": "501", "heroType": "辅助", "name": "明世隐"}, {"heroId": "189", "heroType": "辅助", "name": "鬼谷子"}, {"heroId": "149", "heroType": "坦克", "name": "刘邦"}, {"heroId": "130", "heroType": "战士", "name": "宫本武藏"}, {"heroId": "521", "heroType": "法师", "name": "海月"}, {"heroId": "192", "heroType": "射手", "name": "黄忠"}, {"heroId": "513", "heroType": "法师", "name": "上官婉儿"}, {"heroId": "156", "heroType": "法师", "name": "张良"}, {"heroId": "187", "heroType": "辅助", "name": "东皇太一"}, {"heroId": "155", "heroType": "射手", "name": "艾琳"}, {"heroId": "502", "heroType": "刺客", "name": "裴擒虎"}, {"heroId": "133", "heroType": "射手", "name": "狄仁杰"}, {"heroId": "114", "heroType": "辅助", "name": "刘禅"}, {"heroId": "174", "heroType": "射手", "name": "虞姬"}, {"heroId": "179", "heroType": "法师", "name": "女娲"}, {"heroId": "153", "heroType": "刺客", "name": "兰陵王"}, {"heroId": "191", "heroType": "辅助", "name": "大乔"}, {"heroId": "515", "heroType": "法师", "name": "嫦娥"}, {"heroId": "150", "heroType": "刺客", "name": "韩信"}, {"heroId": "184", "heroType": "辅助", "name": "蔡文姬"}, {"heroId": "510", "heroType": "战士", "name": "孙策"}, {"heroId": "168", "heroType": "辅助", "name": "牛魔"}, {"heroId": "131", "heroType": "刺客", "name": "<PERSON>白"}, {"heroId": "124", "heroType": "法师", "name": "周瑜"}, {"heroId": "105", "heroType": "坦克", "name": "廉颇"}, {"heroId": "120", "heroType": "坦克", "name": "白起"}, {"heroId": "163", "heroType": "刺客", "name": "橘右京"}, {"heroId": "171", "heroType": "辅助", "name": "张飞"}, {"heroId": "183", "heroType": "战士", "name": "雅典娜"}, {"heroId": "193", "heroType": "战士", "name": "铠"}, {"heroId": "112", "heroType": "射手", "name": "鲁班七号"}, {"heroId": "146", "heroType": "刺客", "name": "露娜"}, {"heroId": "167", "heroType": "刺客", "name": "孙悟空"}, {"heroId": "169", "heroType": "射手", "name": "后羿"}, {"heroId": "186", "heroType": "辅助", "name": "太乙真人"}, {"heroId": "507", "heroType": "战士", "name": "李信"}, {"heroId": "116", "heroType": "刺客", "name": "阿轲"}, {"heroId": "177", "heroType": "射手", "name": "苍"}, {"heroId": "525", "heroType": "辅助", "name": "鲁班大师"}, {"heroId": "522", "heroType": "战士", "name": "曜"}, {"heroId": "166", "heroType": "战士", "name": "亚瑟"}, {"heroId": "137", "heroType": "刺客", "name": "司马懿"}, {"heroId": "528", "heroType": "刺客", "name": "澜"}, {"heroId": "537", "heroType": "战士", "name": "司空震"}, {"heroId": "107", "heroType": "战士", "name": "赵云"}, {"heroId": "119", "heroType": "法师", "name": "扁鹊"}, {"heroId": "198", "heroType": "坦克", "name": "梦奇"}, {"heroId": "178", "heroType": "战士", "name": "杨戬"}, {"heroId": "170", "heroType": "战士", "name": "刘备"}, {"heroId": "115", "heroType": "法师", "name": "高渐离"}, {"heroId": "536", "heroType": "战士", "name": "夏洛特"}, {"heroId": "128", "heroType": "战士", "name": "曹操"}, {"heroId": "527", "heroType": "坦克", "name": "蒙恬"}, {"heroId": "509", "heroType": "辅助", "name": "盾山"}, {"heroId": "538", "heroType": "战士", "name": "云缨"}, {"heroId": "126", "heroType": "坦克", "name": "夏侯惇"}, {"heroId": "582", "heroType": "法师", "name": "元流之子(法师)"}, {"heroId": "117", "heroType": "战士", "name": "钟无艳"}, {"heroId": "134", "heroType": "战士", "name": "达摩"}, {"heroId": "144", "heroType": "坦克", "name": "程咬金"}, {"heroId": "503", "heroType": "战士", "name": "狂铁"}, {"heroId": "529", "heroType": "战士", "name": "盘古"}, {"heroId": "548", "heroType": "射手", "name": "戈娅"}, {"heroId": "558", "heroType": "战士", "name": "影"}, {"heroId": "180", "heroType": "战士", "name": "哪吒"}, {"heroId": "514", "heroType": "战士", "name": "亚连"}, {"heroId": "517", "heroType": "战士", "name": "大司命"}, {"heroId": "534", "heroType": "辅助", "name": "桑启"}, {"heroId": "542", "heroType": "刺客", "name": "暃"}, {"heroId": "544", "heroType": "战士", "name": "赵怀真"}, {"heroId": "577", "heroType": "辅助", "name": "少司缘"}, {"heroId": "581", "heroType": "坦克", "name": "元流之子(坦克)"}], "skinList": [{"skinId": 17704, "skinName": "苍林狼骑", "heroId": "177", "heroName": "苍", "classTypeName": ["勇者品质", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 2}, {"skinId": 52701, "skinName": "秩序猎龙将", "heroId": "527", "heroName": "蒙恬", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 3}, {"skinId": 50901, "skinName": "极冰防御线", "heroId": "509", "heroName": "盾山", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 11201, "skinName": "木偶奇遇记", "heroId": "112", "heroName": "鲁班七号", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 18001, "skinName": "三太子", "heroId": "180", "heroName": "哪吒", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 18207, "skinName": "雾都夜雨", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 56402, "skinName": "妄想食味", "heroId": "564", "heroName": "姬小满", "classTypeName": ["史诗品质", "限定", "珍宝阁专属", "妄想都市"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 11402, "skinName": "绅士熊喵", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 53404, "skinName": "鸣野蒿", "heroId": "534", "heroName": "桑启", "classTypeName": ["传说品质", "限定", "周年限定", "草木风华"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 10705, "skinName": "白执事", "heroId": "107", "heroName": "赵云", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 54001, "skinName": "前尘", "heroId": "540", "heroName": "金蝉", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 2}, {"skinId": 18704, "skinName": "噬灭天穹", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S37赛季专属", "skinNum": 4}, {"skinId": 31207, "skinName": "龙舞盛年", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "限定", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 52905, "skinName": "敬我三分", "heroId": "529", "heroName": "盘古", "classTypeName": ["勇者品质", "限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 12602, "skinName": "乘风破浪", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "坦克", "accessWay": "", "skinNum": 6}, {"skinId": 14804, "skinName": "天穹之誓", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S36赛季专属", "skinNum": 4}, {"skinId": 12406, "skinName": "熔金海岸", "heroId": "124", "heroName": "周瑜", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 58100, "skinName": "止戈之道", "heroId": "581", "heroName": "元流之子(坦克)", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 1}, {"skinId": 58200, "skinName": "万妙之心", "heroId": "582", "heroName": "元流之子(法师)", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 1}, {"skinId": 11202, "skinName": "福禄兄弟", "heroId": "112", "heroName": "鲁班七号", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 51701, "skinName": "暗都幽影", "heroId": "517", "heroName": "大司命", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 17801, "skinName": "埃及法老", "heroId": "178", "heroName": "杨戬", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 11210, "skinName": "蔬乡物语", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "战令限定", "限定", "蔬菜精灵"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 11}, {"skinId": 14002, "skinName": "天启骑士", "heroId": "140", "heroName": "关羽", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 17309, "skinName": "蔬乡物语", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "战令限定", "限定", "蔬菜精灵"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 16908, "skinName": "完美运算", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 14906, "skinName": "剑破天穹", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S35赛季专属", "skinNum": 5}, {"skinId": 52401, "skinName": "归虚梦演", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["归虚梦演"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 12901, "skinName": "黄金武士", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 50303, "skinName": "特工战影", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 10504, "skinName": "功夫炙烤", "heroId": "105", "heroName": "廉颇", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 53601, "skinName": "永昼", "heroId": "536", "heroName": "夏洛特", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 10606, "skinName": "青蛇", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 13501, "skinName": "帝国元帅", "heroId": "135", "heroName": "项羽", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 8}, {"skinId": 50401, "skinName": "精准探案法", "heroId": "504", "heroName": "米莱狄", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 50301, "skinName": "命运角斗场", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 16801, "skinName": "西部大镖客", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "西部大镖客"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 13402, "skinName": "大发明家", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 11505, "skinName": "燃音魔法", "heroId": "115", "heroName": "高渐离", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 14207, "skinName": "乘龙·聚宝船", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 14110, "skinName": "幻阙歌", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 10}, {"skinId": 15901, "skinName": "金色潮汐", "heroId": "159", "heroName": "朵莉亚", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 2}, {"skinId": 10910, "skinName": "灵卜魔法", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 10}, {"skinId": 12601, "skinName": "战争骑士", "heroId": "126", "heroName": "夏侯惇", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 6}, {"skinId": 18905, "skinName": "天穹祈灯", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S34赛季专属", "skinNum": 5}, {"skinId": 52703, "skinName": "荣光圣徽", "heroId": "527", "heroName": "蒙恬", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 3}, {"skinId": 19401, "skinName": "爱与和平", "heroId": "194", "heroName": "苏烈", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 13002, "skinName": "未来纪元", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 53303, "skinName": "江河有灵", "heroId": "533", "heroName": "阿古朵", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "坦克", "accessWay": "活动获取", "skinNum": 3}, {"skinId": 51401, "skinName": "破局者", "heroId": "514", "heroName": "亚连", "classTypeName": ["勇者品质", "胡桃异想国"], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 15607, "skinName": "古海寻踪", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S33赛季专属", "skinNum": 7}, {"skinId": 54802, "skinName": "驭风魔法", "heroId": "548", "heroName": "戈娅", "classTypeName": ["史诗品质", "战令限定", "限定", "魔法世界"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 2}, {"skinId": 13104, "skinName": "敏锐之力", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["勇者品质", "五路精神"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 15207, "skinName": "午后时光", "heroId": "152", "heroName": "王昭君", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 52504, "skinName": "探海日志", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S32赛季专属", "skinNum": 4}, {"skinId": 52305, "skinName": "玲珑珍味", "heroId": "523", "heroName": "西施", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 10701, "skinName": "忍·炎影", "heroId": "107", "heroName": "赵云", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 14402, "skinName": "星际陆战队", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9}, {"skinId": 11801, "skinName": "未来旅行", "heroId": "118", "heroName": "孙膑", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 10909, "skinName": "青丘·九尾", "heroId": "109", "heroName": "妲己", "classTypeName": ["妖灵志异", "无双", "珍品限定"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 10}, {"skinId": 54201, "skinName": "碧珀绯影", "heroId": "542", "heroName": "暃", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 18601, "skinName": "圆桌骑士", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["圆桌骑士"], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 12305, "skinName": "野性能量", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 19801, "skinName": "美梦成真", "heroId": "198", "heroName": "梦奇", "classTypeName": ["乐园午夜"], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 19301, "skinName": "龙域领主", "heroId": "193", "heroName": "铠", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 14601, "skinName": "哥特玫瑰", "heroId": "146", "heroName": "露娜", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 52801, "skinName": "孤猎", "heroId": "528", "heroName": "澜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 16601, "skinName": "死亡骑士", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "碎片商店兑换", "skinNum": 7}, {"skinId": 12401, "skinName": "海军大将", "heroId": "124", "heroName": "周瑜", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 31206, "skinName": "匿光破解者", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 54501, "skinName": "西部游侠", "heroId": "545", "heroName": "莱西奥", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 15401, "skinName": "剑舞者", "heroId": "154", "heroName": "花木兰", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 18604, "skinName": "劲辣红锅", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 50206, "skinName": "擒涛扼浪", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "S31赛季专属", "skinNum": 6}, {"skinId": 12501, "skinName": "午夜歌剧院", "heroId": "125", "heroName": "元歌", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 12802, "skinName": "超能战警", "heroId": "128", "heroName": "曹操", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 54401, "skinName": "太极少年", "heroId": "544", "heroName": "赵怀真", "classTypeName": ["勇者品质", "青春校园"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 11405, "skinName": "唤灵魔甲", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 19004, "skinName": "掌控之力", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["勇者品质", "五路精神"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 51104, "skinName": "潮玩探月行", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["勇者品质", "奇趣潮玩"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 4}, {"skinId": 10802, "skinName": "龙骑士", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 12001, "skinName": "白色死神", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 12303, "skinName": "末日机甲", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "末日机甲"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 53401, "skinName": "画中游", "heroId": "534", "heroName": "桑启", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 53402, "skinName": "海盐诗旅", "heroId": "534", "heroName": "桑启", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S30赛季专属", "skinNum": 4}, {"skinId": 19803, "skinName": "顽趣", "heroId": "198", "heroName": "梦奇", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证", "顽趣"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 4}, {"skinId": 13004, "skinName": "万象初新", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 14404, "skinName": "功夫厨神", "heroId": "144", "heroName": "程咬金", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9}, {"skinId": 18904, "skinName": "五谷丰年", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 17306, "skinName": "云中旅人", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S29赛季专属", "skinNum": 9}, {"skinId": 13201, "skinName": "激情绿茵", "heroId": "132", "heroName": "马可波罗", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 52501, "skinName": "归虚梦演", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["归虚梦演"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 19201, "skinName": "芝加哥教父", "heroId": "192", "heroName": "黄忠", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 4}, {"skinId": 31205, "skinName": "大漠名商", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S28赛季专属", "skinNum": 7}, {"skinId": 52901, "skinName": "创世神祝", "heroId": "529", "heroName": "盘古", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 15305, "skinName": "金庭之子", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "赛季之旅", "skinNum": 7}, {"skinId": 13405, "skinName": "沙漠行僧", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S26赛季专属", "skinNum": 5}, {"skinId": 53301, "skinName": "熊喵少女", "heroId": "533", "heroName": "阿古朵", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 3}, {"skinId": 12803, "skinName": "幽灵船长", "heroId": "128", "heroName": "曹操", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 14407, "skinName": "无双福将", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 9}, {"skinId": 18305, "skinName": "黎明之约", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "战士", "accessWay": "时空之境获取", "skinNum": 5}, {"skinId": 18901, "skinName": "阿摩司公爵", "heroId": "189", "heroName": "鬼谷子", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 51803, "skinName": "神威", "heroId": "518", "heroName": "马超", "classTypeName": ["史诗品质", "限定", "五虎上将"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 19404, "skinName": "千军破阵", "heroId": "194", "heroName": "苏烈", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S25赛季专属", "skinNum": 4}, {"skinId": 17301, "skinName": "特种部队", "heroId": "173", "heroName": "李元芳", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 11503, "skinName": "玩趣恶龙", "heroId": "115", "heroName": "高渐离", "classTypeName": ["勇者品质", "限定", "珍宝阁专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 19902, "skinName": "蜜橘之夏", "heroId": "199", "heroName": "公孙离", "classTypeName": ["史诗品质", "限定", "夏日海滩"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 17504, "skinName": "驱傩正仪", "heroId": "175", "heroName": "钟馗", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S24赛季专属", "skinNum": 4}, {"skinId": 53801, "skinName": "赤焰之缨", "heroId": "538", "heroName": "云缨", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 19701, "skinName": "踏雪寻梅", "heroId": "197", "heroName": "弈星", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 15605, "skinName": "缤纷绘卷", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "活动专属", "青春校园"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 16902, "skinName": "阿尔法小队", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 12701, "skinName": "冰雪圆舞曲", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 19504, "skinName": "热力回旋", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "五五朋友节", "青春校园"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 13202, "skinName": "逐梦之星", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 12102, "skinName": "大秦宣太后", "heroId": "121", "heroName": "芈月", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 6}, {"skinId": 10801, "skinName": "金属风暴", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 50403, "skinName": "胡桃异想国", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["史诗品质", "战令限定", "限定", "胡桃异想国"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 11404, "skinName": "秘密基地", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 19104, "skinName": "白蛇", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 13005, "skinName": "地狱之眼", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 53102, "skinName": "炽阳神光", "heroId": "531", "heroName": "镜", "classTypeName": ["传说品质", "FMVP"], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 52202, "skinName": "云鹰飞将", "heroId": "522", "heroName": "曜", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 50702, "skinName": "一念神魔", "heroId": "507", "heroName": "李信", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 15405, "skinName": "冠军飞将", "heroId": "154", "heroName": "花木兰", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 19302, "skinName": "曙光守护者", "heroId": "193", "heroName": "铠", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 13303, "skinName": "超时空战士", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["传说品质", "超时空小队"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 19603, "skinName": "特工魅影", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 15604, "skinName": "黄金白羊座", "heroId": "156", "heroName": "张良", "classTypeName": ["传说品质", "圣域传说"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 19802, "skinName": "胖达荣荣", "heroId": "198", "heroName": "梦奇", "classTypeName": ["传说品质", "中华曲艺"], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 14603, "skinName": "紫霞仙子", "heroId": "146", "heroName": "露娜", "classTypeName": ["史诗品质", "CP皮肤", "大话西游"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 11604, "skinName": "节奏热浪", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 51002, "skinName": "猫狗日记", "heroId": "510", "heroName": "孙策", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 11203, "skinName": "电玩小子", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "电玩狂想"], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 13301, "skinName": "锦衣卫", "heroId": "133", "heroName": "狄仁杰", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 52303, "skinName": "诗语江南", "heroId": "523", "heroName": "西施", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 15403, "skinName": "水晶猎龙者", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 17001, "skinName": "万事如意", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 14406, "skinName": "演武夺筹", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S23赛季专属", "skinNum": 9}, {"skinId": 16301, "skinName": "修罗", "heroId": "163", "heroName": "橘右京", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 13404, "skinName": "星际陆战队", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 18702, "skinName": "逐梦之光", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 19102, "skinName": "守护之力", "heroId": "191", "heroName": "大乔", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 12301, "skinName": "圣诞狂欢", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "CP皮肤", "圣诞颂歌"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 18202, "skinName": "冰霜恋舞曲", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["史诗品质", "限定", "冰雪之歌"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 12502, "skinName": "云间偶戏", "heroId": "125", "heroName": "元歌", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 11105, "skinName": "末日机甲", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["传说品质", "末日机甲"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 13901, "skinName": "潮流仙人", "heroId": "139", "heroName": "老夫子", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 19005, "skinName": "时雨天司", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["传说品质", "限定", "天文志（牛年限定）", "生肖限定"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 52201, "skinName": "归虚梦演", "heroId": "522", "heroName": "曜", "classTypeName": ["归虚梦演"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 17803, "skinName": "次元傲视", "heroId": "178", "heroName": "杨戬", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 17402, "skinName": "霸王别姬", "heroId": "174", "heroName": "虞姬", "classTypeName": ["史诗品质", "限定", "CP皮肤", "中华曲艺"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 14903, "skinName": "德古拉伯爵", "heroId": "149", "heroName": "刘邦", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 13001, "skinName": "鬼剑武藏", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 12104, "skinName": "白晶晶", "heroId": "121", "heroName": "芈月", "classTypeName": ["史诗品质", "限定", "大话西游"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13306, "skinName": "万华元夜", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S22赛季专属", "skinNum": 8}, {"skinId": 16604, "skinName": "潮玩骑士王", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质", "战令限定", "限定", "奇趣潮玩"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 10501, "skinName": "地狱岩魂", "heroId": "105", "heroName": "廉颇", "classTypeName": ["史诗品质", "地狱火"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 53701, "skinName": "启蛰", "heroId": "537", "heroName": "司空震", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 17405, "skinName": "启明星使", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族8级赠送", "skinNum": 8}, {"skinId": 13602, "skinName": "海洋之心", "heroId": "136", "heroName": "武则天", "classTypeName": ["传说品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 15202, "skinName": "偶像歌手", "heroId": "152", "heroName": "王昭君", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 16603, "skinName": "心灵战警", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 11805, "skinName": "天狼运算者", "heroId": "118", "heroName": "孙膑", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 10702, "skinName": "未来纪元", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 17102, "skinName": "乱世虎臣", "heroId": "171", "heroName": "张飞", "classTypeName": ["史诗品质", "御龙在天"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 15701, "skinName": "魅语", "heroId": "157", "heroName": "不知火舞", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 14203, "skinName": "心灵骇客", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 50602, "skinName": "纤云弄巧", "heroId": "506", "heroName": "云中君", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 11901, "skinName": "救世之瞳", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 52402, "skinName": "狂想玩偶喵", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 18903, "skinName": "原初探秘者", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S21赛季专属", "skinNum": 5}, {"skinId": 18304, "skinName": "单词大作战", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["勇者品质", "战令限定", "限定", "青春校园"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 14001, "skinName": "龙腾万里", "heroId": "140", "heroName": "关羽", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 14602, "skinName": "绯红之刃", "heroId": "146", "heroName": "露娜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 19604, "skinName": "朱雀志", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 19502, "skinName": "白虎志", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 50204, "skinName": "李小龙", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 14802, "skinName": "炽热元素使", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 14801, "skinName": "时尚教父", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 12903, "skinName": "蓝屏警告", "heroId": "129", "heroName": "典韦", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 19901, "skinName": "花间舞", "heroId": "199", "heroName": "公孙离", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 7}, {"skinId": 10906, "skinName": "时之彼端", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质", "限定", "时之际遇"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 19903, "skinName": "无限星赏官", "heroId": "199", "heroName": "公孙离", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 7}, {"skinId": 19601, "skinName": "绝影神枪", "heroId": "196", "heroName": "百里守约", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 19002, "skinName": "黄金分割率", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["史诗品质", "青春校园"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 18301, "skinName": "战争女神", "heroId": "183", "heroName": "雅典娜", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 18302, "skinName": "冰冠公主", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 19001, "skinName": "星航指挥官", "heroId": "190", "heroName": "诸葛亮", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 11002, "skinName": "暗夜贵公子", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 11004, "skinName": "白昼王子", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 50201, "skinName": "街头旋风", "heroId": "502", "heroName": "裴擒虎", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 50103, "skinName": "疑决卦", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S19赛季专属", "skinNum": 5}, {"skinId": 11107, "skinName": "时之恋人", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质", "CP皮肤", "时之际遇"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 18003, "skinName": "次元突破", "heroId": "180", "heroName": "哪吒", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 18403, "skinName": "舞动绿茵", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 7}, {"skinId": 18404, "skinName": "繁星吟游", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19101, "skinName": "伊势巫女", "heroId": "191", "heroName": "大乔", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 50102, "skinName": "虹云星官", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 19103, "skinName": "猫狗日记", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 50101, "skinName": "占星师", "heroId": "501", "heroName": "明世隐", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 11106, "skinName": "沉稳之力", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["勇者品质", "五路精神"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 19402, "skinName": "坚韧之力", "heroId": "194", "heroName": "苏烈", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 18701, "skinName": "东海龙王", "heroId": "187", "heroName": "东皇太一", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 12805, "skinName": "烛龙", "heroId": "128", "heroName": "曹操", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S10赛季专属", "skinNum": 7}, {"skinId": 11102, "skinName": "水果甜心", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 14604, "skinName": "一生所爱", "heroId": "146", "heroName": "露娜", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 12804, "skinName": "死神来了", "heroId": "128", "heroName": "曹操", "classTypeName": [], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11205, "skinName": "狮舞东方", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 11}, {"skinId": 17601, "skinName": "霓裳曲", "heroId": "176", "heroName": "杨玉环", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 17602, "skinName": "遇见飞天", "heroId": "176", "heroName": "杨玉环", "classTypeName": ["史诗品质", "限定", "周年限定", "敦煌研究院"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 17501, "skinName": "地府判官", "heroId": "175", "heroName": "钟馗", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 17502, "skinName": "神迹守卫", "heroId": "175", "heroName": "钟馗", "classTypeName": ["勇者品质", "战令限定", "限定", "神迹守卫"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 12403, "skinName": "赤莲之焰", "heroId": "124", "heroName": "周瑜", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 14106, "skinName": "猫影幻舞", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质", "FMVP皮肤"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 15301, "skinName": "隐刃", "heroId": "153", "heroName": "兰陵王", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 15302, "skinName": "暗隐猎兽者", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 15303, "skinName": "驯魔猎人", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 14103, "skinName": "仲夏夜之梦", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 16702, "skinName": "西部大镖客", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["西部大镖客"], "heroType": "刺客", "accessWay": "", "skinNum": 10}, {"skinId": 16706, "skinName": "大圣娶亲", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 50801, "skinName": "花见巫女", "heroId": "508", "heroName": "伽罗", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 50802, "skinName": "箭羽风息", "heroId": "508", "heroName": "伽罗", "classTypeName": ["史诗品质", "五五朋友节", "青春校园"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 14102, "skinName": "圣诞恋歌", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["史诗品质", "CP皮肤", "圣诞颂歌"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 50302, "skinName": "御狮", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S13赛季专属", "skinNum": 4}, {"skinId": 14101, "skinName": "异域舞娘", "heroId": "141", "heroName": "貂蝉", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 13006, "skinName": "霸王丸", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质", "SNK系列"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 14201, "skinName": "玩偶对对碰", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 10703, "skinName": "皇家上将", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "战士", "accessWay": "贵族5级赠送", "skinNum": 10}, {"skinId": 14204, "skinName": "如懿", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 17701, "skinName": "维京掠夺者", "heroId": "177", "heroName": "苍", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 17404, "skinName": "云霓雀翎", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 13101, "skinName": "范海辛", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 13102, "skinName": "千年之狐", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 17403, "skinName": "凯尔特女王", "heroId": "174", "heroName": "虞姬", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S7赛季专属", "skinNum": 8}, {"skinId": 51301, "skinName": "修竹墨客", "heroId": "513", "heroName": "上官婉儿", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 51302, "skinName": "梁祝", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "周年限定", "中华曲艺"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 51303, "skinName": "天狼绘梦者", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13701, "skinName": "魇语军师", "heroId": "137", "heroName": "司马懿", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 19501, "skinName": "威尼斯狂欢", "heroId": "195", "heroName": "百里玄策", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 15002, "skinName": "教廷特使", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "钻石夺宝获取", "skinNum": 8}, {"skinId": 19503, "skinName": "原初追逐者", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "刺客", "accessWay": "时空之境获取", "skinNum": 5}, {"skinId": 15001, "skinName": "街头霸王", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 17101, "skinName": "五福同心", "heroId": "171", "heroName": "张飞", "classTypeName": ["勇者品质", "新春专属"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 16201, "skinName": "晚萤", "heroId": "162", "heroName": "娜可露露", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 17401, "skinName": "加勒比小姐", "heroId": "174", "heroName": "虞姬", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 12604, "skinName": "朔风刀", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S17赛季专属", "skinNum": 6}, {"skinId": 51501, "skinName": "露花倒影", "heroId": "515", "heroName": "嫦娥", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 51502, "skinName": "如梦令", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 50402, "skinName": "御霄", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S14赛季专属", "skinNum": 6}, {"skinId": 15402, "skinName": "兔女郎", "heroId": "154", "heroName": "花木兰", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 12902, "skinName": "穷奇", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S12赛季专属", "skinNum": 7}, {"skinId": 50902, "skinName": "御銮", "heroId": "509", "heroName": "盾山", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S16赛季专属", "skinNum": 4}, {"skinId": 17902, "skinName": "朔望之晖", "heroId": "179", "heroName": "女娲", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 16901, "skinName": "精灵王", "heroId": "169", "heroName": "后羿", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 12304, "skinName": "猎兽之王", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 10}, {"skinId": 11605, "skinName": "迷踪丽影", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 17303, "skinName": "逐浪之夏", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 17304, "skinName": "银河之约", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "战令限定", "限定", "太空漫游"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 17901, "skinName": "尼罗河女神", "heroId": "179", "heroName": "女娲", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 3}, {"skinId": 11603, "skinName": "致命风华", "heroId": "116", "heroName": "阿轲", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 11601, "skinName": "爱心护理", "heroId": "116", "heroName": "阿轲", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 16803, "skinName": "御旌", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S15赛季专属", "skinNum": 6}, {"skinId": 50501, "skinName": "森", "heroId": "505", "heroName": "瑶", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 50502, "skinName": "遇见神鹿", "heroId": "505", "heroName": "瑶", "classTypeName": ["史诗品质", "五五朋友节", "敦煌研究院"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 19702, "skinName": "混沌棋", "heroId": "197", "heroName": "弈星", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S18赛季专属", "skinNum": 4}, {"skinId": 51101, "skinName": "年年有余", "heroId": "511", "heroName": "猪八戒", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 51102, "skinName": "西部大镖客", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["勇者品质", "战令限定", "限定", "西部大镖客"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 11304, "skinName": "奇妙博物学", "heroId": "113", "heroName": "庄周", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 11303, "skinName": "云端筑梦师", "heroId": "113", "heroName": "庄周", "classTypeName": ["史诗品质", "限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 13203, "skinName": "暗影游猎", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 50601, "skinName": "荷鲁斯之眼", "heroId": "506", "heroName": "云中君", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 12702, "skinName": "花好人间", "heroId": "127", "heroName": "甄姬", "classTypeName": ["勇者品质", "新春专属"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 12703, "skinName": "游园惊梦", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "限定", "周年限定", "中华曲艺"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 51001, "skinName": "海之征途", "heroId": "510", "heroName": "孙策", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 31201, "skinName": "棒球奇才", "heroId": "312", "heroName": "沈梦溪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 31202, "skinName": "鲨炮海盗猫", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "钻石夺宝获取", "skinNum": 7}, {"skinId": 31203, "skinName": "星空之诺", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "战令限定", "限定", "太空漫游"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 50701, "skinName": "灼热之刃", "heroId": "507", "heroName": "李信", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 18902, "skinName": "幻乐之宴", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 52301, "skinName": "归虚梦演", "heroId": "523", "heroName": "西施", "classTypeName": ["归虚梦演"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 17002, "skinName": "纽约教父", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 51801, "skinName": "幸存者", "heroId": "518", "heroName": "马超", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 11804, "skinName": "归虚梦演", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质", "活动专属", "归虚梦演"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 11802, "skinName": "天使之翼", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 17003, "skinName": "汉昭烈帝", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "御龙在天"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 11401, "skinName": "英喵野望", "heroId": "114", "heroName": "刘禅", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 15603, "skinName": "幽兰居士", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 15602, "skinName": "一千零一夜", "heroId": "156", "heroName": "张良", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 53101, "skinName": "冰刃幻境", "heroId": "531", "heroName": "镜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 13904, "skinName": "醍醐杖", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S20赛季专属", "skinNum": 6}, {"skinId": 13903, "skinName": "功夫老勺", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 18203, "skinName": "久胜战神", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["传说品质", "FMVP"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 11902, "skinName": "化身博士", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 18201, "skinName": "第七人偶", "heroId": "182", "heroName": "干将莫邪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 14901, "skinName": "圣殿之光", "heroId": "149", "heroName": "刘邦", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 18602, "skinName": "饕餮", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S9赛季专属", "skinNum": 5}, {"skinId": 14904, "skinName": "夺宝奇兵", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 13305, "skinName": "鹰眼统帅", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "信誉专属"], "heroType": "射手", "accessWay": "信誉系统专属", "skinNum": 8}, {"skinId": 13304, "skinName": "阴阳师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "限定", "成就限定"], "heroType": "射手", "accessWay": "成就系统专属", "skinNum": 8}, {"skinId": 10901, "skinName": "女仆咖啡", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 13302, "skinName": "魔术师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族6级赠送", "skinNum": 8}, {"skinId": 10605, "skinName": "丁香结", "heroId": "106", "heroName": "小乔", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 11703, "skinName": "海滩丽影", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["勇者品质", "夏日海滩"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 12003, "skinName": "星夜王子", "heroId": "120", "heroName": "白起", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 12002, "skinName": "狰", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S8赛季专属", "skinNum": 5}, {"skinId": 10604, "skinName": "缤纷独角兽", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 12103, "skinName": "重明", "heroId": "121", "heroName": "芈月", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S11赛季专属", "skinNum": 6}, {"skinId": 10602, "skinName": "天鹅之梦", "heroId": "106", "heroName": "小乔", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 12101, "skinName": "红桃皇后", "heroId": "121", "heroName": "芈月", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 10601, "skinName": "万圣前夜", "heroId": "106", "heroName": "小乔", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 15204, "skinName": "幻想奇妙夜", "heroId": "152", "heroName": "王昭君", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 15203, "skinName": "凤凰于飞", "heroId": "152", "heroName": "王昭君", "classTypeName": ["史诗品质", "限定", "CP皮肤", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 15201, "skinName": "精灵公主", "heroId": "152", "heroName": "王昭君", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 13506, "skinName": "科学大爆炸", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 13503, "skinName": "海滩派对", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质", "限定", "贵族限定", "夏日海滩"], "heroType": "坦克", "accessWay": "贵族4级赠送", "skinNum": 8}, {"skinId": 13502, "skinName": "苍穹之光", "heroId": "135", "heroName": "项羽", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 8}, {"skinId": 10902, "skinName": "魅力维加斯", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "碎片商店兑换", "skinNum": 10}, {"skinId": 10904, "skinName": "少女阿狸", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "教学关卡获取", "skinNum": 10}], "roleJobName": "永恒钻石IV", "userId": "354761129"}