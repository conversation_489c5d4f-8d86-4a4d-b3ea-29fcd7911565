<template>
  <div
    v-loading="loading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb
        style="padding: 20px 0px"
        separator-class="el-icon-arrow-right"
        class="pdTopBottom"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>立即支付</el-breadcrumb-item>
      </el-breadcrumb>
      <div v-if="!showSucc" class="pay_contain">
        <div class="spaceBetween payCon_head">
          <div>支付方式</div>
          <!-- <div class="retun_back">返回</div> -->
        </div>
        <!-- <el-button @click="mockSuc">模拟支付成功</el-button> -->
        <div class="page_comStyle">
          <div v-if="payWap == 2">
            支付宝扫码后需登录平台账号，登录后可以正常付款
          </div>
          <div class="tit_one">
            扫一扫{{ payWap == 1 ? '微信' : '支付宝' }}支付{{ assessNote }}
          </div>
          <div class="spaceCenter tit_two">
            <!-- <div style="font-size: 13px">¥ &nbsp;</div> -->
            <div>¥{{ initDate.payAmount }}</div>
          </div>
          <div class="code_wrap">
            <canvas
              id="QRCode_header_pay"
              style="width: 100%; height: 100%"
            ></canvas>
          </div>
          <div class="tit_four">支付剩余时间：{{ maxtimeMsg }}</div>
          <!-- <div class="tit_five">{{ maxtimeMsg }}</div> -->
          <div class="spaceCenter">
            <div
              :class="payWap == 1 ? 'active' : ''"
              class="payWay_item spaceCenter payWx"
              @click="payWayChoose(1)"
            >
              <!-- <img class="payWay_pic" src="../../../static/wx.png" /> -->
              <div>微信支付</div>
            </div>
            <div
              v-if="IS_OPEN_ZFB_PAY"
              :class="payWap == 2 ? 'active' : ''"
              class="payWay_item spaceCenter payZfb"
              style="margin-left: 14.56px"
              @click="payWayChoose(2)"
            >
              <!-- <img class="payWay_pic" src="../../../static/al.png" /> -->
              <div>支付宝支付</div>
            </div>
          </div>

          <!-- <router-link class="pay_now spaceCenter" to="/">返回首页</router-link> -->
        </div>
      </div>

      <!-- 发布成功弹窗 -->
      <div v-if="showSucc" class="fc succ">
        <!-- <img src="../../../static/zzsc_succ.png" class="succ-icon" /> -->
        <div style="margin-bottom: 37.46px">
          <img
            style="width: 46.39px; height: 48.814px; margin-right: 13.58px"
            src="../../../static/imgs/payOrider_success_logo_icon.png"
          />
          <img
            style="width: 214px"
            src="../../../static/imgs/payOrder_pay_success_text.svg"
            alt=""
          />
        </div>
        <!-- <div class="succ-title1">恭喜您，支付成功</div> -->
        <div class="succ-title2">
          <span class="succ-title2_label">您的订单编号：</span
          >{{ initDate.orderSn }}
          <IconFont
            :size="14"
            icon="copy"
            color="#FF720C"
            class="accountList_copy"
            @click="copyVal(initDate.productSn)"
          />
        </div>
        <div class="succ-title3">
          可前往<router-link to="/account/buyerOrder">个人中心</router-link
          >查看您的订单，联系客服后，
        </div>
        <div class="succ-title3">提供商品编号，进行账号绑定</div>
        <div class="pay_now spaceCenter ws-chat" @click="goChat">联系客服</div>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      class="payCode"
      title="扫码支付"
      width="33%"
      center
    >
      <div class="payCode_price spaceCenter">
        <div>支付金额：</div>
        <div class="payCode_priceNum" style="font-size: 12px">￥</div>
        <div class="payCode_priceNum">{{ initDate.payAmount }}</div>
      </div>
      <div class="code_wrap">
        <div class="codeIden_pic_wrap"></div>
      </div>
    </el-dialog>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import util from '@/utils/index';
import QRCode from 'qrcode';
import isLogin from '@/utils/isLogin.js';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import {
  getOrderDetail,
  getOrderTeam,
  payCheck,
  pay,
} from '@/api/confirmOrder.js';
import moment from 'moment';

const IS_OPEN_ZFB_PAY = false; // 是否开放支付宝支付

// import {
//   payOrderApi,
//   orderDetailApi,
//   orderQueryApi,
//   expireApi,
//   getAccountTxApi,
// } from '@/api/index';

export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      IS_OPEN_ZFB_PAY,
      assessNote: '',
      index: 1,
      payWap: 1, // 1 微信支付 2 支付宝支付
      loading: false,
      dialogVisible: false,
      orderId: '',
      initDate: {}, // 订单详情
      payPic: '', // 支付二维码
      maxtimeMsg: '',
      maxtime: 0, // 支付剩余秒数
      maxtimeTimer: null,
      timer: null,
      // 提交成功
      showSucc: false,
      isNeedCheck: true,
      code: '', // 订单编号
    };
  },
  mounted() {
    if (this.$route.query.from === 'myAssess') {
      this.assessNote = '定金';
    }
    if (this.$route.query.orderId) {
      this.orderId = this.$route.query.orderId;
      this.fetchData();
    }
  },
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.maxtimeTimer);
  },
  methods: {
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    goChat() {
      getOrderTeam({
        orderId: this.orderId,
      }).then((res) => {
        if (res.code === 200) {
          const { nim, store } = window.__xkit_store__;
          const imcode = res.data;
          let sessionId = `team-${imcode}`;
          let scene = `team`;
          if (!util.isNumber(imcode)) {
            sessionId = `p2p-${imcode}`;
            scene = `p2p`;
          }
          if (store.sessionStore.sessions.get(sessionId)) {
            store.uiStore.selectSession(sessionId);
          } else {
            store.sessionStore.insertSessionActive(scene, imcode);
          }
          if (scene == 'p2p') {
            this.$store.dispatch('ToggleOrderCardId', this.orderId);
          }
          this.$store.dispatch('ToggleIM', true);
        }
      });
    },
    handleClose() {
      this.payPic = '';
    },
    // 订单详情
    fetchData() {
      this.loading = true;
      getOrderDetail(this.orderId).then((response) => {
        this.loading = false;
        if (response.code == 200) {
          this.initDate = response.data;
          let { overDate } = response.data;
          overDate = moment(overDate);
          let nowDate = moment(new Date());

          this.maxtime = overDate.diff(nowDate, 'seconds');

          this.payNow();
          // 支付倒计时
          clearInterval(this.maxtimeTimer);
          this.maxtimeTimer = setInterval(() => {
            this.countTimer();
          }, 1000);

          // 查询支付结果
          clearInterval(this.timer);
        }
      });
    },
    /**
     * 立即支付
     */
    payNow() {
      this.loading = true;
      var json = {
        orderId: this.orderId,
        payType: this.payWap,
      };
      pay(json)
        .then((response) => {
          if (response.code == 200) {
            this.payPic = response.data;
            this.createQrcode();
            if (this.maxtime > 0) {
              this.timer = clearInterval(this.timer);
              this.timer = setInterval(() => {
                this.fetchQueryData();
              }, 2000);
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    createQrcode() {
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 169, //宽
        height: 169, //高
        text: this.payPic, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_pay');
      QRCode.toCanvas(msg, this.payPic, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
    // 支付方式选择
    payWayChoose(num) {
      if (this.payWap != num) {
        this.payWap = num;
        this.payNow();
      }
    },
    // 支付剩余时间倒计时
    countTimer() {
      if (this.maxtime > 0) {
        let minutes = Math.floor(this.maxtime / 60);
        let seconds = Math.floor(this.maxtime % 60);
        this.maxtimeMsg = minutes + '分' + seconds + '秒';
        --this.maxtime;
      } else {
        clearInterval(this.maxtimeTimer);
        // this.orderExpire();
        this.maxtimeMsg = '订单已过期，无法付款';

        this.$router.push({
          path: '/account/buyerOrder',
        });
      }
    },
    // 查询支付结果
    fetchQueryData() {
      payCheck({
        orderId: this.orderId,
      }).then((res) => {
        if (res.code === 200 && res.data === true) {
          clearInterval(this.timer);
          this.$message.success('支付成功！');
          getOrderTeam({
            orderId: this.orderId,
          }).finally(() => {
            this.showSucc = true;
          });
        }
      });
      // orderQueryApi({
      //   orderId: this.orderId,
      // }).then((response) => {
      //   if (response.data.status === 'TRADE_SUCCESS') {
      //     clearInterval(this.timer);
      //     this.$message.success('支付成功！');
      //     this.showSucc = true;
      //   }
      // });
    },
    // 将订单状态变为：已取消
    orderExpire() {
      // expireApi({
      //   orderId: this.orderId,
      // }).then((response) => {});
    },
  },
};
</script>

<style scoped>
.accountList_copy {
  cursor: pointer;
}
.plDt_btn {
  margin-left: 20px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.pay_contain {
  padding: 72.845px 62.32px 88.271px 62.32px;
  margin-bottom: 88.271px;
  /* padding-bottom: 88.271px; */
  background: #fff;
  border-radius: 20px;
}
.payCon_head {
  color: #ff7a00;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.retun_back {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}
.tit_one {
  color: #1b1b1b;
  padding-top: 43.707px;
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 85.714% */
  letter-spacing: 0.56px;
}
.tit_two {
  color: #ff720c;
  text-align: center;
  font-family: Inter;
  margin-top: 13.712px;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.tit_three {
  font-size: 16px;
  color: #222222;
  padding-top: 18px;
  text-align: center;
}
.tit_four {
  margin: 0 auto;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}
.tit_five {
  font-size: 16px;
  color: #ff6716;
  text-align: center;
}
.payWay_item {
  height: 42.85px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #ffddbe;
  border-radius: 51.42px;
  margin-top: 39.422px;
  box-sizing: border-box;
  cursor: pointer;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  color: #fff;
  line-height: normal;
  letter-spacing: 0.72px;
  transition: all 0.3s;
  text-align: center;
}
.payWx {
  width: 146.547px;
}
.payZfb {
  width: 161.972px;
}
.payWx.active {
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  /* background: url(../../../static/imgs/playDetail_qq_btn_bk.png); */
  /* background-size: 100% 100%; */
}
.payWx:hover {
  /* background: url(../../../static/imgs/payOrder_btn_bk_hover_189.png); */
  /* background-size: 100% 100%; */
  box-shadow: none;
  background: var(--btn-background-gradient-hover);
  /* border: none; */
}
.payZfb.active {
  /* background: url(../../../static/imgs/payOrder_btn_bk_189.png);
  background-size: 100% 100%; */
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
}
.payZfb:hover {
  /* background: url(../../../static/imgs/payOrder_btn_bk_hover_189.png);
  background-size: 100% 100%;
  border: none; */
  box-shadow: none;
  background: var(--btn-background-gradient-hover);
}
.payWay_pic {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}
.pay_now {
  margin: 30px auto 0;
  width: 146.547px;
  height: 42.85px;
  border-radius: 51.42px;
  text-align: center;
  cursor: pointer;
  letter-spacing: 0.72px;
  font-size: 15.426px;
  font-family: 'PingFang SC';
  font-weight: 500;
  color: #ffffff;
  /* background: url(../../../static/imgs/payOrder_btn_bk_189.png);
  background-size: 100% 100%; */
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
}
.payCode_price {
  background: #f7f7f7;
  border-radius: 4px;
  padding: 30px 0;
  font-size: 16px;
  font-weight: 400;
  color: #222222;
}
.payCode_priceNum {
  font-size: 20px;
  font-family: San Francisco Display;
  font-weight: bold;
  color: #f7423f;
}
.code_wrap {
  width: 171px;
  height: 171px;
  background: #ffffff;
  border: 1px solid #ffb74a;
  margin: 11.141px auto 25.71px;
  box-sizing: border-box;
}
.code_wrap img {
  width: 100%;
  height: 100%;
}
.succ-icon {
  width: 300px;
}
.succ-title1 {
  font-size: 27.424px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #f60;
  padding-bottom: 20px;
}
.succ-title2 {
  margin-bottom: 9.427px;
  color: #ff720c;
  font-family: Inter;
  font-size: 20.56px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.succ-title2_label {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 20.56px;
  letter-spacing: 0.56px;
}
.succ-title3 {
  color: rgba(0, 0, 0, 0.4);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}
.succ {
  max-width: 1200px;
  margin: 0 auto;
  /* padding-top: 30px; */
  padding: 55.12px 0px 41.135px 0px;
  position: relative;
  z-index: 2;
  margin-bottom: 84.843px;
  background: #fff;
  border-radius: 20px;
  flex-direction: column;
  text-align: center;
}
</style>

<style type="text/css">
.payCode .el-dialog__header {
  border-top: 4px solid #ff6716;
}
</style>
