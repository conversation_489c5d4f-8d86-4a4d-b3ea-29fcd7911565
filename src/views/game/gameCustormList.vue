<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="pdTopBottom">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>联系客服</el-breadcrumb-item>
        <el-breadcrumb-item>选择游戏</el-breadcrumb-item>
      </el-breadcrumb>

      <gameList @playPage="playPage">
        <template slot="top">
          <img class="guanggao" src="../../../static/n.png" />
        </template>
      </gameList>
    </div>

    <!-- 联系客服 -->
    <!-- <el-dialog
      :visible.sync="dialogVisibleInden"
      width="30%"
      center
      title="联系客服"
    >
      <div class="cusSss_item_pic">
        <el-image
          v-if="custormDate.pic"
          :src="custormDate.pic"
          style="width: 100%; height: 100%"
          fit="cover"
        ></el-image>
        <el-image
          v-else
          style="width: 100%; height: 100%"
          src="../../../static/user_default.png"
          fit="cover"
        ></el-image>
      </div>
      <div style="text-align: center">
        {{ custormDate.name }}
      </div>
      <a class="cusGo_btn ws-chat" @click="goChat">快速咨询</a>
    </el-dialog> -->

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import { getKfList, m2kfTalk, getMemberHisKFList } from '@/api/kf.js';
import gameList from '@/components/gameList/index';
import gameItem from '@/components/gameItem/index';
import cashGame from '@/components/cashGame/index';
import zimuList from '@/components/zimuList/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';

import isLogin from '@/utils/isLogin';

// import { allGameaApi } from '@/api/index';

export default {
  components: {
    gameItem,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    cashGame,
    zimuList,
    gameList,
  },
  data() {
    return {
      index: 0,
      type: '',
      custormDate: {}, // 客服数据
      dialogVisibleInden: false,
    };
  },
  mounted() {
    if (!isLogin()) {
      this.$router.push({
        path: '/login',
        query: {
          redirect: location.href,
        },
      });
      return;
    }

    // this.initGame();
  },
  methods: {
    // 获取客服
    playPage(date) {
      getMemberHisKFList({
        cateId: date.id,
      }).then((res) => {
        if (res.code == 200) {
          const findKf = res.data;
          if (findKf) {
            this.goChat(findKf, date.id);
            // this.custormDate = res.data[0];
            // this.dialogVisibleInden = true;
          } else {
            this.$store.dispatch('ToggleIM', true);
          }
        }
      });
      // getAccountTxApi({
      //   flag_id: date.id,
      // }).then((response) => {
      //   if (response.code == 200) {
      //     this.custormDate = response.data;
      //     this.dialogVisibleInden = true;
      //   }
      // });
    },
    goChat(item, productCategoryId) {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      // this.dialogVisibleInden = false;
      const { nim, store } = window.__xkit_store__;
      const imcode = item;
      const sessionId = `p2p-${imcode}`;
      m2kfTalk({
        cateId: productCategoryId,
        kfIM: imcode,
      });
      if (store.sessionStore.sessions.get(sessionId)) {
        store.uiStore.selectSession(sessionId);
      } else {
        store.sessionStore.insertSessionActive('p2p', imcode);
      }
      this.$store.dispatch('ToggleIM', true);
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 游戏-初始化
    // initGame() {
    //   allGameaApi({
    //     type: this.type,
    //     letter: this.letter == '全部游戏' ? '' : this.letter,
    //   }).then((response) => {
    //     if (response.code == 200) {
    //       this.gameList = response.data.list;
    //     }
    //   });
    // },
    // 字母筛选-子组件传递值过来
    // choseLetter(date) {
    //   this.letter = date;
    //   this.initGame();
    // },
    // 分类
    // chooseType(num) {
    //   num ? (this.type = num) : (this.type = '');
    //   this.initGame();
    // },
  },
};
</script>

<style scoped>
.gameType_wrap {
  font-size: 16px;
  color: #909090;
}
.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}
.hotGame_wrap {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 24px;
}
.gameAll_wrap {
  flex-wrap: wrap;
  align-items: flex-start;
}
.guanggao {
  cursor: pointer;
  width: 100%;
  margin-bottom: 14px;
}
.cusSss_item_pic {
  width: 132px;
  height: 132px;
  border-radius: 8px;
  overflow: hidden;
  margin: 20px auto;
}
.cusGo_btn {
  padding: 8px 0px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  margin: 0 auto;
  width: 100px;
  display: block;
  text-align: center;
  margin-top: 20px;
}
</style>
