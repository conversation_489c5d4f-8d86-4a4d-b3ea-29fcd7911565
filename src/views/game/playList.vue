<template>
  <div
    ref="mianscroll"
    class="dark_container scrollBody listContentBk"
    style="width: 100%; height: 100vh; overflow-y: scroll"
  >
    <headerKk :active-index="index" @changeproductCategoryId="changeFlagList" />
    <div :style="backgroundStr" class="paTopCom" style="margin-bottom: 80px">
      <div class="safe_width">
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          class="pdTopBottom my-bread"
          style="padding: 16px 0 20px 0px"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/gameList' }"
            >我要买</el-breadcrumb-item
          >
          <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
            jsonGame.name
          }}</el-breadcrumb-item>
        </el-breadcrumb>

        <div
          ref="offsetHeightNeed"
          class="page_comStyle searchListBox"
          style=""
        >
          <!--筛选-->
          <div v-if="singleCate && singleCate.length">
            <div v-for="(item, index) in singleCate" :key="index">
              <div v-if="item.selectType === 3">
                <div class="spaceStart playSearch_wrap">
                  <div class="playSearch_tit_top">{{ item.name }}</div>
                  <div class="spaceStart flexWrap">
                    <div
                      v-for="(iteminner, index) in item.inputList"
                      :class="iteminner.checked ? 'active' : ''"
                      :key="index"
                      class="playSearch_item"
                      @click.stop="chooseJL(iteminner, item)"
                    >
                      {{ iteminner.parent_name }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.serverDateSub.length > 0"
                  class="spaceStart playSearch_wrap"
                >
                  <div class="playSearch_tit_top">{{ getFWQ(item) }}</div>
                  <div class="spaceStart flexWrap">
                    <div
                      v-for="(iteminner, index) in item.serverDateSub"
                      :class="iteminner.checked ? 'active' : ''"
                      :key="index"
                      class="playSearch_item"
                      @click.stop="chooseJLSub(iteminner, item)"
                    >
                      {{ iteminner.parent_name }}
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-else
                :class="item.showMore ? 'choose_dl_open' : 'choose_dl_close'"
                class="choose_dl"
              >
                <div class="playSearch_tit_top" style="color: #2d2d2d">
                  {{ item.name }}
                </div>
                <div class="choose_label_box">
                  <div class="choose_label_checkbox">
                    <div
                      v-for="(iteminner, i) in item.inputList"
                      :key="i"
                      :class="iteminner.checked ? 'active' : ''"
                      class="playSearch_item"
                      @click="searchItemchange(iteminner, item)"
                    >
                      {{ fakeName(iteminner.label) }}
                    </div>
                  </div>
                  <div
                    v-if="item.inputList.length > 9"
                    class="more-btn"
                    @click="toggleMore(item)"
                  >
                    {{ item.moreTxt }}

                    <IconFont
                      v-if="item.showMore"
                      :size="18"
                      color="#FF720C"
                      icon="hide"
                    />
                    <IconFont v-else :size="18" color="#FF720C" icon="show" />
                    <!-- <i
                      :class="
                        item.showMore
                          ? 'el-icon-arrow-up'
                          : 'el-icon-arrow-down'
                      "
                    ></i> -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="groupNameCate && groupNameCate.length"
            style="margin-top: -24px"
          >
            <div
              v-for="(ele, index) in groupNameCate"
              :key="index"
              class="spaceStart hight_level_box"
            >
              <div class="playSearch_tit_top">{{ ele.nameGroup }}</div>
              <div class="spaceStart flexWrap">
                <div
                  v-for="(item, index) in ele.list4"
                  :key="index"
                  style="position: relative"
                  class="hight_level_choose"
                >
                  <!-- -->
                  <div
                    v-if="item.selectValue && item.selectValue.length > 0"
                    class="playList-multiple-value"
                  >
                    <span v-for="(chi, index) in item.selectValue" :key="index">
                      {{ chi }}
                      <span>{{
                        index < item.selectValue.length - 1 ? '/' : ''
                      }}</span>
                    </span>
                    <!-- {{ item.selectValue }} -->
                  </div>
                  <el-select
                    :class="item.filterType == 1 ? 'playList-multiple' : ''"
                    v-model="item.selectValue"
                    :placeholder="item.name"
                    :clearable="item.filterType != 1"
                    :multiple="item.filterType == 1"
                    filterable
                    style="width: 220px"
                    no-match-text="无匹配数据"
                    @change="highLevelChange"
                  >
                    <el-option
                      v-for="iteminner in item.inputList"
                      :key="iteminner.label"
                      :label="fakeName(iteminner.label)"
                      :value="iteminner.label"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div
                v-if="ele.list.length > 4"
                class="more-btn more-btn-highLevel"
                @click="toggleGroupNameCatemore(ele)"
              >
                {{ ele.groupNameCatemore ? '收起全部' : '展开全部' }}

                <IconFont
                  v-if="ele.groupNameCatemore"
                  :size="18"
                  color="#FF720C"
                  icon="hide"
                />
                <IconFont v-else :size="18" color="#FF720C" icon="show" />
                <!-- <i
                  :class="
                    ele.groupNameCatemore
                      ? 'el-icon-arrow-up'
                      : 'el-icon-arrow-down'
                  "
                  style="font-size: 22px; cursor: pointer"
                ></i> -->
              </div>
            </div>
          </div>

          <div
            v-if="highLevelCate4 && highLevelCate4.length"
            class="spaceStart hight_level_box"
          >
            <div class="playSearch_tit">高级筛选</div>
            <div class="spaceStart flexWrap">
              <div
                v-for="(item, index) in highLevelCate4"
                :key="index"
                class="hight_level_choose"
              >
                <el-select
                  v-if="item.selectType !== 3"
                  v-model="item.selectValue"
                  :placeholder="item.name"
                  :clearable="item.filterType != 1"
                  :multiple="item.filterType == 1"
                  filterable
                  style="width: 220px"
                  no-match-text="无匹配数据"
                  @change="highLevelChange"
                >
                  <el-option
                    v-for="iteminner in item.inputList"
                    :key="iteminner.label"
                    :label="fakeName(iteminner.label)"
                    :value="iteminner.label"
                  >
                  </el-option>
                </el-select>
                <!-- 下拉框级联 -->
                <el-cascader
                  v-else
                  v-model="item.selectValue"
                  :options="item.inputList"
                  :props="{
                    multiple: item.filterType === 1,
                    value: 'parent_name',
                    label: 'parent_name',
                    children: 'childList',
                    emitPath: false,
                  }"
                  :show-all-levels="false"
                  :clearable="item.filterType !== 1"
                  :placeholder="item.name"
                  class="playList-cascader"
                  style="width: 220x"
                  @change="highLevelChange"
                ></el-cascader>
              </div>
            </div>
            <div
              v-if="highLevelCate.length > 4"
              class="more-btn more-btn-highLevel"
              @click="toggleMoreHightLevel"
            >
              {{ heightLevelmore ? '收起全部' : '展开全部' }}

              <IconFont
                v-if="heightLevelmore"
                :size="18"
                color="#FF720C"
                icon="hide"
              />
              <IconFont v-else :size="18" color="#FF720C" icon="show" />
              <!-- <i
                :class="
                  heightLevelmore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                "
                style="font-size: 22px; cursor: pointer"
              ></i> -->
            </div>
          </div>

          <!--输入框筛选-->
          <div class="spaceStart flexWrap sxbox">
            <div
              v-for="(item, index) in inputCate"
              :key="index"
              class="spaceStart playSearch_wrap"
              style="
                width: 33.3333333%;
                flex-shrink: 0;
                padding-right: 20px;
                margin-bottom: 24px;
              "
            >
              <div class="playSearch_tit">{{ item.name }}</div>
              <div class="spaceStart sxboxItem">
                <el-input
                  :value="item.selectValue[0]"
                  :placeholder="`最低${item.name}`"
                  size="small"
                  style="margin-right: 9px"
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  @input="
                    (v) => {
                      updateSelectValue(index, v, 0);
                    }
                  "
                ></el-input>
                <div style="margin-right: 9px; color: rgb(153, 153, 153)">
                  -
                </div>
                <el-input
                  :value="item.selectValue[1]"
                  :placeholder="`最高${item.name}`"
                  size="small"
                  class=""
                  type="tel"
                  onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  @input="
                    (v) => {
                      updateSelectValue(index, v, 1);
                    }
                  "
                ></el-input>
              </div>
            </div>
          </div>

          <div class="keyword_box">
            <div class="spaceBetween">
              <div class="spaceStart">
                <div class="playSearch_tit">关键词</div>
                <el-input
                  v-model="keyword"
                  class="search_keyword"
                  placeholder="请输入您要查找账号/关键词"
                  @keyup.enter.native="searchListFun"
                ></el-input>
              </div>
              <div
                class="goodsItem_btn_search playListBtnBk"
                @click="shaixuanFun"
              >
                立即筛选
              </div>
            </div>
          </div>

          <!-- sort -->
          <div
            id="sort_container"
            ref="piediv"
            class="sort_container spaceBetween"
          >
            <div class="spaceStart">
              <!-- <div class="spaceStart" style="margin-right: 10px">
                <el-select
                  v-model="stockValue"
                  style="width: 80px"
                  class="zhpxbox"
                  @change="stockValueChange"
                >
                  <el-option
                    v-for="(item, index) in stockOpts"
                    :label="item.name"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</el-option
                  >
                </el-select>
              </div> -->
              <div class="spaceStart">
                <div
                  v-for="(item, index) in comprehensiveData"
                  :class="item.selected != '' ? 'active' : ''"
                  :key="index"
                  class="spaceStart sort_item"
                  @click="sortChos(item)"
                >
                  <div>{{ item.sortName }}</div>
                  <IconFont
                    v-if="item.selected == '' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="sort"
                  />

                  <IconFont
                    v-if="item.selected == 'asc' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="asc"
                  />
                  <IconFont
                    v-if="item.selected == 'desc' && item.value != ''"
                    :size="15"
                    style="margin: 0 0 0 4px"
                    icon="desc"
                  />
                </div>
              </div>
            </div>

            <div class="spaceStart">
              <div
                class="spaceStart"
                style="cursor: pointer; margin-right: 32px"
                @click="changeListStyle"
              >
                {{ lietStyleName }}
                <IconFont :size="24" style="margin-left: 3px" icon="switch" />
                <!-- <i
                  class="el-icon-sort"
                  style="transform: rotate(90deg); font-weight: 600"
                ></i> -->
              </div>
              <!-- clearSearch -->
              <div class="spaceStart cursor" @click="cleanAllChoose">
                <!-- <i class="el-icon-delete" style="font-size: 18px"></i>&nbsp; -->
                <!-- <IconFont :size="24" color="#FF720C" icon="detail" /> -->
                <div>清空筛选项</div>
                <img src="../../../static/imgs/list_detail.svg" alt="" />
                <!-- <IconFont :size="24" style="margin-left: 3px" icon="detail" /> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="safe_width">
        <el-carousel
          v-if="GameBannerList && GameBannerList.length > 0"
          :loop="true"
          height="162px"
          indicator-position="none"
          style="margin: 20px 0px; border-radius: 24px; position: relative"
        >
          <el-carousel-item
            v-for="(item, index) in GameBannerList"
            :key="index"
          >
            <div v-if="item.url" class="pageGoBtn" @click="pageGo(item)">
              点击进入
            </div>
            <el-image
              :src="item.pic"
              style="width: 100%; height: 100%; cursor: pointer"
              fit="fill"
            ></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- 列表 -->
      <div class="safe_width">
        <div class="play_List_page_comStyle">
          <!-- style="margin-bottom: 60px; padding-bottom: 40px" -->
          <div
            v-loading.fullscreen.lock="listLoading"
            element-loading-text="加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(255, 255, 255, 0.8)"
            customClass="fixeIndex"
          >
            <div v-if="accountShopList && accountShopList.length">
              <div
                v-for="(item, index) in accountShopList"
                :key="index"
                :class="isNoPic ? 'single_word' : ''"
                class="goodsList_item"
                style="position: relative"
                @click="palyPage(item)"
              >
                <div style="width: 100%" class="spaceStartNotAi">
                  <div class="goodsItem_pic">
                    <el-image
                      :src="item.pic"
                      style="width: 100%; height: 100%; border-radius: 12px"
                      fit="cover"
                    ></el-image>
                    <el-image
                      v-if="item.stock == 0 || item.stock == 1"
                      class="soled_pic"
                      style="width: 100%; height: 100%; border-radius: 12px"
                      src="../../../static/soled.jpg"
                      fit="cover"
                    ></el-image>

                    <div v-if="item.tssnum" class="tssnum">
                      <span class="innernum">
                        <img
                          style="width: 20px; height: 20px"
                          src="../../../static/imgs/tssnum_icon.svg"
                          alt=""
                        />
                        {{ item.tssnum }}天赏</span
                      >
                    </div>
                    <div
                      v-else-if="item.tagsKKList && item.tagsKKList.length"
                      class="hot_pic"
                    >
                      {{ item.tagsKKList[0] }}
                    </div>
                  </div>
                  <div
                    :class="[
                      isNoPic
                        ? 'goodsItem_center_is_pic'
                        : 'goodsItem_center_is_not_pic',
                    ]"
                    class="goodsItem_center"
                  >
                    <div>
                      <div
                        class="goodsItem_center_title"
                        style="padding-bottom: 12px"
                      >
                        【{{ item.productSn }}】
                        <span v-if="item.accountType"
                          >【{{ item.accountType }}】</span
                        >
                        <span v-if="item.careers">【{{ item.careers }}】</span>
                      </div>

                      <el-tooltip
                        v-if="isNoPic == false"
                        :visible-arrow="false"
                        popper-class="tooltip_list"
                        class="item"
                        effect="dark"
                        placement="bottom"
                      >
                        <div slot="content">
                          <div class="topTips_tit">商品详情</div>
                          <div class="topTips_tit_content">
                            <span>{{ item.productSn }}</span>
                            <span>{{ item.gameAccountQufu }}</span>
                          </div>
                          <div
                            class="topTips_con light topTips_content"
                            v-html="tedianFilter(item.subTitle, item)"
                          ></div>
                        </div>
                        <div class="topTips_con text_linTwo list_infoWord">
                          <span v-if="item.profession"
                            >【{{ item.profession }}】</span
                          >
                          <div
                            class="light goodsItem_center_content"
                            v-html="
                              tedianFilter(`账号描述：${item.subTitle}`, item)
                            "
                          ></div>
                        </div>
                      </el-tooltip>
                      <div v-else class="topTips_con">
                        <span v-if="item.profession"
                          >【{{ item.profession }}】</span
                        >
                        <div
                          class="light content_is_pic"
                          v-html="tedianFilter(item.subTitle, item)"
                        ></div>
                      </div>
                      <div class="spaceStart goodsItem_center_address">
                        <div class="spaceStart">
                          <!-- <i class="el-icon-alarm-clock" style=""></i>&nbsp; -->
                          <div style="color: #969696">发布时间：</div>
                          <div>{{ item.publishTime | formatTime }}</div>
                        </div>
                        <div class="spaceStart" style="margin-left: 32.566px">
                          <div class="spaceStart">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoReadcount || 0 }}</div>
                          </div>
                          <div class="spaceStart" style="margin-left: 18.854px">
                            <img
                              class="icon_typeS"
                              src="../../../static/home/<USER>"
                            />
                            <div>{{ item.gameSysinfoCollectcount || 0 }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 游戏名+区服 -->
                    <!-- <div class="goodsItem_center_address textOneLine">
                      {{
                        `${item.productCategoryName}|${item.gameAccountQufu}`
                      }}
                    </div> -->

                    <div
                      class="spaceEnd"
                      style="justify-content: space-between; margin-top: 9px"
                    >
                      <!-- v-if="topAccount" -->
                      <div>
                        <img
                          v-if="item.topAccount == '顶级账号'"
                          style="width: 90px; height: 30px"
                          src="../../../static/imgs/playList_topAccount.svg"
                          alt=""
                        />
                      </div>
                      <div
                        v-if="item.price && item.price != 0"
                        class="goodsItem_price"
                      >
                        ¥ {{ item.price }}
                      </div>
                      <!-- <div v-else class="goodsItem_price">联系客服</div> -->

                      <!-- <div class="goodsItem_btn">查看详情</div> -->
                    </div>
                  </div>
                </div>
                <div
                  v-if="index === accountShopList.length - 1"
                  style="
                    text-align: center;
                    padding: 36px 0px 15px 0px;
                    position: relative;
                    z-index: 99;
                  "
                  @click.stop
                >
                  <el-pagination
                    :total="totalPage"
                    style="padding: 0"
                    layout=" pager,jumper"
                    class="playList_search_page_pagination"
                    @current-change="pageFun"
                  >
                  </el-pagination>
                  <!-- layout="prev, pager, next,jumper" -->
                </div>
                <!-- <img
                  style="
                    width: 1200px;
                    height: 1px;
                    position: absolute;
                    bottom: 0px;
                    left: 0px;
                  "
                  class="playListDavie"
                  src="../../../static/imgs/divider.png"
                  alt=""
                /> -->
              </div>
            </div>
            <div v-else class="sorry">
              <img
                style="width: 54px; height: 56px"
                src="../../../static/imgs/null.png"
                alt=""
              />
              <div style="margin-left: 15.85px">
                <!-- <div class="sorry_title">抱歉..</div> -->
                <img
                  style="width: 63px; height: 36px"
                  src="../../../static/imgs/sorry_text.svg"
                  alt=""
                />
                <div class="sorry_text">暂时没有搜索到您要的账号</div>
              </div>
              <!-- 抱歉，暂时没有搜索到您要的账号 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed
      :product-category-id="productCategoryId"
      @goPageTop="backTopPage"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import { getCategoryAdverList } from '@/api/kf.js';

import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import {
  searchProductList2,
  getProductAttribute,
  getProductCategory,
} from '@/api/search.js';
// import { getBannerListApi } from '@/api/index';
import { mapState } from 'vuex';
import util from '@/utils/index';
import { size } from 'lodash';
const STOCKQUERYALL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 9,
    },
  ],
};
const STOCKQUERYOVER = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 0,
    },
  ],
};

const STOCKQUERYOL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 1,
      max: 9,
    },
  ],
};

const STOCKQRYMAP = {
  'all': STOCKQUERYALL,
  'ol': STOCKQUERYOL,
  'over': STOCKQUERYOVER,
};

const DEFSTOCKOPTS = [
  {
    name: '全部',
    id: 'all',
  },
  {
    name: '在售',
    id: 'ol',
  },
  {
    name: '已售',
    id: 'over',
  },
];

export default {
  metaInfo: {
    title: '看看账号网',
    titleTemplate: null,
  },
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
  },
  data() {
    return {
      stockOpts: DEFSTOCKOPTS,
      heightLevelmore: false,
      highLevelCate4: [],
      highLevelCate: [],
      saveCateList: [],
      cateList: [],
      ename: '',
      comprehensiveDataSort: '',
      comprehensiveDataOrder: '',
      singleCate: [],
      comprehensiveData: [],
      groupNameCate: {},
      inputCate: [],
      tinptCate: [],
      lietStyleName: '切换文字版',
      isNoPic: false, // 是否不展示图片模式
      activeNames: ['1'],
      background: '',
      backgroundImg: '',
      index: 1,
      jsonGame: {}, // 游戏展示
      totalPage: 10,
      productCategoryId: '',
      keyword: '', // 账号搜索
      clas_id: '', // 专区id
      accountShopList: [], // 账号数据
      classDate: [],
      GameBannerList: [],
      listLoading: false,
      searchParam: {
        productCategoryId: this.$route.query.productCategoryId,
        pageNum: 1,
        pageSize: 10,
      },
      stockValue: 'ol',
      stockQuery: STOCKQUERYOL,
    };
  },
  watch: {
    '$route.fullPath'() {
      this.doInit();
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    backgroundStr: function () {
      if (this.backgroundImg) {
        return (
          'background: url(' +
          this.backgroundImg +
          ') no-repeat center top / 100% auto;'
        );
      } else if (this.background) {
        return (
          'background: linear-gradient(' +
          this.background +
          ' 0%, rgb(255, 255, 255, 0) 50%);'
        );
      }
    },
  },
  created() {},
  mounted() {
    this.doInit();
  },
  methods: {
    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    getTssClazz(item) {
      let l = item.tssnum.toString().length;
      return `tssnum${l}`;
    },
    getFWQ(item) {
      let fwq = '服务器：';
      if (item.childName) {
        fwq = item.childName;
      }
      return fwq;
    },
    stockValueChange(value) {
      this.stockQuery = STOCKQRYMAP[value];
      this.searchByCate();
    },
    doInit() {
      if (this.$route.query.keyword) {
        this.keyword = this.$route.query.keyword;
      }
      if (this.$route.query.productCategoryId) {
        this.productCategoryId = this.$route.query.productCategoryId;
        this.getProductCategory();
        // 获取搜索属性后，直接进行一次搜索
        this.loadCateList().then(() => {
          this.searchByCate();
        });
      }
    },
    highLevelChange(v) {
      this.searchByCate();
    },
    toggleMoreHightLevel() {
      this.heightLevelmore = !this.heightLevelmore;
      if (this.heightLevelmore) {
        this.highLevelCate4 = this.highLevelCate.concat();
      } else {
        this.highLevelCate4 = this.highLevelCate.slice(0, 4);
      }
    },
    toggleGroupNameCatemore(ele) {
      ele.groupNameCatemore = !ele.groupNameCatemore;
      if (ele.groupNameCatemore) {
        ele.list4 = ele.list.concat();
      } else {
        ele.list4 = ele.list.slice(0, 4);
      }
    },
    // 多选展开收起
    toggleMore(item) {
      let flag = !item.showMore;
      item.moreTxt = flag ? '收起全部' : '展开全部';
      this.$set(item, 'showMore', flag);
    },
    // 输入框修改值
    updateSelectValue(index, value, i) {
      this.$set(this.inputCate[index].selectValue, i, value);
    },
    // 替换文案
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    getProductCategory() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code == 200) {
          this.jsonGame = res.data;

          this.getAd();
        }
      });
    },
    getAd() {
      const categoryId = this.jsonGame.id;
      let custom = this.jsonGame.custom || '{}';
      custom = JSON.parse(custom);
      const findKey = Object.keys(custom).find(
        (ele) => ele.indexOf('advertise') == 0
      );
      if (findKey) {
        // const type = findKey.split('_')[1];
        getCategoryAdverList({
          categoryId,
          type: 0,
        }).then((res) => {
          if (res.code == 200) {
            this.GameBannerList = res.data;
          }
        });
      }
    },
    resetPage() {
      this.searchParam.pageNum = 1;
      this.totalPage = 0;
    },
    checkInputSearch() {
      // 根据输入框设置排序规则
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((ele) => {
        ele.selected = '';
      });
      const inputHasValueList = this.inputCate.filter((ele) => {
        return (
          ele.selectValue &&
          ele.selectValue.length === 2 &&
          (ele.selectValue[0] != '' || ele.selectValue[1] != '')
        );
      });
      if (inputHasValueList.length == 1) {
        let item = inputHasValueList[0];
        let findIt;
        if (item.name == '价格') {
          findIt = this.comprehensiveData.find(
            (ele) => ele.sortName == item.name
          );
        } else {
          findIt = this.comprehensiveData.find((ele) => ele.name == item.name);
        }
        if (item.selectValue[0] != '' && item.selectValue[1] == '') {
          // 低到高
          if (findIt) {
            findIt.selected = 'asc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'asc';
          }
        } else if (item.selectValue[0] == '' && item.selectValue[1] != '') {
          if (findIt) {
            findIt.selected = 'desc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'desc';
          }
        }
      }
    },
    searchByCate(isPageNum, shaixuan) {
      if (isPageNum != 1) {
        this.resetPage();
      }
      let attrValueList = [];
      let queryIntParams = undefined;
      if (shaixuan == 'shaixuan') {
        this.checkInputSearch();
      }

      this.cateList.forEach((ele) => {
        let query = {
          ...ele,
        };
        // 单选
        query.value = query.selectValue === '不限' ? '' : query.selectValue;
        let type = this.getSearchType(query);
        if (type === 3) {
          // 级联现在最多支持2层
          if (query.searchType === 3) {
            // 高级筛选的下拉级联
            if (query.filterType === 1) {
              // 复选
              query.value = (query.selectValue || []).join(',');
            } else {
              // 单选
              query.value = query.selectValue;
            }
          } else if (query.selectValueSub) {
            // 点击的级联
            query.value = query.selectValue + '|' + query.selectValueSub;
          } else {
            query.value = query.selectValue;
          }
        } else if (type === 2 || type === 1) {
          // 多选
          if (ele.filterType === 1) {
            query.value = query.selectValue.join(',');
          } else {
            query.value = query.selectValue;
            if (query.value == '不限') {
              query.value = '';
            }
          }
        } else if (type === 4 && query.searchType === 2) {
          // 输入框范围
          let tempvalue = [...query.value];
          if (!query.ispublic) {
            if (!tempvalue[0] && !tempvalue[1]) {
              query.value = '';
            } else {
              if (tempvalue[0] || tempvalue[1]) {
                if (tempvalue[0] === '') {
                  tempvalue[0] = 0;
                }
                if (tempvalue[1] === '') {
                  tempvalue[1] = 9999999;
                }
              }
              query.value = tempvalue.join('-');
            }
          }
        }
        delete query.selectValue;
        delete query.selectValueSub;
        delete query.inputList;
        if (query.ispublic) {
          // 价格是公共属性提到queryIntParams里
          let min = query.value[0];
          let max = query.value[1];
          if (min === '' && max === '') {
            queryIntParams = undefined;
          } else {
            queryIntParams = [
              {
                min: min === '' ? undefined : parseInt(min, 10),
                key: 'price',
                max: max === '' ? undefined : parseInt(max, 10),
              },
            ];
          }
        } else {
          attrValueList.push(query);
        }
      });
      this.searchParam.productCategoryId = parseInt(this.productCategoryId, 10);

      // 属性
      let attrValueListCopy = _.cloneDeep(attrValueList);
      // 排序
      let comprehensiveData = {
        sort: this.comprehensiveDataSort,
        order: this.comprehensiveDataOrder,
      };
      let queryStrParams = [];
      // 如果是32306，有ename，放入queryStrParams里,同时属性里要剔除。否则不用管。pc端不像h5端，按属性正常处理的，不用加回来
      const findIndex = attrValueListCopy.findIndex((ele) => {
        return ele.sort === 32306 && ele.ename;
      });
      if (findIndex !== -1) {
        let findIt = attrValueListCopy[findIndex];
        if (findIt.value) {
          queryStrParams.push({
            key: findIt.ename,
            value: findIt.value,
          });
        }
        attrValueListCopy.splice(findIndex, 1);
      }

      let data = Object.assign(
        comprehensiveData,
        {
          queryStrParams,
          queryIntParams,
          keyword: this.keyword,
        },
        {
          attrValueList: attrValueListCopy,
        }
      );
      // if (this.stockQuery) {
      //   let queryIntParams = data.queryIntParams;
      //   if (queryIntParams) {
      //     data.queryIntParams = data.queryIntParams.concat(
      //       this.stockQuery.queryIntParams,
      //     );
      //   } else {
      //     data.queryIntParams = this.stockQuery.queryIntParams;
      //   }
      // }
      this.listLoading = true;

      let searchParam = _.cloneDeep(this.searchParam);
      if (this.userInfo.id) {
        // data.memberId = this.userInfo.id;
        searchParam.memberId = this.userInfo.id;
      }

      searchProductList2(searchParam, data).then((response) => {
        this.listLoading = false;
        if (response.code == 200) {
          let list = response.data.list || [];

          if (this.checkSn(list)) {
            return;
          }
          this.totalPage = response.data.total;
          this.accountShopList = list;
          this.accountShopList.forEach((ele) => {
            const findtss = ele.attrValueList.find((item) => {
              return item.name === '已使用天赏石';
            });
            const findtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用天赏石';
            });
            const findtss3 = ele.attrValueList.find((item) => {
              return item.name === '账号专区';
            });
            const findtss4 = ele.attrValueList.find((item) => {
              return item.name === '账号类型';
            });
            const findtss5 = ele.attrValueList.find((item) => {
              return item.name === '职业';
            });

            ele.tssnum = 0;
            ele.topAccount = '';
            ele.accountType = '';
            ele.careers = '';
            if (findtss) {
              ele.tssnum = findtss.intValue;
            }
            if (findtss2) {
              ele.tssnum = ele.tssnum + findtss2.intValue;
            }
            if (findtss3) {
              ele.topAccount = findtss3.value;
            }
            if (findtss4) {
              ele.accountType = findtss4.value;
            }
            if (findtss5) {
              ele.careers = findtss5.value;
            }
          });
        }
      });
    },
    checkSn(list) {
      return false;
      let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      if (
        reg.test(this.keyword) &&
        list.length === 1 &&
        this.searchParam.pageNum === 1
      ) {
        const { productCategoryId, id, productSn } = list[0];
        this.keyword = '';
        this.$router.push({
          path: `/gd/${productSn}`,
        });
        return true;
      }
      return false;
    },
    singleChange(item, itemP) {
      itemP.inputList.forEach((ele) => {
        ele.checked = false;
      });
      item.checked = true;
      itemP.selectValue = item.label;
      this.searchByCate();
    },
    searchItemchange(item, itemP) {
      // if (item.label == '在售专区') {
      //   this.stockValue = 'ol';
      //   this.stockValueChange(this.stockValue);
      // }
      // if (item.label == '已售专区') {
      //   this.stockValue = 'over';
      //   this.stockValueChange(this.stockValue);
      // }
      if (itemP.filterType === 1) {
        this.multipleChange(item, itemP);
      } else {
        this.singleChange(item, itemP);
      }
    },
    multipleChange(item, itemP) {
      this.$set(item, 'checked', !item.checked);
      if (item.label === '不限') {
        itemP.selectValue = [];
        itemP.inputList.forEach((ele, index) => {
          if (index != 0) {
            ele.checked = false;
          }
        });
      } else {
        const all = itemP.inputList.find((ele) => ele.label == '不限');
        if (all) {
          all.checked = false;
        }
        const checkedList = itemP.inputList.filter((ele) => {
          return ele.checked;
        });
        itemP.selectValue = checkedList.map((ele) => ele.label);
      }
      this.searchByCate();
    },
    getSearchType(item) {
      // 4输入框，1单选，2多选，3级联
      if (item.inputType === 0) {
        return 4;
      } else if (item.inputType === 1) {
        return item.selectType;
      }
    },
    // 搜索属性分类
    cateListSplit() {
      this.singleCate = [];
      this.highLevelCate = [];
      this.inputCate = [];
      this.groupNameCate = {};
      this.cateList.forEach((ele) => {
        const { nameGroup } = ele;
        if (nameGroup) {
          // 进分组归类
          if (ele.inputList[0].label == '不限') {
            ele.inputList.shift();
          }
          if (!this.groupNameCate[nameGroup]) {
            this.groupNameCate[nameGroup] = {
              list: [ele],
              nameGroup,
            };
          } else {
            this.groupNameCate[nameGroup].list.push(ele);
          }
        } else {
          let type = this.getSearchType(ele);
          if (type === 4) {
            this.inputCate.push(ele);
          } else {
            if (ele.searchType === 3) {
              // 进高级筛选
              if (ele.inputList[0].label == '不限') {
                ele.inputList.shift();
              }
              this.highLevelCate.push(ele);
            } else {
              if (ele.selectType === 3) {
                // 级联的需要serverDateSub
                ele.serverDateSub = [];
              }
              this.singleCate.push(ele);
            }
          }
        }
      });
      // 分组搜索
      this.groupNameCate = Object.keys(this.groupNameCate).map((key) => {
        let ele = this.groupNameCate[key];
        ele.list4 = ele.list.slice(0, 4);
        return ele;
      });
      this.highLevelCate4 = this.highLevelCate.slice(0, 4);
    },
    loadCateList() {
      return getProductAttribute(this.productCategoryId).then((response) => {
        if (response.code == 200) {
          this.cateList = response.data;
          // type排序
          // this.cateList.sort((a, b) => {
          //   return a.type - b.type;
          // });
          // 再根据sort排序
          this.cateList.sort((a, b) => {
            return b.sort - a.sort;
          });
          let list = [];
          this.cateList.forEach((ele) => {
            if (ele.type !== 0 && ele.searchType !== 0) {
              ele.moreTxt = '展开全部';
              list.push(ele);
            }
          });
          this.cateList = list;
          // this.formatServerData();
          this.formatClasData();
          this.formatCate();
          this.setZQFirst();
          return true;
        }
      });
    },
    setZQFirst() {
      // 默认在售专区
      const findIt = this.cateList.find((ele) => ele.name == '账号专区');
      if (findIt) {
        findIt.selectValue = '在售专区';
        const item = findIt.inputList.find((ele) => ele.label == '在售专区');
        item.checked = true;
      }
    },
    formatClasData() {
      this.comprehensiveData = [];
      // this.comprehensiveData.push({
      //   sortName: '综合排序',
      //   selected: '',
      //   searchSort: 'score',
      //   value: '',
      // });
      // this.comprehensiveData.push({
      //   sortName: '最多人看',
      //   selected: '',
      //   searchSort: 'gameSysinfoReadcount',
      // });
      this.comprehensiveData.push({
        sortName: '上架时间',
        selected: '',
        searchSort: 'publishTime',
      });
      this.comprehensiveData.push({
        sortName: '价格',
        selected: '',
        searchSort: 'price',
      });
      this.cateList.forEach((item) => {
        let ele = _.cloneDeep(item);
        if (ele.searchSort) {
          ele.sortName = `${ele.name}`;
          ele.selected = '';
          this.comprehensiveData.push(_.cloneDeep(ele));
        }
      });
    },
    formatInputList(ele) {
      ele.inputList = ele.inputList.split(',').map((ele) => {
        return {
          label: ele,
          checked: false,
        };
      });
    },
    // 格式化筛选条件
    formatCate() {
      // selectValue设置，输入框默认值数组2个空值，单选框默认值string，多选框默认值空数组，级联默认string
      let tempList = [
        {
          id: +new Date(),
          name: '价格', //价格补到第一个
          searchType: 2,
          selectValue: ['', ''],
          inputList: '',
          inputType: 0,
          type: 1,
          ispublic: true,
        },
      ];
      this.cateList.forEach((ele) => {
        if (ele.inputType === 0) {
          // 输入框
          this.$set(ele, 'selectValue', ['', '']);
        } else if (ele.selectType === 3) {
          const tempList = JSON.parse(ele.inputList);
          const isHighLevelSub = ele.searchType === 3; // 是高级筛选的级联

          ele.childName = tempList[0].childName;
          ele.inputList = tempList.map((ele) => {
            let childList = ele.childList.map((ele) => {
              return {
                parent_name: ele,
                checked: false,
              };
            });
            if (!isHighLevelSub) {
              childList.unshift({
                checked: false,
                parent_name: '不限',
              });
            }
            return {
              parent_name: ele.parent_name,
              checked: false,
              childList,
            };
          });
          if (!isHighLevelSub) {
            ele.inputList.unshift({
              parent_name: '不限',
              checked: false,
            });
          }
          this.$set(ele, 'selectValue', '');
        } else if (ele.selectType === 2) {
          this.formatInputList(ele);
          if (ele.searchType !== 3) {
            // 多选不是高级筛选，补一个全部方便
            // ele.inputList.unshift({
            //   label: '全部',
            //   checked: false,
            // });
          }
          let tempV = [];
          if (ele.filterType === 0) {
            // 筛选的时候要单选
            tempV = '';
          }
          this.$set(ele, 'selectValue', tempV);
        } else if (ele.selectType === 1) {
          this.formatInputList(ele);
          ele.inputList = ele.inputList.filter((item) => item.label != '未知');
          if (ele.name != '账号专区') {
            // 账号专区不要不限
            ele.inputList.unshift({
              label: '不限',
              checked: false,
            });
          }

          let tempV = [];
          if (ele.filterType === 0) {
            // 筛选的时候要单选
            tempV = '';
          }
          this.$set(ele, 'selectValue', tempV);
        }
        if (ele.name !== '未知') {
          tempList.push(ele);
        }
      });
      this.cateList = tempList;
      this.saveCateList = _.cloneDeep(this.cateList);
      this.cateListSplit();
    },
    //分割线
    changeListStyle() {
      if (this.isNoPic == false) {
        this.isNoPic = true;
        this.lietStyleName = '切换图文版';
      } else {
        this.isNoPic = false;
        this.lietStyleName = '切换文字版';
      }
    },
    // 滚动到顶部
    backTopPage() {
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 滚动回到固定位置
    scrollTop() {
      var heightNum = this.$refs.offsetHeightNeed.offsetHeight + 230;
      // var topDistance = this.$refs.piediv.getBoundingClientRect().top;
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: heightNum,
        behavior: 'smooth',
      });
    },
    // 顶部搜索框 搜索游戏ID变化
    changeFlagList(val) {
      if (val) {
        this.doInit();
      }
    },
    // 点击筛选按钮
    shaixuanFun() {
      this.searchByCate(null, 'shaixuan');
    },
    // 专区筛选
    changeClas(e) {
      this.clas_id = e;
    },
    // 大区筛选-逆水寒-单选联动
    chooseJL(date, item) {
      if (item.selectType === 3 || (date.childList && date.childList.length)) {
        // 级联区服不能多选
        item.inputList.forEach((v, i) => {
          v.checked = false;
        });
      }
      date.checked = true;
      // 切换一级，二级清空
      item.serverDateSub.forEach((v, i) => {
        v.checked = false;
      });
      item.selectValueSub = '';

      if (date.parent_name == '不限') {
        item.selectValue = '';
        item.serverDateSub = [];
      } else {
        item.selectValue = date.parent_name;
        item.serverDateSub = date.childList || [];
      }
      this.searchByCate();
    },
    // 大区筛选-逆水寒-单选联动-二级选择
    chooseJLSub(date, item) {
      item.serverDateSub.forEach((v, i) => {
        v.checked = false;
      });
      date.checked = true;
      if (date.parent_name == '不限') {
        item.selectValueSub = '';
      } else {
        item.selectValueSub = date.parent_name;
      }
      this.searchByCate();
    },
    // 筛选排序
    sortChos(date) {
      if (date.sortName == '综合排序') {
        this.comprehensiveDataOrder = '';
        this.comprehensiveDataSort = '';
        this.comprehensiveData.forEach((item, index) => {
          if (index == 0) {
            item.selected = '综合排序';
          } else {
            item.selected = '';
          }
        });
      } else {
        this.comprehensiveData.forEach((item, index) => {
          if (item.sortName == date.sortName) {
            if (date.selected == '') {
              date.selected = 'desc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'desc';
            } else if (date.selected == 'desc') {
              date.selected = 'asc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'asc';
            } else if (date.selected == 'asc') {
              date.selected = '';
              this.comprehensiveDataOrder = '';
              this.comprehensiveDataSort = '';
            }
          } else {
            item.selected = '';
          }
        });
      }
      //   this.changePageFirst();
      this.searchByCate();
    },
    // 清空所有筛选
    cleanAllChoose() {
      this.keyword = '';
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((item, index) => {
        item.selected = '';
      });
      this.cateList = _.cloneDeep(this.saveCateList);
      this.cateListSplit();
      this.serverDateSub = [];
      this.setZQFirst();
      this.searchByCate();
    },
    // 分页
    pageFun(val) {
      this.searchParam.pageNum = `${val}`;
      this.searchByCate(1);
      this.scrollTop();
    },
    // 搜索账号
    searchListFun() {
      // if (!this.keyword) {
      //   this.$message.error('请输入搜索内容');
      //   return;
      // }
      this.changePageFirst();
      this.searchByCate();
    },
    changePageFirst() {
      this.searchParam.pageNum = 1;
    },
    // 账号详情
    palyPage(date) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${date.productSn}`,
      });
      window.open(routeUrl.href, '_blank');
    },
    // 轮播跳转
    pageGo(date) {
      const { url = '' } = date;
      if (url.indexOf('http') == 0) {
        window.open(url);
      } else {
        this.$router.push({
          path: url,
        });
      }
    },
  },
};
</script>

<style type="text/css">
@import url(./playList.scss);

.light span {
  color: #fe5a1e;
}
.topSearch_clas {
  margin-right: 20px;
  border-radius: 30px;
}
.el-select.topSearch_clas .el-input__inner {
  border-radius: 10px;
  border-color: #ff6716;
  text-align: center;
  width: 150px;
}
.playSearch_wrap .el-collapse-item__arrow {
  margin-left: 10px !important;
  margin-top: 11px !important;
}
.el-collapse-item__wrap {
  overflow: visible !important;
}

.playList-multiple .el-select__tags > span {
  display: block;
  width: 200px;
  height: 36px;
  display: flex;
  align-items: center;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.playList-multiple .el-tag__close {
  display: none;
}
.playList-multiple .el-tag {
  min-width: 1px;
  background: transparent;
  border: none;
}
.playList-multiple .el-select__tags span {
  min-width: 1px;
  background: transparent;
  border: none;
}
.playList-multiple .el-select__input {
  display: none;
}
.playList-multiple .el-select__tags-text {
  overflow: visible !important;
  text-overflow: clip !important;
}
.playList-multiple-value {
  height: 34px;
  border-radius: 24px 0px 0px 24px;
  line-height: 34px;
  text-indent: 19px;
  width: 190px;
  z-index: 9;
  top: 1px;
  left: 1px;
  pointer-events: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: absolute;
  background: #fff;
  text-indent: 20px;
  color: #000;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  /* letter-spacing: 0.64px; */
}
</style>
<style lang="scss" scoped>
//下面
.sxboxItem {
  /deep/.el-input__inner {
    width: 108px;
    height: 36px;
    font-size: 14px;
    border-radius: 20px;
    border: 0.5px solid rgba(0, 0, 0, 0.1);
    background: var(--White, #fff);
    box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.05);
    letter-spacing: 0.64px;
    color: #000;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.72px;
    text-align: center;
  }
  border-radius: 30px;
  background: #f6f6f6;
}
/deep/ .zhpxbox {
  .el-input__inner {
    border: 0;
    background: #f4f4f4;
    font-size: 16px;
    color: #222;
  }
}
.stock_box {
  padding: 20px 0;
  border-bottom: 1px solid #dcdcdc;
}
.scrollBody::-webkit-scrollbar {
  width: 2px;
}
.scrollBody::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}
.playHead_wrap {
  border-bottom: 1px solid #dcdcdc;
  padding-bottom: 25px;
}

.hight_level_box {
  // margin-top: 10px;
  // margin-top: -24px;
  align-items: baseline;
  position: relative;
  .playSearch_tit {
    color: #222;
  }
}
/deep/ .hight_level_choose {
  width: 220px;
  margin: 24px 12px 0 0;
  .el-input__inner {
    height: 36px !important;
    border-radius: 20px;
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.56px;
  }
}

// .hight_level_choose:nth-child(1) {
//   margin: 0px 12px 0 0;
// }
.playSearch_wrap {
  // padding-top: 8px;
  align-items: baseline;
}
.flexWrap {
  flex-wrap: wrap;
}
.more-btn-highLevel {
  position: absolute;
  right: 0px;
  top: 32px;
  letter-spacing: 0.64px;
  color: rgba(0, 0, 0, 0.6);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}
.choose_dl {
  display: flex;
  margin-right: 5px;
  // align-items: baseline;
  align-items: baseline;
  .more-btn {
    align-items: center;
    // border: 1px solid #e5e5e5;
    // border-radius: 5px;
    // color: #999;
    // font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    text-align: right;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    margin-bottom: 20px;
    user-select: none;
    width: 90px;
  }
}
.choose_dl_close {
  // height: 62px;
  overflow: hidden;
  .choose_label_checkbox {
    // height: 53px;
  }
}
.choose_dl_open {
  height: inherit;
  .choose_label_checkbox {
    height: inherit;
  }
}
.choose_label_box {
  display: flex;
  align-items: start;
}
.choose_label_checkbox {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.playSearch_item {
  cursor: pointer;
  padding: 0px 22px;
  background: #f6f6f6;
  letter-spacing: 0.8px;
  // border: 1px solid #dcdcdc;
  border-radius: 20px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  margin-right: 12px;
  transition: all 0.3s;
  font-weight: 400;
  letter-spacing: 0.56px;
  // margin-top: 8px;
  margin-bottom: 24px;
  line-height: 36px !important;
  height: 36px !important;
  flex-shrink: 0;
}
.playSearch_item.active {
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 14px;
  background: #ff7a00;
}

.goodsItem_btn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 16px;
  color: #ffffff;
  font-family: 'PingFang SC';
  cursor: pointer;
  white-space: nowrap;
  height: 40px;
  line-height: 40px;
  padding: 0 26px;
  border-radius: 20px;
}

.chooseSelect {
  margin-right: 15px;
  margin-top: 15px;
  width: 240px;
}

.playList_icon {
  position: absolute;
  left: -160px;
  top: -20px;
}
.tagPic_acc {
  position: absolute;
  z-index: 10;
  left: 0;
  top: 0;
  width: 110px;
  height: auto;
}
.hotPic_list {
  position: absolute;
  width: 64px;
  z-index: 10;
  right: -10px;
  top: -35px;
}
.keyword_box {
  border-bottom: 0.5px solid #ff7a00;
  margin: 0 0 40px 0;
  padding-bottom: 40px;
  .search_keyword {
    width: 265px;
    margin-right: 10px;
    /deep/ .el-input__inner {
      border-radius: 50px !important;
    }
  }
}
/deep/.playList_search_page_pagination {
  position: relative;
  .el-pagination__jump {
    position: absolute;
    right: 0px;
    color: #2d2d2d;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    .el-input__inner {
      height: 20px;
      border-radius: 20px;
      border: none;
      background: #f6f6f6;
    }
  }
}
.playList_search_page_pagination /deep/ ul {
  li {
    color: rgba(0, 0, 0, 0.4);
    min-width: 24px;
    padding: 0px 12px;
  }
  .active {
    color: #2d2d2d !important;
  }
}
/*********************************************  列表模式切换  *******************************************************/
</style>
