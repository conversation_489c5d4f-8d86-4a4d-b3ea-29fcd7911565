<template>
  <div ref="bodyScroll" class="dark_container scrollPageSmoth">
    <div class="gameListBk">
      <headerKk :active-index="index" />
      <div class="safe_width">
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          style="padding: 20px 0px 17px 0px"
          class="pdTopBottom"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item class="el-bread-item">我要买</el-breadcrumb-item>
        </el-breadcrumb>
        <gameList :need-cash="true" @playPage="playPage">
          <template slot="top">
            <img class="guanggao" src="../../../static/n.png" />
          </template>
        </gameList>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
  </div>
</template>

<script>
import gameItem from '@/components/gameItem/index';
import cashGame from '@/components/cashGame/index';
import zimuList from '@/components/zimuList/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import gameList from '@/components/gameList/index';

export default {
  components: {
    gameItem,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    cashGame,
    zimuList,
    gameList,
  },
  data() {
    return {
      index: 1,
      gameList: [],
      type: '',
    };
  },
  mounted() {
    // this.initGame();
  },
  methods: {
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 游戏-初始化
    // initGame() {
    //   allGameaApi({
    //     type: this.type,
    //     letter: this.letter == '全部游戏' ? '' : this.letter,
    //   }).then((response) => {
    //     if (response.code == 200) {
    //       this.gameList = response.data.list;
    //     }
    //   });
    // },
    // 字母筛选-子组件传递值过来
    // choseLetter(date) {
    //   this.letter = date;
    //   this.initGame();
    // },
    // // 分类
    // chooseType(num) {
    //   num ? (this.type = num) : (this.type = '');
    //   this.initGame();
    // },
    // 跳转
    playPage(date) {
      this.$router.push({
        path: '/playList?productCategoryId=' + date.id,
      });
    },
  },
};
</script>

<style scoped>
.gameListBk {
  background: var(--game-list-background-gradient) !important;
  padding-bottom: 80px;
}
.gameType_wrap {
  font-size: 16px;
  color: #909090;
}
.gameType_item {
  margin-right: 50px;
  padding: 10px 0 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.gameType_item.active,
.gameType_item:hover {
  font-weight: 600;
  color: #333;
  border-bottom-color: #ff6917;
}
.hotGame_wrap {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 24px;
}
.gameAll_wrap {
  flex-wrap: wrap;
  align-items: flex-start;
}
.guanggao {
  cursor: pointer;
  width: 100%;
  margin-bottom: 14px;
}
</style>
