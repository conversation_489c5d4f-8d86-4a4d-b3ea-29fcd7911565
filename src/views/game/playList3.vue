<template>
  <div ref="mianscroll" class="dark_container scrollBody listContentBk"
    style="width: 100%; height: 100vh; overflow-y: scroll">
    <div class="playListBk">
      <headerKk :active-index="index" @changeproductCategoryId="changeFlagList" />
      <div :style="backgroundStr" class="paTopCom">
        <div class="safe_width">
          <el-breadcrumb separator-class="el-icon-arrow-right" class="pdTopBottom my-bread"
            style="padding: 16px 0 20px 0px">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/gameList' }">我要买</el-breadcrumb-item>
            <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
              jsonGame.name
              }}</el-breadcrumb-item>
          </el-breadcrumb>

          <!-- 筛选条件区域 -->
          <div ref="offsetHeightNeed" class="page_comStyle searchListBox" style="">
            <!-- 搜索选项 -->
            <el-input v-model="keyword2" placeholder="请输入内容" prefix-icon="el-icon-search"
              style="width: 321px; margin-bottom: 24px" clearable class="playList_searchh_input"
              @input="handelOptsSearch">
            </el-input>
            <div v-if="keyword2 && (!optsSearchResult || !optsSearchResult.length)" style="
                color: rgba(0, 0, 0, 0.4);
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.56px;
              ">
              暂无符合条件的筛选项
            </div>
            <div style="display: flex; align-items: center; flex-wrap: wrap">
              <div v-for="item in optsSearchResult" :key="item.value" :class="getOptIsCheck(item) ? 'active' : ''"
                class="opt-item spaceAround" @click="handelOptClick(item)">
                {{ item.value }}
              </div>
            </div>

            <!-- 复选框点击搜索区域 -->
            <CheckBoxList3 v-for="(item, index) in newCheckBoxAttrGroup" :key="item" :group-name="item" :index="index"
              :list="checkBoxAttributeList.filter((e) => e.nameGroup === item).map(item => ({...item,valueSearchType:item.valueSearchType||'must'}))" @change="handelCheckboxAttrChange"  @searchTypeClick="searchTypeClick"/>

            <div v-if="checkBoxAttributeList.length > 8" class="more_btn" style="margin: 10px auto 20px auto"
              @click="toggelOptExpand">
              {{ isExpand ? '收起' : '展开'
              }}<i :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                style="font-size: 12px; cursor: pointer"></i>
            </div>
            <div v-if="pmsSearchTags.length" class="spaceStart">
              <div class="playSearch_tit">快捷筛选</div>
              <div>
                <!-- <div v-for="item in pmsSearchTags"> -->
                  <el-checkbox-group v-model="checkedTags" @change="handleCheckedTagsChange">
                    <el-checkbox v-for="item in pmsSearchTags" :label="item.tagName" :key="item.tagName">{{item.tagName}}</el-checkbox>
                  </el-checkbox-group>
                <!-- </div> -->
              </div>
            </div>
            <!-- 输入框点击搜索区域 -->
            <!--  -->
            <div v-if="!jsonGame.goodsType">
              <div class="spaceStart flexWrap sxbox">
                <InputNumber v-for="(item, index) in inputAttributeList" :item="item" :key="index"
                  @change="(v, i) => handelInputAttrChange(index, v, i)" />
              </div>

              <div class="keyword_box">
                <div class="spaceBetween">
                  <div class="spaceStart">
                    <div class="playSearch_tit">关键词</div>
                    <el-input v-model="keyword" class="search_keyword" placeholder="请输入您要查找账号/关键词"
                      @keyup.enter.native="searchListFun"></el-input>
                  </div>
                  <div class="goodsItem_btn_search playListBtnBk" @click="shaixuanFun">
                    立即筛选
                  </div>
                </div>
              </div>
            </div>
            <!-- 道具类价格筛选 -->
            <div v-if="jsonGame.goodsType === 'vgoods'">
              <div>
                <div class="spaceBetween" style="margin-top: 20px">
                  <div class="spaceStart">
                    <InputNumber v-for="(item, index) in inputAttributeList" :item="item" :key="index"
                      style="margin-bottom: -3px" @change="(v, i) => handelInputAttrChange(index, v, i)" />
                  </div>
                  <div class="goodsItem_btn_search playListBtnBk" @click="shaixuanFun">
                    立即筛选
                  </div>
                </div>
              </div>
            </div>
            <!-- 展示您已选择 -->

            <div v-if="selectValueList.length" class="spaceStart" style="
                align-items: baseline;
                border-bottom: 0.5px solid #ff7a00;
                margin-bottom: 0px;
                padding-bottom: 20px;
                margin-top: 20px;
              ">
              <span class="playSearch_tit" style="font-weight: 600">您已选择：</span>
              <div class="spaceStart flexWrap" style="flex: 1">
                <span v-for="item in selectValueList" :key="item.value" class="opt-item" @click="handelOptClick(item)">
                  {{ item.value }}&nbsp;<i class="el-icon-close" style="font-size: 14px; cursor: pointer"></i>
                </span>
              </div>
            </div>

            <!-- sort -->
            <div id="sort_container" ref="piediv" class="sort_container spaceBetweenNoAi">
              <div class="spaceStart">
                <!-- <div class="spaceStart" style="margin-right: 10px">
                <el-select
                  v-model="stockValue"
                  style="width: 80px"
                  class="zhpxbox"
                  @change="stockValueChange"
                >
                  <el-option
                    v-for="(item, index) in stockOpts"
                    :label="item.name"
                    :value="item.id"
                    :key="index"
                    >{{ item.name }}</el-option
                  >
                </el-select>
              </div> -->
                <div class="spaceStartNotAi" style="flex-wrap: wrap">
                  <div v-for="(item, index) in comprehensiveData" :class="item.selected != '' ? 'active' : ''"
                    :key="index" class="spaceStart sort_item" @click="sortChos(item)">
                    <div>{{ item.sortName }}</div>
                    <IconFont v-if="item.selected == '' && item.value != ''" :size="15" style="margin: 0 0 0 4px"
                      icon="sort" />

                    <IconFont v-if="item.selected == 'asc' && item.value != ''" :size="15" style="margin: 0 0 0 4px"
                      icon="asc" />
                    <IconFont v-if="item.selected == 'desc' && item.value != ''" :size="15" style="margin: 0 0 0 4px"
                      icon="desc" />
                  </div>
                </div>
              </div>

              <div style="width: 290px; height: 31px" class="spaceEnd">
                <div v-if="!jsonGame.goodsType" class="spaceStart" style="cursor: pointer; margin-right: 32px"
                  @click="changeListStyle">
                  {{ lietStyleName }}
                  <IconFont :size="24" style="margin-left: 3px" icon="switch" />
                  <!-- <i
                  class="el-icon-sort"
                  style="transform: rotate(90deg); font-weight: 600"
                ></i> -->
                </div>
                <!-- clearSearch -->
                <div class="spaceStart cursor" @click="cleanAllChoose">
                  <!-- <i class="el-icon-delete" style="font-size: 18px"></i>&nbsp; -->
                  <!-- <IconFont :size="24" color="#FF720C" icon="detail" /> -->
                  <div>清空筛选项</div>
                  <img src="../../../static/imgs/list_detail.svg" alt="" />
                  <!-- <IconFont :size="24" style="margin-left: 3px" icon="detail" /> -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表 -->
        <div class="safe_width">
          <el-carousel v-if="GameBannerList && GameBannerList.length > 0" :loop="true" height="60px"
            indicator-position="none" style="margin: 20px 0px; position: relative">
            <el-carousel-item v-for="(item, index) in GameBannerList" :key="index">
              <!-- v-if="item.url" -->
              <!-- <div class="pageGoBtn" @click="pageGo(item)">点击进入</div> -->
              <el-image :src="item.pic" style="width: 100%; height: 100%; cursor: pointer" fit="fill"
                @click="pageGo(item)"></el-image>
            </el-carousel-item>
          </el-carousel>
          <div v-else style="height: 20px"></div>
        </div>
        <!-- 列表 -->
        <div  v-infinite-scroll="load" infinite-scroll-distance="200" infinite-scroll-disabled="isScrollDisabled" infinite-scroll-delay="0" class="safe_width">
          <div class="play_List_page_comStyle">
            <!-- style="margin-bottom: 60px; padding-bottom: 40px" -->
            <div v-loading.fullscreen.lock="listLoading" element-loading-text="加载中"
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"
              customClass="fixeIndex">
              <div v-if="
                jsonGame.goodsType === 'vgoods' &&
                accountShopList &&
                accountShopList.length
              ">
                <div v-for="(item, index) in accountShopList" :key="index" class="number_goodsList_item"
                  style="position: relative; width: 100%" @click="palyPage(item)">
                  <div style="width: 100%" class="spaceBetween number_goodsList_item_box">
                    <div style="width: 590px">
                      <div class="spaceAlignCenter textOneLine">
                        <div v-if="getAttrTypeTxt(item, '交易类型')" :class="getAttrTypeTxt(item, '交易类型') === '平台代发'
                          ? 'number_goodsItem_center_title_platform'
                          : ''
                          " class="number_goodsItem_center_title">
                          {{ getAttrTypeTxt(item, '交易类型') }}
                        </div>
                        <div class="number_goodsItem_center_content spaceStart" style="flex-wrap: wrap">
                          <span v-if="getAttrTypeTxt(item, '交易方式')" style="margin-left: -8px">【{{ getAttrTypeTxt(item,
                            '交易方式') }}】</span>
                          <span v-if="getAttrTypeTxt(item, '商品名称')" style="margin-left: -8px">【{{ getAttrTypeTxt(item,
                            '商品名称') }}】</span>
                          {{ getAttrTypeTxt(item, '数量')
                          }}{{ getAttrTypeTxt(item, '单位') }}={{
                            item.price
                          }}元
                        </div>
                      </div>
                      <div class="number_goodsItem_center_text">
                        游戏区服：{{ item.gameAccountQufu }}
                      </div>
                      <div style="display: flex; align-items: center" class="number_goodsItem_center_text">
                        商品编号：{{ item.productSn }}

                        <div style="
                            margin: 0 10px;
                            border-left: 1px solid #d9d9d9;
                            border-right: 1px solid #d9d9d9;
                            padding: 0 10px;
                            line-height: 12px;
                          ">
                          商品类型：{{ getAttrTypeTxt(item, '商品类型') }}
                        </div>
                        库存：<span style="color: #3399ff; font-weight: 700">{{
                          getAttrTypeTxt(item, '发布件数')
                          }}</span>
                      </div>
                    </div>
                    <div class="number_goodsItem_center_price">
                      ¥{{ item.price }}
                    </div>
                    <div style="width: 170px" v-html="getAttrTypeTxt(item, '比例说明')
                      .split('')
                      .map((char) =>
                        isNaN(char)
                          ? char
                          : `<span style='color: #FF5C00'>${char}</span>`
                      )
                      .join('')
                      "></div>
                    <div class="number_goodsItem_center_btn" @click.stop="palyPage(item)">
                      前往购买
                    </div>
                  </div>
                  <div v-if="index === accountShopList.length - 1" style="
                      text-align: center;
                      padding: 36px 0px 15px 0px;
                      position: relative;
                      z-index: 99;
                    " @click.stop>
                    <el-pagination :total="totalPage" style="padding: 0" layout=" pager,jumper"
                      class="playList_search_page_pagination" @current-change="pageFun">
                    </el-pagination>
                  </div>
                </div>
              </div>
     
              <div v-if="accountShopList && accountShopList.length">
                <div v-if="!jsonGame.goodsType">
                  <div v-for="(item, index) in accountShopList" :key="index" :class="isNoPic ? 'single_word' : ''"
                    class="goodsList_item goodsItem_pic_img" style="position: relative" @click="palyPage(item)">
                    <div style="width: 100%" class="spaceStartNotAi ">
                      <div class="goodsItem_pic ">

                        <el-image class="" :src="item.pic" @click.stop="showImagePriview(item)"
                          style="width: 100%; height: 100%; border-radius: 12px" fit="cover"></el-image>
                        <el-image v-if="item.stock == 0 || item.stock == 1" class="soled_pic "
                          @click.stop="showImagePriview(item)" style="width: 100%; height: 100%; border-radius: 12px"
                          src="../../../static/soled.jpg" fit="cover"></el-image>
                        <div @mouseover="palyDivImg(item)" @click.stop="showImagePriview(item)"
                          class="goodsItem_pic_img_box">预览</div>
                        <div v-if="item.tssnum" :class="getTssClazz(item)" class="tssnum">
                          <span class="innernum">{{ item.tssnum }}天赏</span>
                        </div>
                        <div v-if="item.zxtssnum" :class="getZxtssClazz(item)" class="zxtssnum">
                          <span class="zxinnernum">{{ item.zxtssnum }}女娲石</span>
                        </div>
                        <div v-else-if="item.tagsKKList && item.tagsKKList.length" class="hot_pic">
                          {{ item.tagsKKList[0] }}
                        </div>
                      </div>
                      <div :class="[
                        isNoPic
                          ? 'goodsItem_center_is_pic'
                          : 'goodsItem_center_is_not_pic',
                      ]" class="goodsItem_center">
                        <!-- && item.productCategoryId === 75 -->
                        <div>
                          <!-- <el-tooltip v-if="
                            isNoPic == false
                          " :visible-arrow="false" popper-class="tooltip_list" class="item" :open-delay="550"
                            effect="dark" placement="bottom" @mouseleave="palyTitleMouseleave">
                            <div slot="content">
                              <div class="topTips_tit topTips_tit_play spaceBetween">
                                <span>商品详情 ｜ {{ item.productSn }}</span>
                                <a style="font-size: 14px;color: #ff720c;" :href="`/gd/${item.productSn}`"
                                  target="_blank">查看详情</a>
                              </div>
                              <div class="topTips_con  topTips_content playListTopTips_table">
                                <el-table style="margin-top: 10px;" :data="tableData" :span-method="arraySpanMethod"
                                  border>
                                  <el-table-column prop="name" width="140">
                                    <template slot-scope="scope">
                                      <span v-html="scope.row.name"></span>
                                    </template>
                                  </el-table-column>
                                  <el-table-column prop="value">
                                    <template slot-scope="scope">
                                      <span
                                        v-html="scope.row.selectType == 2 ? scope.row.name : scope.row.value"></span>
                                    </template>
                                  </el-table-column>
                                  <div v-loading="playTableLoading" element-loading-text="加载中"
                                    element-loading-spinner="el-icon-loading"
                                    element-loading-background="rgba(255, 255, 255, 0.8)" slot="empty">

                                  </div>
                                </el-table>
                              </div>
                            </div> -->
                            <!-- @mouseenter="palyTitleMouseenter(item)"
                            @mouseleave="palyTitleMouseleave2" -->
                            <div  v-if="isNoPic == false" class="topTips_con text_linThree list_infoWord">
                              <div class="light goodsItem_center_content" style="width: 794px"
                                v-html="tedianFilter(`${item.subTitle}`, item)"></div>
                            </div>
                          <!-- </el-tooltip> -->
                          <!-- <div
                            v-if="
                              isNoPic == false && item.productCategoryId != 75
                            "
                            class="topTips_con text_linThree list_infoWord"
                          >
                            <div
                              class="light goodsItem_center_content"
                              style="width: 794px"
                              v-html="tedianFilter(`${item.subTitle}`, item)"
                            ></div>
                          </div> -->
                          <div v-if="isNoPic" class="topTips_con">
                            <div style="width: 1030px" class="light content_is_pic"
                              v-html="tedianFilter(item.subTitle, item)"></div>
                          </div>
                          <div class="attrValueListText">
                            {{ getTdTxt(item) }}
                          </div>
                          <div class="districtServer">
                            {{ item.gameAccountQufu }}
                          </div>
                          <div class="spaceStart goodsItem_center_address">
                            <div class="spaceStart">
                              <div style="color: #969696">发布时间：</div>
                              <div>{{ item.publishTime | formatTime }}</div>
                            </div>
                            <div class="spaceStart" style="margin-left: 32.566px">
                              <div class="spaceStart">
                                <img class="icon_typeS" src="../../../static/home/<USER>" />
                                <div>{{ item.gameSysinfoReadcount || 0 }}</div>
                              </div>
                              <div class="spaceStart" style="margin-left: 18.854px">
                                <img class="icon_typeS" src="../../../static/home/<USER>" />
                                <div>
                                  {{ item.gameSysinfoCollectcount || 0 }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="spaceEnd" style="
                            justify-content: space-between;
                            margin-top: 9px;
                          ">
                          <!-- v-if="topAccount" -->
                          <div>
                            <img v-if="item.topAccount == '顶级账号'" style="width: 90px; height: 30px"
                              src="../../../static/imgs/playList_topAccount.svg" alt="" />
                          </div>
                          <div v-if="item.price && item.price != 0" class="goodsItem_price">
                            ¥ {{ item.price }}
                            <div v-if="getJJPrice(item)" class="jjPrice">
                              <img src="../../../static/imgs/reducePrice.png" alt="" style="width: 18px" />已降价¥ {{
                              getJJPrice(item) }}
                            </div>
                          </div>
                          <!-- <div v-else class="goodsItem_price">联系客服</div> -->

                          <!-- <div class="goodsItem_btn">查看详情</div> -->
                        </div>
                      </div>
                    </div>
                    <!-- <div v-if="index === accountShopList.length - 1" style="
                        text-align: center;
                        padding: 36px 0px 15px 0px;
                        position: relative;
                        z-index: 99;
                      " @click.stop>
                      <el-pagination :total="totalPage" style="padding: 0" :page-size="searchParam.pageSize"
                        layout=" pager,jumper" class="playList_search_page_pagination" @current-change="pageFun">
                      </el-pagination>
                    </div> -->
                  </div>
                </div>
              </div>
              <div v-else class="sorry">
                <img style="width: 54px; height: 56px" src="../../../static/imgs/null.png" alt="" />
                <div style="margin-left: 15.85px">
                  <!-- <div class="sorry_title">抱歉..</div> -->
                  <img style="width: 63px; height: 36px" src="../../../static/imgs/sorry_text.svg" alt="" />
                  <div class="sorry_text">暂时没有搜索到您要的商品</div>
                </div>
                <!-- 抱歉，暂时没有搜索到您要的账号 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <safeNews />
    <footerKk />
    <navigation-fixed :product-category-id="searchParam.productCategoryId" @goPageTop="backTopPage" />
    <daojuNumDialog :count="daojuCount" :item="daojuDialogObj" :visible="daojuNumVisible"
      @dialogClone="daojuDialogClone" />
    <swperImagePriview :tableData="tableData" :product="productObj" :productSn="productSn" v-if="showViewer" :z-index="10000" :initial-index="imgViewer" :on-close="closeViewer"
      :url-list="arrDtPicForShow" :tableDataFlag="true" :gameSysinfoReadcount="gameSysinfoReadcount" :gameSysinfoCollectcount="gameSysinfoCollectcount" :price="price"/>
  </div>
</template>

<script>
import _ from 'lodash';
import { getCategoryAdverList2 } from '@/api/kf.js';
import daojuNumDialog from '@/components/borderDialog/daojuNumDialog.vue';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import CheckBoxList from './components/CheckBoxList.vue';
import CheckBoxList3 from './components/CheckBoxList3.vue';
import InputNumber from './components/InputNumber.vue';
import isLogin from '@/utils/isLogin';
import swperImagePriview from '@/components/imagePriview.vue'
import defaultImage from '../../../static/imgs/sl_img.png';
import {
  searchProductList2,
  getProductAttribute,
  getProductCategory,
} from '@/api/search.js';
import { getDetailByCode } from '@/api/playDetail';
// import { getBannerListApi } from '@/api/index';
import { mapState } from 'vuex';
import util from '@/utils/index';
import { re } from 'semver';
import { sort } from 'semver';
const STOCKQUERYALL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 9,
    },
  ],
};
const STOCKQUERYOVER = {
  queryIntParams: [
    {
      key: 'stock',
      min: 0,
      max: 0,
    },
  ],
};

const STOCKQUERYOL = {
  queryIntParams: [
    {
      key: 'stock',
      min: 1,
      max: 9,
    },
  ],
};

const STOCKQRYMAP = {
  'all': STOCKQUERYALL,
  'ol': STOCKQUERYOL,
  'over': STOCKQUERYOVER,
};

const DEFSTOCKOPTS = [
  {
    name: '全部',
    id: 'all',
  },
  {
    name: '在售',
    id: 'ol',
  },
  {
    name: '已售',
    id: 'over',
  },
];

export default {
  metaInfo: {
    title: '看看账号网',
    titleTemplate: null,
  },
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    CheckBoxList,
    CheckBoxList3,
    InputNumber,
    daojuNumDialog,
    swperImagePriview
  },
  data() {
    return {
      isScrollDisabled:true,
      keyword2: '',
      playTableLoading: true,
      keyword: '', // 账号搜索
      searchParam: {
        productCategoryId: this.$route.query.productCategoryId,
        pageNum: 1,
        pageSize: 10,
      },
      inputAttributeList: [],
      checkBoxAttrGroup: [], // 分组名称
      checkBoxAttributeList: [],
      isExpand: false,
      daojuDialogObj: {},
      daojuCount: 0,
      daojuNumVisible: false,
      optsSearchResult: [],

      stockOpts: DEFSTOCKOPTS,

      comprehensiveDataSort: '',
      comprehensiveDataOrder: '',
      comprehensiveData: [],

      lietStyleName: '切换文字版',
      isNoPic: false, // 是否不展示图片模式
      background: '',
      backgroundImg: '',
      index: 1,
      jsonGame: {}, // 游戏展示
      totalPage: 10,

      accountShopList: [], // 账号数据
      GameBannerList: [],
      listLoading: false,

      stockValue: 'ol',
      stockQuery: STOCKQUERYOL,
      tableData: [],
      playListTimer: null,
      showViewer: false,
      arrDtPicForShow: [],
      imgViewer: 0,
      productSn:'',
      gameSysinfoReadcount:'',
      gameSysinfoCollectcount:'',
      price:'',
      productObj:{},
      loadFlag:false,
      pmsSearchTags:[],
      checkedTags:[],
      attributeData:[],
      searchConfigList:[],
      harborConfigList:[],
    };
  },
  watch: {
    '$route.fullPath'() {
      this.doInit();
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    backgroundStr: function () {
      if (this.backgroundImg) {
        return (
          'background: url(' +
          this.backgroundImg +
          ') no-repeat center top / 100% auto;'
        );
      } else if (this.background) {
        return (
          'background: linear-gradient(' +
          this.background +
          ' 0%, rgb(255, 255, 255, 0) 50%);'
        );
      }
    },
    selectValueList() {
      let a = [];
      this.checkBoxAttributeList.forEach((e) => {
        const arr = e.selectValue.map((item) => ({
          name: e.name,
          value: item,
          parent:
            e.ename === 'gameAccountQufu' && e.selectType === 3
              ? item.split('|')[0]
              : '',
        }));
        a = a.concat(arr);
      });
      return a;
    },
    newCheckBoxAttrGroup() {
      return this.isExpand
        ? this.checkBoxAttrGroup
        : this.checkBoxAttrGroup.slice(0, 8);
    },
  },
  created() { },
  mounted() {
    this.doInit();
    
  },
  beforeDestroy() {
  
  },
  _methods: {
    // handleCheckedTagsChange(val){
    //   //如果勾选为空 则清空所有筛选内容
    //   if(!val.length){
    //     this.searchConfigList=[]
    //     this.harborConfigList=[]
    //     this.cleanAllChoose()
    //     return
    //   }
    //   // <-- 目前暂时走单选标签 -->
    //   this.searchConfigList=[]
    //   // this.checkedTags=[this.checkedTags[this.checkedTags.length-1]]
    //   this.checkedTags=[]
    //   //根据val来调整顺序
    //   let pmsSearchTags=JSON.parse(JSON.stringify(this.pmsSearchTags))
    //   pmsSearchTags.sort((a, b) => {
    //         return this.checkedTags.indexOf(a.tagName) - this.checkedTags.indexOf(b.tagName);
    //     });
    //     pmsSearchTags.forEach(item=>{
    //     let searchConditions=JSON.parse(item.searchConditions)
    //     if(this.checkedTags.includes(item.tagName)){
    //       //是否是第一次勾选 无需处理 直接覆盖
    //       if(this.searchConfigList.length==0){
    //         this.searchConfigList=searchConditions.attrValueList
    //         this.keyword=searchConditions.keyword||''
    //         return
    //       }
    //       // if(searchConditions.keyword){
    //       //   this.keyword=searchConditions.keyword||''
    //       // }
    //       //  // 非第一次勾选，按照后勾选的覆盖先勾选的逻辑处理
    //       // searchConditions.attrValueList.forEach(attrChildItem => {
    //       //   const existingItem = this.searchConfigList.find(childItem => childItem.name === attrChildItem.name);
            
    //       //   if (existingItem) {
    //       //     // 如果 valueSearchType 是 'must' 或未定义，直接覆盖（后勾选的优先级更高）
    //       //     if (!attrChildItem.valueSearchType || attrChildItem.valueSearchType === 'must') {
    //       //       existingItem.selectValue = attrChildItem.selectValue;
    //       //     } 
    //       //     // 否则合并去重（保留先勾选的值，并合并后勾选的值）
    //       //     else {
    //       //       existingItem.selectValue = [...new Set([...existingItem.selectValue, ...attrChildItem.selectValue])];
    //       //     }
    //       //   } 
    //       //   // 如果 searchConfigList 中没有这个属性，直接添加
    //       //   else {
    //       //     this.searchConfigList.push(attrChildItem);
    //       //   }
    //       // });

    //     }
    //   })
    //   //隐藏属性
    //   this.harborConfigList=[]
    //   //循环赋值给配置列表
    //     let attributeData = JSON.parse(JSON.stringify(this.attributeData));
    //     const hasPriceInOriginal = attributeData.some(item => item.name === '价格');
    //     const priceItemInSearch = this.searchConfigList.find(j => j.name === '价格');
    //     //要塞入一个价格 方便赋值
    //     if (!hasPriceInOriginal && priceItemInSearch) {
    //       attributeData.unshift({
    //         name: '价格',
    //         inputType: 0,
    //         selectValue: priceItemInSearch.selectValue
    //       });
    //     }
    //     //匹配到的就赋值 未匹配到就置空
    //     attributeData.forEach(item => {
    //       const matchedConfig = this.searchConfigList.find(j => j.name === item.name);
    //       //获取隐藏属性
    //       const allUnmatchedItems = this.searchConfigList.filter(config => {
    //         return !attributeData.some(item => item.name === config.name);
    //       });
    //       if (matchedConfig) {
    //         item.selectValue = matchedConfig.selectValue;
    //         item.valueSearchType = matchedConfig.valueSearchType;
    //       } else {
    //         item.selectValue = [];
    //       }
    //       if(allUnmatchedItems.length){
    //         this.harborConfigList=allUnmatchedItems
    //       }
    //     });
    //     console.log(attributeData)
    //     this.getSeachConfig(attributeData);
    //     this.searchByCate();
    // },
    handleCheckedTagsChange(val) {
      if (!val.length) {
        this.searchConfigList = [];
        this.harborConfigList = [];
        this.cleanAllChoose();
        return;
      }

      // 根据当前选中标签进行分类  分成唯一和不是唯一两类
      let uniqueTags = [];
      let nonUniqueTags = [];
      val.forEach(tagName => {
        const tagItem = this.pmsSearchTags.find(item => item.tagName === tagName);
        if (tagItem) {
          if (tagItem.isUnique == 1) {
            uniqueTags.push(tagName);
          } else {
            nonUniqueTags.push(tagName);
          }
        }
      });

      // 如果有多个 isUnique==1 的，只保留最后一个
      if (uniqueTags.length > 1) {
        uniqueTags = [uniqueTags[uniqueTags.length - 1]];
      }

      // 重新组装 checkedTags
      this.checkedTags = [...uniqueTags, ...nonUniqueTags];

      // 开始处理筛选逻辑
      this.searchConfigList = [];
      let pmsSearchTags = JSON.parse(JSON.stringify(this.pmsSearchTags));
      //根据val来调整顺序
      pmsSearchTags.sort((a, b) => {
        return this.checkedTags.indexOf(a.tagName) - this.checkedTags.indexOf(b.tagName);
      });
      //循环标签列表 查询当前是否选中标签 然后获取选中标签的内容
      pmsSearchTags.forEach(item => {
        let searchConditions = JSON.parse(item.searchConditions);
        if (this.checkedTags.includes(item.tagName)) {
          // 如果还没选择 直接修改内容 不走多个合并逻辑
          if (this.searchConfigList.length == 0) {
            this.searchConfigList = searchConditions.attrValueList;
            this.keyword = searchConditions.keyword || '';
          } else {
            // 合并逻辑（如果需要的话）
            searchConditions.attrValueList.forEach(attrChildItem => {
              const existingItem = this.searchConfigList.find(childItem => childItem.name === attrChildItem.name);
              //判断合并的是否和当前选中的一样 如果一样则有冲突 使用最新的覆盖上一次的  否则就是不存在冲突 直接push进去
              if (existingItem) {
                if (!attrChildItem.valueSearchType || attrChildItem.valueSearchType === 'must') {
                  existingItem.selectValue = attrChildItem.selectValue;
                } else {
                  // 否则合并去重（保留先勾选的值，并合并后勾选的值）
                  existingItem.selectValue = [...new Set([...existingItem.selectValue, ...attrChildItem.selectValue])];
                }
              } else {
                this.searchConfigList.push(attrChildItem);
              }
            });
          }
        }
      });

      // 隐藏属性处理
      this.harborConfigList = [];
      let attributeData = JSON.parse(JSON.stringify(this.attributeData));
      const hasPriceInOriginal = attributeData.some(item => item.name === '价格');
      const priceItemInSearch = this.searchConfigList.find(j => j.name === '价格');

      if (!hasPriceInOriginal && priceItemInSearch) {
        attributeData.unshift({
          name: '价格',
          inputType: 0,
          selectValue: priceItemInSearch.selectValue
        });
      }
      //匹配到的就赋值 未匹配到就置空
      attributeData.forEach(item => {
        const matchedConfig = this.searchConfigList.find(j => j.name === item.name);
        const allUnmatchedItems = this.searchConfigList.filter(config => {
          return !attributeData.some(attr => attr.name === config.name);
        });
        //获取隐藏属性
        if (matchedConfig) {
          item.selectValue = matchedConfig.selectValue;
          item.valueSearchType = matchedConfig.valueSearchType;
        } else {
          item.selectValue = [];
        }
        if (allUnmatchedItems.length) {
          this.harborConfigList = allUnmatchedItems;
        }
      });

      this.getSeachConfig(attributeData);
      this.searchByCate();
    },
    load() {
      if(this.loadFlag){
        this.searchParam.pageNum=this.searchParam.pageNum+1
        console.log(this.searchParam)
        this.searchByCate(1);
      }
     
    },
    palyDivImg(item) {
      // this.palyTitleMouseenter2(item)
    },
    showImagePriview(item) {
      getDetailByCode({ productSn: item.productSn }).then((res) => {

        const product = res.data.product
        let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : [];
        let arr = []
        if (product.albumPics) {
          arr = product.albumPics.split(',').filter(item => item.trim() !== '');
        } else {
          albumPicsJson = JSON.parse(albumPicsJson)
          albumPicsJson.forEach(item => {
            if (arr.length < 10&&item.url) {
              arr.push(item.url)
            }
          })
        }
        this.productObj=item
        this.productSn=item.productSn
        this.gameSysinfoReadcount=item.gameSysinfoReadcount
        this.gameSysinfoCollectcount=item.gameSysinfoCollectcount
        this.price=item.price
        let oldArr = this.mergeOptions(
          res.data.productAttributeList,
          res.data.productAttributeValueList
        );
        let newArr = [];
        let newArr2 = [];
        oldArr.forEach((item) => {
          if (item.selectType == 2) {
            newArr2.push(item);
          } else {
            newArr.push(item);
          }
        });

        newArr.sort((a, b) => {
          return b.type - a.type;
        });
        newArr2.sort((a, b) => {
          return b.sort - a.sort;
        });
        if (res.data.product.description) {
          newArr2.push({
            name: `【卖家说】${res.data.product.description}`,
            value: '',
            sort: 11,
            selectType: 2,
          });
        }
        let allArr = newArr.concat(newArr2);
        let searchArr = this.getSarchArr()
        allArr.forEach(item => {
          let searchName = searchArr.find((j) => {
            return j.name == item.label
          })
          // console.log(searchName,99999)

          if (searchName && searchName.value && searchName.value.length > 0) {
            let nameList = searchName.value.split(',')
            nameList.sort((a, b) => b.length - a.length);
            nameList.forEach(keyword => {
              const regex = new RegExp(`(${keyword})`, 'gi');
              if (item.selectType == 2) {
                item.name = item.name.replace(regex, '<span style="color:#ff720c">$1</span>');
              } {
                item.value = item.value.replace(regex, '<span style="color:#ff720c">$1</span>');
              }

            });
          }

        })
        // console.log(JSON.stringify(allArr),8989)
        // console.log(searchArr,7777)
        // this.tableData = newArr.concat(newArr2);
        this.tableData = allArr
        console.log(this.tableData)
        this.playTableLoading = false
        arr.unshift(defaultImage);
        this.arrDtPicForShow = arr
        this.showViewer = true
      })
      // this.arrDtPicForShow = arr
      // this.showViewer = true
    },
    closeViewer() {
      this.showViewer = false;
    },
    mergeOptions(productAttributeList, productAttributeValueList) {
      productAttributeList.sort((a, b) => {
        return a.type - b.type;
      });
      productAttributeList.sort((a, b) => {
        return b.sort - a.sort;
      });
      let tempList = [];
      productAttributeList.forEach((ele) => {
        if (ele.name == '营地ID') {
          const findIt = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          this.wzryId = findIt && findIt.value;
        }
        if (ele.type === 1 || ele.type === 2) {
          const findV = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          if (findV && findV.value) {
            tempList.push({
              name:
                ele.selectType == 2
                  ? `【${ele.name}】${this.formatValue(findV.value)}`
                  : ele.name,
              label: ele.name,
              value: this.formatValue(findV.value),
              sort: ele.sort,
              selectType: ele.selectType,
            });
          }
        }
      });
      return tempList;
    },
    formatValue(value) {
      return value
        .replace(/[,]/g, '，')
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },
    palyTitleMouseleave() {

      setTimeout(() => {
        this.playTableLoading = true
        this.tableData = []
      }, 300)
    },
    palyTitleMouseleave2() {
      clearTimeout(this.playListTimer);
    },
    palyTitleMouseenter(e) {
      this.playListTimer = setTimeout(() => {
        getDetailByCode({ productSn: e.productSn }).then((res) => {
          let oldArr = this.mergeOptions(
            res.data.productAttributeList,
            res.data.productAttributeValueList
          );
          let newArr = [];
          let newArr2 = [];
          oldArr.forEach((item) => {
            if (item.selectType == 2) {
              newArr2.push(item);
            } else {
              newArr.push(item);
            }
          });

          newArr.sort((a, b) => {
            return b.type - a.type;
          });
          newArr2.sort((a, b) => {
            return b.sort - a.sort;
          });
          if (res.data.product.description) {
            newArr2.push({
              name: `【卖家说】${res.data.product.description}`,
              value: '',
              sort: 11,
              selectType: 2,
            });
          }
          let allArr = newArr.concat(newArr2);
          let searchArr = this.getSarchArr()
          allArr.forEach(item => {
            let searchName = searchArr.find((j) => {
              return j.name == item.label
            })
            if (searchName && searchName.value && searchName.value.length > 0) {
              let nameList = searchName.value.split(',')
              nameList.sort((a, b) => b.length - a.length);
              nameList.forEach(keyword => {
                const regex = new RegExp(`(${keyword})`, 'gi');
                // const regex = new RegExp(`(?<=^|，)(${keyword})(?=$|，)`, 'g');
                if (item.selectType == 2) {
                  item.name = item.name.replace(regex, '<span style="color:#ff720c">$1</span>');
                } {
                  item.value = item.value.replace(regex, '<span style="color:#ff720c">$1</span>');
                }
              });

            }

          })
          // console.log(JSON.stringify(allArr),8989)
          // console.log(searchArr,7777)
          // this.tableData = newArr.concat(newArr2);
          this.tableData = allArr
          console.log(this.tableData)
          this.playTableLoading = false
        });
      }, 500);
    },
    palyTitleMouseenter2(e) {
      getDetailByCode({ productSn: e.productSn }).then((res) => {
        this.productSn=e.productSn
        this.gameSysinfoReadcount=e.gameSysinfoReadcount
        this.gameSysinfoCollectcount=e.gameSysinfoCollectcount
        this.price=e.price
        let oldArr = this.mergeOptions(
          res.data.productAttributeList,
          res.data.productAttributeValueList
        );
        let newArr = [];
        let newArr2 = [];
        oldArr.forEach((item) => {
          if (item.selectType == 2) {
            newArr2.push(item);
          } else {
            newArr.push(item);
          }
        });

        newArr.sort((a, b) => {
          return b.type - a.type;
        });
        newArr2.sort((a, b) => {
          return b.sort - a.sort;
        });
        if (res.data.product.description) {
          newArr2.push({
            name: `【卖家说】${res.data.product.description}`,
            value: '',
            sort: 11,
            selectType: 2,
          });
        }
        let allArr = newArr.concat(newArr2);
        let searchArr = this.getSarchArr()
        allArr.forEach(item => {
          let searchName = searchArr.find((j) => {
            return j.name == item.label
          })
          // console.log(searchName,99999)

          if (searchName && searchName.value && searchName.value.length > 0) {
            let nameList = searchName.value.split(',')
            nameList.sort((a, b) => b.length - a.length);
            nameList.forEach(keyword => {
              const regex = new RegExp(`(${keyword})`, 'gi');
              if (item.selectType == 2) {
                item.name = item.name.replace(regex, '<span style="color:#ff720c">$1</span>');
              } {
                item.value = item.value.replace(regex, '<span style="color:#ff720c">$1</span>');
              }

            });
          }

        })
        // console.log(JSON.stringify(allArr),8989)
        // console.log(searchArr,7777)
        // this.tableData = newArr.concat(newArr2);
        this.tableData = allArr
        console.log(this.tableData)
        this.playTableLoading = false
      });
    },

    getSarchArr() {
      let queryIntParams = undefined;
      const checkBoxParams = this.checkBoxAttributeList.map((e) => {
        return {
          ...e,
          value: e.selectValue.join(','),
        };
      });
      const findIndex = checkBoxParams.findIndex((ele) => {
        return ele.sort === 32306 && ele.ename;
      });
      let queryStrParams = []
      if (findIndex !== -1) {
        let { value, ename } = checkBoxParams[findIndex];
        if (value) {
          queryStrParams.push({
            key: ename,
            value,
          });
        }
        checkBoxParams.splice(findIndex, 1);
      }
      const inputParams = this.inputAttributeList
        .map((e) => {
          let [min, max] = e.selectValue || [];
          // 价格是公共属性，得放到queryIntParams
          if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
            queryIntParams = [
              {
                min: !isNaN(min) ? parseInt(min, 10) : undefined,
                key: 'price',
                max: !isNaN(max) ? parseInt(max, 10) : undefined,
              },
            ];
          }
          return {
            ...e,
            value:
              !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
          };
        })
        .filter((e) => e.name !== '价格');
      return checkBoxParams.concat(inputParams)
    },
    getUnitType() {
      let custom =
        this.opetionOtherDate0Group11.find((item) => item.name === '数量')
          .custom || '';

      let name =
        this.topList.find((item) => item.name === '商品类型').value || '';
      let newCustom = [];
      if (custom && typeof custom === 'string' && custom !== '{}') {
        newCustom = JSON.parse(custom);
      }
      return newCustom[name + 'unit'] || '';
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.selectType === 2) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
    },
    daojuDialogClone() {
      this.daojuNumVisible = false;
    },
    confirnOrder(item) {
      let pushAccountNum = this.getAttrTypeTxt(item, '发布件数');
      if (pushAccountNum == 1 || pushAccountNum <= 1) {
        if (!isLogin()) {
          this.$router.push({
            path: '/login',
            query: {
              redirect: location.href,
            },
          });
          return;
        } else {
          let routeUrl = this.$router.resolve({
            path: `/confirmOrder?productCategoryId=${item.productCategoryId}&productId=${item.id}`,
          });
          window.open(routeUrl.href, '_blank');
        }
      } else {
        this.daojuDialogObj = item;
        this.daojuCount = Number(pushAccountNum);
        this.daojuNumVisible = true;
      }
    },
    getJJPrice(item) {
      const data = JSON.parse(item.priceHistory || '[]');
      data.sort((a, b) => a.changeTime - b.changeTime);
      if (!data.length) {
        return '';
      }
      const priceDiff = data[0].price - data[data.length - 1].price;
      // 添加判断，如果价格差是负数，返回空字符串
      if (priceDiff < 0) {
        return '';
      }

      return priceDiff;
    },
    getTdTxt(item) {
      if (item.productCategoryId === 75) {
        const str1 = item.attrValueList
          .filter((ele) =>
            ['稀有外观', '天赏祥瑞', '天赏发型'].includes(ele.name)
          )
          .map((ele) => ele.value)
          .join(' ');
        const str2 = item.attrValueList
          .filter(
            (ele) =>
              ['灵韵数量', '天霓染'].includes(ele.name) &&
              ele.value &&
              ele.value !== '0'
          )
          .map((ele) => ele.name + ele.value)
          .join(' ');
        return item.description
          ? item.description
          : (str1 + ' ' + str2).replace('数量', '').replace(/,/g, ' ');
      }
      if (item.productCategoryId != 75) {
        return item.description;
      }
    },
    getAttrTypeTxt(item, str) {
      const str1 = item.attrValueList
        .filter((ele) => [str].includes(ele.name))
        .map((ele) => ele.value)
        .join(' ');
      return str1;
    },

    // 判断筛选项是否选中
    getOptIsCheck({ name, ename, selectType, parent, value }) {
      if (ename === 'gameAccountQufu' && selectType === 3) {
        value = parent + '|' + value;
      }
      return this.selectValueList.find((sV) => {
        return sV.value === value && sV.name === name;
      });
    },
    // 筛选项搜索
    handelOptsSearch(v) {
      let result = [];
      if (v) {
        this.checkBoxAttributeList.forEach((e) => {
          if (e.selectType === 3) {
            // 级联数据
            for (let key in e.optionList) {
              const childList = e.optionList[key];
              childList.forEach((child) => {
                if (child.includes(v)) {
                  result.push({
                    name: e.name,
                    parent: key,
                    value: child,
                  });
                }
              });
            }
          } else {
            result = result.concat(
              e.optionList
                .filter((opt) => opt.includes(v))
                .map((opt) => ({ name: e.name, value: opt }))
            );
          }
        });
      }
      this.optsSearchResult = result;
      console.log(v, result);
    },
    // 筛选项点击
    handelOptClick({ name, parent, value, selectType, ename }) {
      console.log('handelOptClick', name, parent, value);
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === name
      );
      const { selectValue, filterType } =
        this.checkBoxAttributeList[index] || {};

      let newV = selectValue;

      if (
        ename === 'gameAccountQufu' &&
        selectType === 3 &&
        !value.includes('|')
      ) {
        value = `${parent}|${value}`;
      }

      // 单选处理
      if (filterType !== 1) {
        if (selectValue.includes(value)) {
          newV = [];
        } else {
          newV = [value];
        }
      } else {
        // 多选处理 filterType=1 表示多选
        if (selectValue.includes(value)) {
          newV = selectValue.filter((e) => e !== value);
        } else {
          newV.push(value);
        }
      }
      this.$set(this.checkBoxAttributeList[index], 'selectValue', newV);
      this.$forceUpdate();
      this.searchByCate();
    },
    // 筛选区域展开收起
    toggelOptExpand() {
      this.isExpand = !this.isExpand;
    },
    // 筛选输入框区域值变化
    handelInputAttrChange(index, v) {
      this.$set(this.inputAttributeList[index], 'selectValue', v);
    },
    // 筛选复选框区域值变化
    handelCheckboxAttrChange(v, { name }) {
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === name
      );
      console.log('change', name, v);

      this.$set(this.checkBoxAttributeList[index], 'selectValue', v);
      this.searchByCate();
    },
    searchTypeClick(list,v){
      const index = this.checkBoxAttributeList.findIndex(
        (e) => e.name === list.name
      );
      this.$set(this.checkBoxAttributeList[index], 'valueSearchType', v);
      this.searchByCate();
    },

    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    getTssClazz(item) {
      let l = item.tssnum.toString().length;
      return `tssnum${l}`;
    },
    getZxtssClazz(item) {
      let l = item.zxtssnum.toString().length;
      return `zxtssnum${l}`;
    },
    stockValueChange(value) {
      this.stockQuery = STOCKQRYMAP[value];
      this.searchByCate();
    },
    doInit() {
      const { keyword, productCategoryId } = this.$route.query;
      if (keyword) {
        this.keyword = keyword;
      }
      if (productCategoryId) {
        // 获取游戏具体信息
        getProductCategory(productCategoryId).then((res) => {
          if (res.code == 200) {
            this.jsonGame = res.data || {};
            try {
              const custom = res.data.custom;
              this.jsonGame.goodsType =
                custom && custom !== '{}'
                  ? JSON.parse(custom).goodsType || ''
                  : '';
            } catch (e) {
              this.jsonGame.goodsType = '';
            }
            // 获取游戏bannar
            this.getGameBanner(this.jsonGame);
          }
        });

        // 获取动态配置的搜索属性后，直接进行一次搜索
        this.loadCateList(productCategoryId).then(() => {
          this.searchByCate();
        });
      }
    },
    getGameBanner({ id, custom }) {
      custom = JSON.parse(custom || '{}');
      if(custom.pcList&&custom.pcList.length){
        getCategoryAdverList2({
          // categoryId: id,
          // type: 0,
          ids:custom.pcList.join(',')
        }).then((res) => {
          if (res.code == 200) {
            this.GameBannerList = res.data;
          }
        });
      }
    },
    resetPage() {
      this.searchParam.pageNum = 1;
      this.totalPage = 0;
    },
    checkInputSearch() {
      // 根据输入框设置排序规则
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((ele) => {
        ele.selected = '';
      });
      const inputHasValueList = this.inputAttributeList.filter((ele) => {
        return (
          ele.selectValue &&
          ele.selectValue.length === 2 &&
          (ele.selectValue[0] != '' || ele.selectValue[1] != '')
        );
      });
      if (inputHasValueList.length == 1) {
        let item = inputHasValueList[0];
        let findIt;
        if (item.name == '价格') {
          findIt = this.comprehensiveData.find(
            (ele) => ele.sortName == item.name
          );
        } else {
          findIt = this.comprehensiveData.find((ele) => ele.name == item.name);
        }
        if (item.selectValue[0] != '' && item.selectValue[1] == '') {
          // 低到高
          if (findIt) {
            findIt.selected = 'asc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'asc';
          }
        } else if (item.selectValue[0] == '' && item.selectValue[1] != '') {
          if (findIt) {
            findIt.selected = 'desc';
            this.comprehensiveDataOrder = findIt.searchSort;
            this.comprehensiveDataSort = 'desc';
          }
        }
      }
    },
    searchByCate(isPageNum, shaixuan) {
      this.loadFlag=false
      if (isPageNum != 1) {
        this.resetPage();
      }

      if (shaixuan == 'shaixuan') {
        this.checkInputSearch();
      }

      // 搜集复选框搜索条件
      let checkBoxAttributeList=JSON.parse(JSON.stringify(this.checkBoxAttributeList))
      console.log(checkBoxAttributeList,2222222)
      // const checkBoxParams = this.checkBoxAttributeList.map((e) => {
        if(this.harborConfigList.length){
          let arr=this.harborConfigList.filter(item=>{
            return item.inputType!=0
          })
          checkBoxAttributeList=checkBoxAttributeList.concat(arr)
        }
        const checkBoxParams = checkBoxAttributeList.map((e) => {
        return {
          ...e,
          value: e.selectValue.join(','),
        };
      });

      // 搜集输入框条件,价格是公用条件要单独拎出
      let queryIntParams = undefined;
      let inputAttributeList=JSON.parse(JSON.stringify(this.inputAttributeList))
      if(this.harborConfigList.length){
        let arr=this.harborConfigList.filter(item=>{
          return item.inputType==0
        })
        inputAttributeList=inputAttributeList.concat(arr)
      }
      // const inputParams = this.inputAttributeList
      const inputParams =inputAttributeList
        .map((e) => {
          let [min, max] = e.selectValue || [];

          // 价格是公共属性，得放到queryIntParams
          if (e.name === '价格' && (!isNaN(min) || !isNaN(max))) {
            queryIntParams = [
              {
                min: !isNaN(min) ? parseInt(min, 10) : undefined,
                key: 'price',
                max: !isNaN(max) ? parseInt(max, 10) : undefined,
              },
            ];
          }
          return {
            ...e,
            value:
              !isNaN(min) || !isNaN(max) ? `${min || 0}-${max || 9999999}` : '',
          };
        })
        .filter((e) => e.name !== '价格');

      // 如果是32306，有ename，放入queryStrParams里,同时属性里要剔除。否则不用管。pc端不像h5端，按属性正常处理的，不用加回来
      // 比如区服的数据
      let queryStrParams = [];
      const findIndex = checkBoxParams.findIndex((ele) => {
        return ele.sort === 32306 && ele.ename;
      });


      if (findIndex !== -1) {
        let { value, ename } = checkBoxParams[findIndex];
        if (value) {
          queryStrParams.push({
            key: ename,
            value,
          });
        }
        checkBoxParams.splice(findIndex, 1);
      }


      // if (this.stockQuery) {
      //   let queryIntParams = data.queryIntParams;
      //   if (queryIntParams) {
      //     data.queryIntParams = data.queryIntParams.concat(
      //       this.stockQuery.queryIntParams,
      //     );
      //   } else {
      //     data.queryIntParams = this.stockQuery.queryIntParams;
      //   }
      // }
      this.listLoading = true;
      const searchParam = {
        ...this.searchParam, //分页等条件
        memberId: this.userInfo.id,
      };
      const params = {
        attrValueList: checkBoxParams.concat(inputParams),
        keyword: this.keyword, // 关键词搜索
        sort: this.comprehensiveDataSort,
        order: this.comprehensiveDataOrder,
        queryStrParams,
        queryIntParams, // 价格搜索条件
      };
      searchProductList2(searchParam, params).then((response) => {
        this.listLoading = false;
        if (response.code == 200) {
          let list = response.data.list || [];
          if(list.length<10){
            this.isScrollDisabled=true
          }else{
            this.isScrollDisabled=false
          }
          if (this.checkSn(list)) {
            return;
          }
          this.totalPage = response.data.total;
          if(isPageNum==1){
            this.accountShopList.push(...list)
          }else{
            this.accountShopList = list;
          }
          
          this.accountShopList.forEach((ele) => {
            const findtss = ele.attrValueList.find((item) => {
              return item.name === '已使用天赏石';
            });
            const findtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用天赏石';
            });
            const findtss3 = ele.attrValueList.find((item) => {
              return item.name === '账号专区';
            });
            const findtss4 = ele.attrValueList.find((item) => {
              return item.name === '账号类型';
            });
            const findtss5 = ele.attrValueList.find((item) => {
              return item.name === '职业';
            });
            const findtss6 = ele.attrValueList.find((item) => {
              return item.name === '稀有外观';
            });
            const findtss7 = ele.attrValueList.find((item) => {
              return item.name === '天赏祥瑞';
            });
            const findtss8 = ele.attrValueList.find((item) => {
              return item.name === '天赏发型';
            });
            const findtss9 = ele.attrValueList.find((item) => {
              return item.name === '灵韵数量';
            });
            const findtss10 = ele.attrValueList.find((item) => {
              return item.name === '天霓染';
            });
            const zxfindtss = ele.attrValueList.find((item) => {
              return item.name === '已使用女娲石数量';
            });
            const zxfindtss2 = ele.attrValueList.find((item) => {
              return item.name === '未使用女娲石数量';
            });
            // 灵韵数量
            ele.tssnum = 0;
            ele.tssnum = 0;
            ele.topAccount = '';
            ele.accountType = '';
            ele.careers = '';
            ele.appearance = '';
            ele.auspiciousSymbol = '';
            ele.hairstyle = '';
            ele.aura = '';
            ele.tianniRan = '';
            ele.zxtssnum = 0;
            if (zxfindtss && zxfindtss.intValue !== -1) {
              ele.zxtssnum = zxfindtss.intValue;
            }
            if (zxfindtss2 && zxfindtss2.intValue !== -1) {
              if (zxfindtss2.intValue === -1) {
                zxfindtss2.intValue = 0;
              }
              if (ele.zxtssnum === -1) {
                ele.zxtssnum = 0;
              }
              ele.zxtssnum = ele.zxtssnum + zxfindtss2.intValue;
            }
            if (findtss) {
              ele.tssnum = findtss.intValue;
            }
            if (findtss2) {
              ele.tssnum = ele.tssnum + findtss2.intValue;
            }
            if (findtss3) {
              ele.topAccount = findtss3.value;
            }
            if (findtss4) {
              ele.accountType = findtss4.value;
            }
            if (findtss5) {
              ele.careers = findtss5.value;
            }
            if (findtss6) {
              ele.appearance = findtss6.value;
            }
            if (findtss7) {
              ele.auspiciousSymbol = findtss7.value;
            }
            if (findtss8) {
              ele.hairstyle = findtss8.value;
            }
            if (findtss9) {
              ele.aura = findtss9.value;
            }
            if (findtss10) {
              ele.tianniRan = findtss10.value;
            }
          });
          setTimeout(()=>{
            this.loadFlag=true
          },500)
        }
      });
    },
    checkSn(list) {
      return false;
      let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      if (
        reg.test(this.keyword) &&
        list.length === 1 &&
        this.searchParam.pageNum === 1
      ) {
        const { productCategoryId, id, productSn } = list[0];
        this.keyword = '';
        this.$router.push({
          path: `/gd/${productSn}`,
        });
        return true;
      }
      return false;
    },
    getShowSearch(ele) {
      if (ele.custom && ele.custom != '{}') {
        let newCustom = JSON.parse(ele.custom || '{}');
        if (newCustom.showSearch && showSearch.showSearch !== false) {
          return false;
        } else {
          return true;
        }
      }
    },
    getTagDetailSelectValue(name,data) {
        let value=[]
        data.forEach(item=>{
          if(item.name==name){
            value=item.selectValue
          }
        })
      return value
    },
    getSeachConfig(data){
          this.attributeData=JSON.parse(JSON.stringify(data))
          
          // 根据searchSortWeight排序
          const newCateList = data
            .filter((ele) => ele.type !== 0 && ele.searchType !== 0)
            .sort((a, b) => {
              const customA = JSON.parse(a.custom || '{}');
              const customB = JSON.parse(b.custom || '{}');
              return (
                ((customB && customB.searchSortWeight) || 0) -
                ((customA && customA.searchSortWeight) || 0)
              );
            });
          const cateList = data
            .filter(
              (ele) =>
                ele.type !== 0 &&
                ele.searchType !== 0 &&
                JSON.parse(ele.custom || '{}').showSearch !== false
            )
            .sort((a, b) => {
              return b.sort - a.sort;
            });
          // 复选框属性
          const checkBoxAttrGroup = [];
          this.checkBoxAttributeList = cateList
            .filter((e) => e.inputType !== 0) // 过滤掉输入框属性
            .map((e) => {
              const {
                inputList,
                selectType,
                name,
                searchType,
                nameGroup,
                custom,
                selectValue
              } = e;

              let groupName = nameGroup || name;
              checkBoxAttrGroup.push(groupName);

              let optionList = inputList.split(','); // 子级数据
              let pOptionList = null; // 父级数据

              // 级联数据
              if (selectType === 3) {
                optionList = {};
                const treeData = JSON.parse(inputList);

                pOptionList = treeData.map((e) => e.parent_name);
                treeData.forEach((e) => {
                  const { parent_name, childList } = e;
                  optionList[parent_name] = childList;
                });
              }
              // 处理默认值
              let value = [];
              if (name === '账号专区') {
                value = ['在售专区'];
              }
              // value=this.getTagDetailSelectValue(name)||[]
              // if(this.tagDetail && Object.keys(this.tagDetail).length > 0){
              //   this.keyword=this.tagDetail.keyword
              //   this.tagDetail.attrValueList.forEach(item=>{
              //       if(item.name==name){
              //           value=item.selectValue
              //       }
              //   })
              // }
              if (name === '商品类型') {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('goodsType')) {
                  value = optionList.includes(urlParams.get('goodsType'))
                    ? [urlParams.get('goodsType')]
                    : [];
                } else if (custom && custom !== '{}') {
                  let newCustom = JSON.parse(custom);
                  value = optionList.includes(newCustom.sdefault)
                    ? [newCustom.sdefault]
                    : [];
                } else {
                  value = [optionList[0]];
                }
              }

              return {
                ...e,
                selectValue: selectValue||value,
                childValue: {}, // 只有级联数据才有
                hasClear: name != '商品类型' && searchType !== 3,
                optionList,
                pOptionList,
                defaultValue: value, // 清空选项时 可以设回初识默认值
                nameGroup: groupName,
                valueSearchType:e.valueSearchType||'must'
              };
            });

          console.log(
            'checkBoxAttrGroup',
            [...new Set(checkBoxAttrGroup)],
            this.checkBoxAttributeList
          );
          this.checkBoxAttrGroup = [...new Set(checkBoxAttrGroup)];

          // 输入框属性搜索列表
          const inputAttributeList = cateList
            .filter((e) => e.inputType === 0&&e.name!='价格')
            .map((e) => {
              return {
                ...e,
                selectValue: e.selectValue||[],
                defaultValue: [], // 清空选项时 可以设回初识默认值
              };
            });

          // 价格公共属性插入
          this.inputAttributeList = [
            {
              'name': '价格',
              'selectType': 0,
              'inputType': 0,
              'inputList': '',
              'sort': 0,
              'filterType': 0,
              'searchType': 2,
              'type': 1,
              'searchSort': 0,
              selectValue: this.getTagDetailSelectValue('价格',data)||[],
              defaultValue: [],
            },
          ].concat(inputAttributeList);

          // 排序数据
          this.comprehensiveData = [
            // {
            //   sortName: '综合排序',
            //   selected: '',
            //   searchSort: 'score',
            //   value: '',
            // },
            // {
            //   sortName: '最多人看',
            //   selected: '',
            //   searchSort: 'gameSysinfoReadcount',
            // },
            {
              sortName: '上架时间',
              selected: '',
              searchSort: 'publishTime',
            },
            {
              sortName: '价格',
              selected: '',
              searchSort: 'price',
            },
          ].concat(
            newCateList
              .filter((e) => {
                if (!e.searchSort) return false;
                const custom = e.custom ? JSON.parse(e.custom) : {};
                return Object.keys(custom).length > 0;
              })
              .map((e) => {
                return {
                  sortName: e.name,
                  selected: '',
                  searchSort: e.searchSort,
                };
              })
          );
          return true;
        
    },
    loadCateList(productCategoryId) {
      return getProductAttribute(productCategoryId).then((response) => {
        if (response.code == 200) {
          if(response.data.pmsSearchTags&&response.data.pmsSearchTags.length){
            this.pmsSearchTags=response.data.pmsSearchTags.sort((a, b) => a.sort - b.sort);
          }
          return this.getSeachConfig(response.data.productAttributeList)
        }
      });
    },
    //分割线
    changeListStyle() {
      if (this.isNoPic == false) {
        this.isNoPic = true;
        this.lietStyleName = '切换图文版';
      } else {
        this.isNoPic = false;
        this.lietStyleName = '切换文字版';
      }
    },
    // 滚动到顶部
    backTopPage() {
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 滚动回到固定位置
    scrollTop() {
      var heightNum = this.$refs.offsetHeightNeed.offsetHeight + 230;
      // var topDistance = this.$refs.piediv.getBoundingClientRect().top;
      let scrollEl = this.$refs.mianscroll;
      scrollEl.scrollTo({
        top: heightNum,
        behavior: 'smooth',
      });
    },
    // 顶部搜索框 搜索游戏ID变化
    changeFlagList(val) {
      if (val) {
        this.doInit();
      }
    },
    // 点击筛选按钮
    shaixuanFun() {
      this.searchByCate(null, 'shaixuan');
    },
    // 筛选排序
    sortChos(date) {
      if (date.sortName == '综合排序') {
        this.comprehensiveDataOrder = '';
        this.comprehensiveDataSort = '';
        this.comprehensiveData.forEach((item, index) => {
          if (index == 0) {
            item.selected = '综合排序';
          } else {
            item.selected = '';
          }
        });
      } else {
        this.comprehensiveData.forEach((item, index) => {
          if (item.sortName == date.sortName) {
            if (date.selected == '') {
              date.selected = 'desc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'desc';
            } else if (date.selected == 'desc') {
              date.selected = 'asc';
              this.comprehensiveDataOrder = date.searchSort;
              this.comprehensiveDataSort = 'asc';
            } else if (date.selected == 'asc') {
              date.selected = '';
              this.comprehensiveDataOrder = '';
              this.comprehensiveDataSort = '';
            }
          } else {
            item.selected = '';
          }
        });
      }
      //   this.changePageFirst();
      this.searchByCate();
    },
    // 清空所有筛选
    cleanAllChoose(isAxios=true) {
      this.searchConfigList=[]
      this.harborConfigList=[]
      this.checkedTags=[]
      this.keyword = '';
      this.comprehensiveDataOrder = '';
      this.comprehensiveDataSort = '';
      this.comprehensiveData.forEach((item, index) => {
        item.selected = '';
      });

      this.inputAttributeList = this.inputAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.checkBoxAttributeList = this.checkBoxAttributeList.map((e) => ({
        ...e,
        selectValue: e.defaultValue,
      }));
      this.$forceUpdate();
      
      this.serverDateSub = [];
      if(isAxios){
        this.searchByCate();
      }
      
    },
    // 分页
    pageFun(val) {
      this.searchParam.pageNum = `${val}`;
      this.searchByCate(1);
      this.scrollTop();
    },
    // 搜索账号
    searchListFun() {
      // if (!this.keyword) {
      //   this.$message.error('请输入搜索内容');
      //   return;
      // }
      this.changePageFirst();
      this.searchByCate();
    },
    changePageFirst() {
      this.searchParam.pageNum = 1;
    },
    // 账号详情
    palyPage(date) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${date.productSn}`,
      });
      window.open(routeUrl.href, '_blank');
      // this.$router.push({
      //   path: `/gd/${date.productSn}`,
      // });
    },
    // 轮播跳转
    pageGo(date) {
      const { url = '' } = date;
      if (url.indexOf('http') == 0) {
        window.open(url);
      } else {
        this.$router.push({
          path: url,
        });
      }
    },
  },
  get methods() {
    return this._methods;
  },
  set methods(value) {
    this._methods = value;
  },
};
</script>

<style type="text/css">
@import url(./playList.scss);

.playListTopTips_table {
  padding: 0px 10px;
}

.playListTopTips_table .el-loading-mask {
  position: relative
}

.playListTopTips_table .el-table__empty-text {
  line-height: 20px;
}

.playListTopTips_table thead {
  display: none;
}

.playListTopTips_table .el-table__cell {
  padding: 5px 0px;
}

.light span {
  color: #fe5a1e;
}

.el-collapse-item__wrap {
  overflow: visible !important;
}
</style>
<style lang="scss" scoped>
.scrollBody::-webkit-scrollbar {
  width: 2px;
}

.scrollBody::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}

.flexWrap {
  flex-wrap: wrap;
}

/deep/ .zhpxbox {
  .el-input__inner {
    border: 0;
    background: #f4f4f4;
    font-size: 16px;
    color: #222;
  }
}

.stock_box {
  padding: 20px 0;
  border-bottom: 1px solid #dcdcdc;
}

.scrollBody::-webkit-scrollbar {
  width: 2px;
}

.scrollBody::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}

.playHead_wrap {
  border-bottom: 1px solid #dcdcdc;
  padding-bottom: 25px;
}

.hight_level_box {
  // margin-top: 10px;
  // margin-top: -24px;
  align-items: baseline;
  position: relative;

  .playSearch_tit {
    color: #2d2d2d;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.8px;
  }
}

/deep/ .hight_level_choose {
  width: 220px;
  margin: 24px 12px 0 0;

  .el-input__inner {
    height: 36px !important;
    border-radius: 20px;
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.56px;
  }
}

.more-btn-highLevel {
  position: absolute;
  right: 0px;
  top: 32px;
  letter-spacing: 0.64px;
  color: rgba(0, 0, 0, 0.6);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
}

.choose_dl {
  display: flex;
  margin-right: 5px;
  // align-items: baseline;
  align-items: baseline;

  .more-btn {
    align-items: center;
    // border: 1px solid #e5e5e5;
    // border-radius: 5px;
    // color: #999;
    // font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    text-align: right;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    height: 32px;
    justify-content: center;
    margin-bottom: 20px;
    user-select: none;
    width: 90px;
  }
}

.choose_dl_close {
  // height: 62px;
  overflow: hidden;

  .choose_label_checkbox {
    // height: 53px;
  }
}

.choose_dl_open {
  height: inherit;

  .choose_label_checkbox {
    height: inherit;
  }
}

.choose_label_box {
  display: flex;
  align-items: start;
}

.choose_label_checkbox {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.goodsItem_btn {
  background: linear-gradient(90deg, #ff9600, #ff6700);
  font-size: 16px;
  color: #ffffff;
  font-family: 'PingFang SC';
  cursor: pointer;
  white-space: nowrap;
  height: 40px;
  line-height: 40px;
  padding: 0 26px;
  border-radius: 20px;
}

.chooseSelect {
  margin-right: 15px;
  margin-top: 15px;
  width: 240px;
}

.playList_icon {
  position: absolute;
  left: -160px;
  top: -20px;
}

.tagPic_acc {
  position: absolute;
  z-index: 10;
  left: 0;
  top: 0;
  width: 110px;
  height: auto;
}

.hotPic_list {
  position: absolute;
  width: 64px;
  z-index: 10;
  right: -10px;
  top: -35px;
}

.keyword_box {
  border-bottom: 0.5px solid #ff7a00;
  // margin: 0 0 20px 0;
  padding-bottom: 20px;

  .search_keyword {
    width: 265px;
    margin-right: 10px;

    /deep/ .el-input__inner {
      border-radius: 50px !important;
    }
  }
}

/deep/.playList_search_page_pagination {
  position: relative;

  .el-pagination__jump {
    position: absolute;
    right: 0px;
    color: #2d2d2d;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;

    .el-input__inner {
      height: 20px;
      border-radius: 20px;
      border: none;
      background: #f6f6f6;
    }
  }
}

.playList_search_page_pagination /deep/ ul {
  li {
    color: rgba(0, 0, 0, 0.4);
    min-width: 24px;
    padding: 0px 7px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;

    letter-spacing: 0.64px;
  }

  .active {
    color: #2d2d2d !important;
  }
}

/*********************************************  列表模式切换  *******************************************************/

.more_btn {
  width: 60px;
  cursor: pointer;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  text-align: center;
  border-radius: 60px;
  padding: 6px 0;
  font-size: 12px;

  &:hover {
    background: #f7f7f7;
  }
}

.opt-item {
  cursor: pointer;
  padding: 0px 22px;
  background: #f6f6f6;
  border-radius: 20px;
  margin-right: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
  line-height: 30px !important;
  height: 36px !important;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.56px;
  display: flex;
  align-items: center;

  &.active {
    background: #fff4ee;
    color: #ff6716;
    border-color: #fff4ee;
  }
}

.attrValueListText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 794px;
  font-family: 'PingFang SC';
  font-size: 14px;
  margin-top: 12px;
  color: #ff720c;
}

.districtServer {
  font-family: 'PingFang SC';
  font-size: 14px;
  color: #9a9a9a;
  font-weight: 400;
}

.playList_searchh_input {
  border-radius: 20px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.05);
}

.playList_searchh_input /deep/ .el-input__inner {
  border-radius: 20px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
}
</style>
