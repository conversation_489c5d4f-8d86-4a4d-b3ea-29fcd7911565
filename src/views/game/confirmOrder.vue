<template>
  <div
    v-loading="loading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="background: linear-gradient(180deg, #fdf5ed 0%, #fff 200%)"
  >
    <headerKk :active-index="index" />

    <div class="safe_width">
      <el-breadcrumb
        separator-class="el-icon-arrow-right"
        style="padding: 20px 0px"
        class="pdTopBottom my-bread"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/gameList' }"
          >我要买</el-breadcrumb-item
        >
        <!-- <el-breadcrumb-item>确认订单</el-breadcrumb-item> -->
        <el-breadcrumb-item
          :to="{
            path: `/playList?productCategoryId=${productCategoryId}`,
          }"
          >{{ gameDt.productBrand }}</el-breadcrumb-item
        >
        <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
          gameDt.productSn
        }}</el-breadcrumb-item>
      </el-breadcrumb>

      <div
        style="border-radius: 24px; padding: 40px 40px 46px 40px"
        class="page_comStyle"
      >
        <div class="main_tit">确认订单信息</div>
        <div>
          <div class="table_self">
            <div class="spaceBetweenNoAi table_header">
              <div style="text-indent: 56px" class="table_one">商品信息</div>
              <!-- style="margin-left: 114px" -->
              <div class="table_two">价格（¥）</div>
              <div style="margin-right: 46px" class="table_three">编号</div>
            </div>
            <div class="spaceBetween table_body">
              <div class="table_one" style="display: flex">
                <div class="goodsOrder_pic">
                  <img
                    :src="gameDt.productPic"
                    style="width: 100%; height: 100%"
                  />
                </div>
                <div class="goodsOrder_text">
                  <div v-if="productCategoryObj.goodsType==='vgoods'" class="number_goodsList_item_box">
                      <div class="spaceAlignCenter textOneLine">
                        <div
                          :class="
                            getAttrTypeTxt('交易类型') === '平台代发'
                              ? 'number_goodsItem_center_title_platform'
                              : ''
                          "
                          class="number_goodsItem_center_title"
                        >
                          {{ getAttrTypeTxt('交易类型') }}
                        </div>
                        <div class="number_goodsItem_center_content">
                          【{{ getAttrTypeTxt('交易方式') }}】{{
                            getAttrTypeTxt('数量')
                          }}{{ getAttrTypeTxt('单位') }}={{
                            gameDt.price
                          }}元
                        </div>
                      </div>
                      <div class="spaceStart number_goodsItem_center_text">
                        {{ gameDt2.gameAccountQufu }}
                        <div style="
                              margin:0 10px;
                              padding:0 10px;
                              border-left: 1px solid #d9d9d9;
                              border-right: 1px solid #d9d9d9;
                              line-height: 12px;
                            "> 
                          {{ getAttrTypeTxt('商品类型') }}
                        </div>
                        库存：<span style="color: #3399ff; font-weight: 700">{{ getAttrTypeTxt('发布件数') }}</span>
                      </div>
                      <div
                      style="width: 170px;font-size: 12px;margin-top: 10px;"
                      v-html="
                        getAttrTypeTxt('比例说明')
                          .split('')
                          .map((char) =>
                            isNaN(char)
                              ? char
                              : `<span style='color: #FF5C00'>${char}</span>`
                          )
                          .join('')
                      "
                    ></div>
                  </div>
                  <div v-else class="text_linThree">
                    {{ gameDt.productSubTitle | tedianFilter }}
                  </div>
                  <div class="spaceStart mid_chennuo">
                    <div class="spaceStart mid_chennuo_item">
                      <img src="../../../static/imgs/goods_detail_icon1.svg" />

                      <div>找回包赔</div>
                    </div>
                    <div class="spaceStart mid_chennuo_item">
                      <!-- <img src="../../../static/d2.png" /> -->
                      <img src="../../../static/imgs/goods_detail_icon2.svg" />
                      <div>合同保障</div>
                    </div>
                    <div ref="buyDiv" class="spaceStart mid_chennuo_item">
                      <!-- <img src="../../../static/d3.png" /> -->
                      <img src="../../../static/imgs/goods_detail_icon3.svg" />
                      <div>百人团队</div>
                    </div>
                  </div>
                  <!-- <div class="goodsOrder_subTit">
                    <span
                      v-for="(itemS, indexS) in gameDt.details"
                      :key="indexS"
                      >{{ itemS.lable }}:{{ itemS.value }}；</span
                    >
                  </div> -->
                </div>
              </div>
              <div class="table_two">{{ gameDt.price }}</div>
             
              <div class="table_three" style="margin-right: 46.2px">
                {{ gameDt.productSn }}
              </div>
            </div>
          </div>
          <!-- <div v-if="productCategoryObj.navStatus == 4" class="table_self">
            <div class="spaceBetweenNoAi account_table_header">
              <div class="account_table_one account_table_header_text">商品</div>
              <div class="account_table_two account_table_header_text">类型</div>
              <div class="account_table_two account_table_header_text">单价（¥）</div>
              <div class="account_table_two account_table_header_text">售价（元）</div>
            </div>
            <div class="spaceBetween account_table_body">
              <div class="account_table_one" style="display: flex">
                <div class="number_goodsOrder_pic">
                  <img
                    :src="productCategoryObj.icon"
                    style="width: 100%; height: 100%"
                  />
                </div>
                <div class="number_goodsOrder_text">
                  <div class="number_goodsOrder_text_one">
                    【邮寄交易】 6万银=43元
                  </div>
                  <div class="number_goodsOrder_text_two">
                    游戏区服：游戏官方/幻月御风
                  </div>
                </div>
              </div>
              <div class="account_table_two">{{ gameDt.price }}</div>
              <div class="account_table_two" >
                {{ gameDt.price }}
              </div>
              <div class="account_table_two" >
                {{ gameDt.price }}
              </div>
            </div>
          </div> -->
          <div v-if="baseBPList && baseBPList.length" class="table_self">
            <div
              style="padding-left: 56px; padding-top: 15px"
              class="table_header"
            >
              <!-- <span class="color_red">基础 </span>
              <span>包赔服务（单选，97%人已选择）</span> -->
              <div style="display: flex; align-items: center">
                <div class="color_red">基础</div>
                <div>包赔服务（单选，97%人已选择）</div>
              </div>
            </div>
            <div style="padding: 40px 39px" class="table_body spaceStart">
              <div
                v-for="(item, index) in baseBPList"
                :key="index"
                :class="baojiaIndex == index ? 'active' : ''"
                class="compensate_wrap"
                @click="chooseBao(index, item)"
              >
                <img
                  v-if="baojiaIndex == index"
                  class="basic_not_active_logo"
                  src="../../../static/imgs/confirmOrder_logo_active.svg"
                  alt=""
                />
                <img
                  v-else
                  class="basic_not_active_logo"
                  src="../../../static/imgs/confirmOrder_logo_not_active.svg"
                  alt=""
                />

                <div style="position: relative; z-index: 2">
                  <div class="spaceBetween">
                    <div class="spaceBetween bp_note_top">
                      <div class="title">
                        {{ item.value }}
                        <span @click="doQuestion(index, item, $event)">
                          <IconFont
                            :size="16.28"
                            style="margin: 0; cursor: pointer; margin-top: -3px"
                            color="#FF720C"
                            icon="remind"
                          />
                        </span>
                        <!-- @click="addBaopeiShow('basic')" -->
                      </div>
                      <div class="basic_price">¥{{ getPrice2(item) }}</div>
                    </div>

                    <!-- <div class="spaceStart">
                      <i
                        class="iconfontnew icon-icon-question"
                        @click="doQuestion(index, item, $event)"
                      ></i>
                    </div> -->
                  </div>
                  <div class="basic_text">
                    最高赔付<span class="payPrice"
                      >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</span
                    >
                  </div>
                  <div class="spaceBetween itemstart">
                    <div class="bp_note">
                      {{ item.ruler }}
                    </div>
                    <div class="spaceStart">
                      <!-- <div>¥{{ getPrice2(item) }}</div> -->

                      <img
                        v-if="baojiaIndex == index"
                        class="acrive_icon"
                        src="../../../static/imgs/confirmOrder_ative_icon.svg"
                        alt=""
                      />
                      <!-- <div v-else class="noactive_red"></div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="addBPList && addBPList.length&&faceFlag" class="table_self">
            <div
              style="padding-left: 56px; padding-top: 15px"
              class="table_header"
            >
              <!-- <span class="color_blue">增值 </span>包赔服务<span
                ><span>（多选，95%人已选择）</span></span
              > -->
              <div style="display: flex; align-items: center">
                <div class="color_blue">增值</div>
                <div>包赔服务（多选，95%人已选择）</div>
              </div>
            </div>
            <div style="padding: 40px 39px" class="table_body spaceStart">
              <div
                v-for="(item, index) in addBPList"
                v-if="comparePrice(item)"
                :key="index"
                :class="hasAdd(item) ? 'active2' : ''"
                class="compensate_wrap"
                @click="chooseBaoAdd(item)"
              >
                <img
                  v-if="hasAdd(item)"
                  class="basic_not_active_logo"
                  src="../../../static/imgs/confirmOrder_logo_active.svg"
                  alt=""
                />
                <img
                  v-else
                  class="basic_not_active_logo"
                  src="../../../static/imgs/confirmOrder_logo_not_active.svg"
                  alt=""
                />

                <!-- <img src="../../../static/imgs/logo_Bk.svg" alt="" /> -->
                <div style="position: relative; z-index: 2">
                  <div class="spaceBetween">
                    <div class="spaceBetween bp_note_top">
                      <div class="title">
                        {{ item.value }}
                        <img
                          style="
                            width: 16.27px;
                            cursor: pointer;
                            margin-top: -3px;
                          "
                          src="../../../static/imgs/confirmOrder_appreciate_icon.svg"
                          alt=""
                          @click="doQuestionAdd(index, item, $event)"
                        />
                      </div>
                      <!-- @click="addBaopeiShow('appreciate')" -->
                    </div>
                    <div class="appreciate_price">¥{{ getPrice2(item) }}</div>
                  </div>

                  <div class="basic_text">
                    最高赔付
                    <span v-if="item.topRepay == -1" class="payPrice2"
                      >无上限</span
                    >
                    <span v-else class="payPrice2"
                      >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</span
                    >
                  </div>
                  <!-- <div class="spaceStart">
                    <i
                      class="iconfontnew icon-icon-question"
                      @click="doQuestionAdd(index, item, $event)"
                    ></i>
                  </div> -->
                  <div class="spaceBetween itemstart">
                    <div class="bp_note">
                      {{ item.ruler }}
                    </div>
                    <div class="spaceStart">
                      <img
                        v-if="hasAdd(item)"
                        class="acrive_icon"
                        src="../../../static/imgs/confirmOrder_ative_icon.svg"
                        alt=""
                      />
                      <!-- <div>¥{{ getPrice2(item) }}</div> -->
                      <!-- <div
                        v-if="hasAdd(item)"
                        class="iconfontnew icon-huiyishiqueren_huabanfuben active_blue"
                      ></div>
                      <div v-else class="noactive_blue"></div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="type == 1" class="dingjin_pay">
            订单总价大于200000元，因账号金额限制，无法支付全款，将按定金金额200000元先行支付。
          </div>
        </div>
        <div v-if="productCategoryObj.goodsType==='vgoods'" class="table_self">
          <div class="spaceBetweenNoAi table_header">
            <div style="text-indent: 56px" class="table_one">收货信息</div>
          </div>
          <div class="spaceBetween table_body">
            <div style="width: 100%">
              
              <el-form
                ref="postForm"
                :rules="rules"
                :model="{ orderSkuList }"
                style="margin-top: 30px; margin-left: 30px; padding-top: 0px"
                class="confirmOrder_navStatus4_form"
                label-width="130px"
              >
              <el-form-item label="购买件数">
                <div><span style="color: #ff720c;">{{gameDt.quantity||1 }}</span>件</div>
              </el-form-item>
                <el-form-item
                  v-for="(item, index) in orderSkuList"
                  :key="`orderSkuList${index}`"
                  :label="item.name"
                  :prop="`orderSkuList.${index}.value`"
                  :rules="[
                    {
                      required: item.is_required ? true : false,
                      message: '请输入' + item.name,
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="orderSkuList[index].value"
                    :placeholder="item.placeholder || '请输入'"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
        <div class="table_self">
          <div class="spaceBetweenNoAi table_header">
            <div style="color: #1f1f1f;margin-left:56px; font-weight: 600" class="table_one">
              代金券
            </div>
          </div>
          <div
            style="padding-left: 24px; padding-right: 24px"
            class="table_body"
          >
            <div class="tabs">
              <div
                v-for="item in COUPON_STATUS"
                :key="item.value"
                :class="{ 'tabs-item-actived': couponStatus === item.value }"
                class="tabs-item"
                @click="changeCouponStatus(item)"
              >
                {{ item.label }}
              </div>
            </div>
            <div class="coupon-list">
              <template v-if="displayCouponList && displayCouponList.length">
                <CouponItem
                  v-for="item in displayCouponList"
                  :key="item.id"
                  :item="item"
                  :select-item="couponSelectItem"
                  :is-can-use="couponStatus === 1 ? '可用' : '不可用'"
                  type="check"
                  @check="selectCoupon"
                ></CouponItem>
              </template>
              <div v-else class="list_null_sorry" style="margin-top: 3px">
                <img
                  style="width: 54px; height: 56px"
                  src="../../../static/imgs/null.png"
                  alt=""
                />
                <div class="sorry_text">暂时无不可用代金券</div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer_box">
          <div
            :class="priceDetailShow ? 'confirm_footer_show' : ''"
            class="confirm_footer"
          >
            <div class="title">订单明细</div>
            <div class="content">
              <div class="spaceBetween confirm_footer_item">
                <div class="textRight left_title">单价</div>
                <div class="conOrder_right">¥ {{ gameDt.price }}</div>
              </div>
              <div v-if="baojiaItem" class="spaceBetween confirm_footer_item">
                <div class="textRight left_title">{{ baojiaItem.value }}</div>
                <div class="conOrder_right">
                  ¥ {{ getBaopeiPrice(baojiaItem) }}
                </div>
              </div>
              <div
                v-for="(item, index) in baojiaAddList"
                :key="index"
                class="spaceBetween confirm_footer_item"
              >
                <div class="textRight left_title">{{ item.value }}</div>
                <div class="conOrder_right">¥ {{ getBaopeiPrice(item) }}</div>
              </div>
              <div
              v-if="couponSelectItem.id"
              class="spaceBetween"
            >
              <div class="textRight left_title">代金券：</div>
              <div class="conOrder_right">-￥{{ couponSelectItem.amount }}</div>
            </div>

              <div
                style="padding-bottom: 0px"
                class="spaceEnd confirm_footer_item"
              >
                <div style="margin-right: 9.427px" class="textRight left_title">
                  总价
                </div>
                <div class="conOrder_right">¥ {{ totalPrice }}</div>
              </div>
            </div>
          </div>

          <div class="spaceEnd">
            <div class="textRight goodsNum">共1件</div>
          </div>

          <div class="spaceEnd">
            <div class="payPrice">
              <span style="margin-right: 16px">合计</span>¥{{ totalPrice }}
            </div>
          </div>

          <div class="spaceEnd">
            <div class="ddDetail spaceCenter" @click="toggleDetail">
              <span>订单详情</span>
            </div>
            <!-- <img
              class="ddDetail"
              src="../../../static/imgs/confirmOrder_order_detail_btn.svg"
              alt=""
              @click="toggleDetail"
            /> -->
            <div class="plDt_btn spaceCenter" @click="payChoose">提交订单</div>
          </div>
          <div class="spaceEnd aggree_pay">
            <IconFont
              v-if="!checked"
              :size="16"
              style="margin-right: 6px; cursor: pointer; margin-top: 0px"
              icon="unchecked"
              @click="changChecked(true)"
            />
            <img
              v-if="checked"
              style="width: 16px; margin-right: 6px; cursor: pointer"
              src="../../../static/imgs/confirmOrder_order_chebox_icon.svg"
              alt=""
              @click="changChecked(false)"
            />

            <!-- <IconFont
              v-if="checked"
              :size="17.14"
              style="
                margin-right: 7px;
                cursor: pointer;
                color: #ff720c;
                width: 17.14px;
                height: 17.14px;
              "
              icon="checked"
              @click="changChecked(false)"
            /> -->
            <!-- <el-checkbox v-model="checked"></el-checkbox> -->
            <div class="cursor">
              <span @click="changeChecked">我同意阅读并愿意遵守</span
              ><router-link to="/helpCenter?id=323" class="helpCenter_text"
                >买家交易规则</router-link
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      class="baopeiDialog"
      title="包赔服务"
      width="680px"
    >
      <div class="baopei_box">
        <div>
          <div><i class="iconfontnew icon-qiandun-32"></i>包赔规则</div>
          <div class="baopei_content" v-html="dialogVisibleItem.detail"></div>
        </div>
        <div>
          <div><i class="iconfontnew icon-tanhao"></i>注意事项</div>
          <div class="baopei_content" v-html="dialogVisibleItem.note"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBaopei">选择此赔付服务</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisibleAdd"
      class="baopeiDialog"
      title="包赔服务"
      width="680px"
    >
      <div class="baopei_box">
        <div>
          <div><i class="iconfontnew icon-qiandun-32"></i>包赔规则</div>
          <div
            class="baopei_content"
            v-html="dialogVisibleAddItem.detail"
          ></div>
        </div>
        <div>
          <div><i class="iconfontnew icon-tanhao"></i>注意事项</div>
          <div class="baopei_content" v-html="dialogVisibleAddItem.note"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBaopeiAdd"
          >选择此赔付服务</el-button
        >
      </span>
    </el-dialog>

    <!-- 责任须知 -->
    <!-- <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible.sync="popupSw"
      title="责任须知"
      width="50%"
      center
    >
      <div class="zeren_needKnow">
        <p>普通包赔：号价10%，账号如发生找回未追回，赔付100%号价。</p>
        <p>
          双倍包赔：号价20%，账号如发生找回未追回，赔付200%号价。因为普通包赔只赔付号款，双倍包赔是针对想对账号进行大额投入的买家（如购买天赏，大额充值，代练代肝）特地推出的，可以保障您后续的心血投入。
        </p>
        <p>如不选择包赔服务，买家请知晓并同意以下风险：</p>
        <p>
          1.账号如发生找回，因未签订合同，涉及个人隐私，平台方无法提供卖家的详细信息，但会指导帮助您该如何维权。
        </p>
        <p>
          2.如有期望在平台二次出售该账号，新买家若有购买包赔意向，账号如发生找回，将由您承担责任。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="popupTime > 0" type="success" disabled>
          我知道了{{ popupTime > 0 ? '（' + popupTime + '秒）' : '' }}
        </el-button>
        <el-button v-else type="success" @click="popupHide">我知道了</el-button>
      </span>
    </el-dialog> -->

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>
    <navigation-fixed @goPageTop="backTopPage" />
    <dialogContent :visible="bpVisible" @dialogClone="dialogClone">
      <template slot="right_title">
        <div class="dialog_right_title">包赔服务</div>
      </template>
      <template slot="content">
        <div class="dialog_content_box">
          <div class="title">包赔规则</div>
          <!-- <p>1.账号发生恶意找回必须在7日内联系售后处理</p>
          <p>2.超过7日再联系平台，包赔无效</p>
          <p>3.安全中心技术部门反馈申诉记录只保留7日</p>
          <p>4.看看账号在7天以内未能成功帮助买家追回账号则进行赔付</p> -->

          <div
            v-if="baopeiType === 'basic'"
            class="baseBp_contnet"
            v-html="dialogVisibleItem.detail"
          ></div>
          <div
            v-if="baopeiType === 'appreciate'"
            class="baseBp_contnet"
            v-html="dialogVisibleAddItem.detail"
          ></div>
          <div style="margin-top: 34.286px" class="title">注意事项</div>
          <!-- <p>不能私下更换手机号或脱离平台转手，否则包赔失效</p> -->
          <div
            v-if="baopeiType === 'basic'"
            class="baseBp_contnet"
            v-html="dialogVisibleItem.note"
          ></div>
          <div
            v-if="baopeiType === 'appreciate'"
            class="baseBp_contnet"
            v-html="dialogVisibleAddItem.note"
          ></div>
          <div class="spaceAlignCenter">
            <img
              class="dialog_btn"
              src="../../../static/imgs/confirmOrder_dialog_btn.svg"
              alt=""
              @click="optionBaopei"
            />
          </div>
        </div>
      </template>
    </dialogContent>
  </div>
</template>

<script>
import util from '@/utils/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import dialogContent from '@/components/borderDialog/index2.vue';
import CouponItem from '../account/coupon/modules/coupon-item.vue';
import { getProductCategory, getOrderSku } from '@/api/search.js';
import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import {
  generateKKConfirmOrderMyAssess,
  generateKKOrderMyAssess2,
} from '@/api/myAssess.js';
import {
  generateKKConfirmOrder,
  generateKKOrder2,
} from '@/api/confirmOrder.js';

import {
  getCouponCanUseByProduct,
  getCouponCantUseByProduct,
} from '@/api/coupon.js';
export default {
  components: {
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    dialogContent,
    CouponItem
  },
  data() {
    return {
      COUPON_STATUS: [
        {
          label: '可用',
          value: 1,
        },
        {
          label: '不可用',
          value: 0,
        },
      ],
      couponStatus: 1,
      canUseCouponList: [],
      cantUseCouponList: [],
      couponSelectItem: {},
      isbaopeiForce:false,
      postForm: {},
      orderSkuList: [],
      rules: {
        'orderSkuList.0.value': [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      baopeiType: '',
      bpVisible: false,
      dialogVisibleAdd: false,
      dialogVisible: false,
      // pmsSkuStockList: [],
      popupTime: 3,
      popupTimer: null,
      // popupSw: false, // 协议弹框
      index: 1,
      loading: false,
      checked: false, // 统一协议
      productCategoryId: '',
      productId: '',
      quantity:'',
      gameDt: {}, // 商品数据
      gameDt2:{},//商品更详细的数据
      totalPrice: 0, // 总价
      baojiaIndex: null, // 保价选择的下标
      baojiaItem: null,
      // baojiaAddIndex: null,
      ensure: 1, // 是否报价,1保价 0不保价
      ensure_price: 0, // 保价的金额
      ensure_price_add: 0,
      type: 2, // 1 预定 2 全款
      from: '',
      negoId: '',
      priceDetailShow: false,
      dialogVisibleIndex: 0,
      dialogVisibleItem: '',
      // dialogVisibleAddIndex: 0,
      dialogVisibleAddItem: '',
      baseBPList: [],
      addBPList: [],
      baojiaAddList: [],
      productCategoryObj: {},
      faceFlag:true,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    displayCouponList() {
      if (this.couponStatus === 1) {
        return this.canUseCouponList;
      } else {
        return this.cantUseCouponList;
      }
    },
  },
  mounted() {
    if (isLogin()) {
      this.$store.dispatch('getUserInfoStore');
    }
    this.productCategoryId = this.$route.query.productCategoryId;
    this.quantity=this.$route.query.quantity
    this.from = this.$route.query.from;
    if (this.from === 'myAssess') {
      this.negoId = this.$route.query.negoId;
      this.productId = this.$route.query.productId;
      this.getGenerateKKConfirmOrderMyAssess();
    } else if (this.$route.query.productId) {
      this.productId = this.$route.query.productId;
      this.initGame();
    }
    this.getCouponCantUseByProduct();
    this.getCouponCanUseByProduct();
  },
  methods: {
    getCouponCantUseByProduct() {
      getCouponCantUseByProduct(this.productId).then((res) => {
        this.cantUseCouponList = res.data;
      });
    },
    getCouponCanUseByProduct() {
      getCouponCanUseByProduct(this.productId).then((res) => {
        this.canUseCouponList = res.data;
      });
    },

    selectCoupon(item) {
      if (this.couponSelectItem.id === item.id) {
        this.couponSelectItem = {};
      } else {
        this.couponSelectItem = item;
      }
      this.computePrice();
    },
    changeCouponStatus(item) {
      this.couponStatus = item.value;
    },
    getAttrTypeTxt(str) {
      const str1 = (this.gameDt2.attrValueList||[])
        .filter((ele) => [str].includes(ele.name))
        .map((ele) => ele.value)
        .join(' ');
      return str1;
    },
    changChecked(flag) {
      this.checked = flag;
      if (flag) {
        this.checkedFlag = false;
      }
    },
    hasAdd(item) {
      const findIt = this.baojiaAddList.find((ele) => ele.id === item.id);
      return !!findIt;
    },
    addBaopei() {
      this.dialogVisible = false;
      if (this.dialogVisibleIndex == this.baojiaIndex) {
        return;
      }
      this.chooseBao(this.dialogVisibleIndex, this.dialogVisibleItem);
    },
    doQuestion(index, item, event) {
      this.baopeiType = 'basic';
      event.stopPropagation();
      this.dialogVisibleIndex = index;
      this.dialogVisibleItem = item;
      // this.dialogVisible = true;
      this.bpVisible = true;
    },
    addBaopeiAdd() {
      this.dialogVisibleAdd = false;
      const findIt = this.baojiaAddList.find(
        (ele) => ele.id === this.dialogVisibleAddItem.id
      );
      if (findIt) {
        return;
      }
      this.chooseBaoAdd(this.dialogVisibleAddItem);
    },
    optionBaopei() {
      this.bpVisible = false;
      if (this.baopeiType == 'basic') {
        if (this.dialogVisibleIndex == this.baojiaIndex) {
          return;
        }
        this.chooseBao(this.dialogVisibleIndex, this.dialogVisibleItem);
      }
      if (this.baopeiType == 'appreciate') {
        const findIt = this.baojiaAddList.find(
          (ele) => ele.id === this.dialogVisibleAddItem.id
        );
        if (findIt) {
          return;
        }
        this.chooseBaoAdd(this.dialogVisibleAddItem);
      }
      // if (
      //   this.baopeiType === 'basic' &&
      //   this.baseBPList &&
      //   this.baseBPList.length > 0
      // ) {
      //   if (this.dialogVisibleIndex == this.baojiaIndex) {
      //     return;
      //   }
      //   this.chooseBao(0, this.baseBPList[0]);
      // }
      // if (
      //   this.baopeiType === 'appreciate' &&
      //   this.addBPList &&
      //   this.addBPList.length > 0
      // ) {
      //   // 如果第一个已选择 再次选择不生效
      //   if (
      //     this.baojiaAddList &&
      //     this.baojiaAddList.length > 0 &&
      //     this.baojiaAddList[0].id === this.addBPList[0].id
      //   ) {
      //     return;
      //   }
      //   this.chooseBaoAdd(this.addBPList[0]);
      // }
      // this.chooseBao(0, this.baseBPList[0])
      // this.chooseBaoAdd(this.addBPList[0]);
    },
    addBaopeiShow(v) {
      this.baopeiType = v;
      this.bpVisible = true;
    },
    dialogClone() {
      this.bpVisible = false;
    },
    doQuestionAdd(index, item, event) {
      this.baopeiType = 'appreciate';
      event.stopPropagation();
      // this.dialogVisibleAddIndex = index;
      this.dialogVisibleAddItem = item;
      this.bpVisible = true;
    },
    toggleDetail() {
      this.priceDetailShow = !this.priceDetailShow;
    },
    getGenerateKKConfirmOrderMyAssess() {
      generateKKConfirmOrderMyAssess({ negoId: this.negoId }).then((res) => {
        if (res.code == 200) {
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    changeChecked() {
      this.checked = !this.checked;
    },
    getBaopeiPrice(ele) {
      let ratio = ele.price;
      let price = util.times(this.gameDt.price, ratio);
      return price;
    },
    getPrecent(item) {
      return util.times(item.topRepay, 100, 0);
    },
    getPrice(item) {
      let ratio = item.topRepay;
      return this.accMul(this.gameDt.price, ratio);
    },
    getPrice2(item) {
      return util.times(this.gameDt.price, item.price);
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    comparePrice(item) {
      const result = util.comparedTo(this.gameDt.price, item.lowprice);
      if (result === -1) {
        return false;
      } else {
        return true;
      }
    },
    // 责任须知
    // popupShow() {
    //   this.popupSw = true;
    //   clearInterval(this.popupTimer);
    //   this.popupTimer = setInterval(() => {
    //     if (this.popupTime <= 0) {
    //       clearInterval(this.popupTimer);
    //     } else {
    //       this.popupTime--;
    //     }
    //   }, 1000);
    // },
    popupHide() {
      this.popupTime = 0;
      this.popupSw = false;
      clearInterval(this.popupTimer);
    },
    // 创建订单
    payChoose() {
      if (!this.productCategoryObj.goodsType) {
        if (!this.checked) {
          this.$message.error('请勾选买家交易规则!');
          return;
        }
        if (this.from === 'myAssess') {
          this.payAssess();
        } else {
          this.payNormal();
        }
      } else {
        this.$refs['postForm'].validate((valid) => {
          if (valid) {
            if (!this.checked) {
              this.$message.error('请勾选买家交易规则!');
              return;
            }
            this.payNormal();
          }
        });
      }
    },
    payAssess() {
      this.loading = true;
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));
      let data = {
        buyType: baopeiTypes.length ? 1 : 0,
        negoId: this.negoId,
        baopeiTypes: baopeiTypes.join(','),
        sourceType: 3,
        quantity:this.quantity,
        couponCode: this.couponSelectItem.couponCode || undefined,
      };
      generateKKOrderMyAssess2(data).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.$confirm(
            `支付金额 <span style="color:#ff6700;">¥${res.data.payAmount||this.totalPrice}</span>`,
            '立即支付',
            {
              closeOnClickModal: false,
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'success',
              dangerouslyUseHTMLString: true,
            }
          )
            .then(() => {
              this.$router.push({
                path: '/payOrder?orderId=' + res.data.id,
              });
            })
            .catch(() => {
              this.$router.push({
                path: '/account/orderDetail?orderId=' + res.data.id,
              });
            });
        }
      });
    },
    payNormal() {
      this.loading = true;
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));
      let data = {
        buyType: baopeiTypes.length ? 1 : 0,
        baopeiTypes,
        productId: this.productId,
        sourceType: 3,
        quantity:this.quantity,
        couponCode: this.couponSelectItem.couponCode || undefined,
      };
      if (this.productCategoryObj.goodsType==='vgoods') {
        data.buyerAttr = JSON.stringify(this.orderSkuList);
      }
      generateKKOrder2(data).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.$confirm(
            `支付金额<span style="color:#ff6700;">¥${res.data.payAmount||this.totalPrice}</span>`,
            '立即支付',
            {
              closeOnClickModal: false,
              confirmButtonText: '去支付',
              cancelButtonText: '取消',
              type: 'success',
              dangerouslyUseHTMLString: true,
            }
          )
            .then(() => {
              this.$router.push({
                path: '/payOrder?orderId=' + res.data.id,
              });
            })
            .catch(() => {
              this.$router.push({
                path: '/account/orderDetail?orderId=' + res.data.id,
              });
            });
        }
      });
    },
    formatRes(res) {
      let calcAmount=res.data.calcAmount
      let obj={
        ...res.data.cartPromotionItemList[0],
        price:calcAmount.payAmount
      }
      // this.gameDt = res.data.cartPromotionItemList[0];
      this.gameDt=obj

      // 组装道具产品信息
      const {product,productAttributeValueList=[]} = res.data.goodsProduct||{}
      this.gameDt2={
        ...product,
        attrValueList:productAttributeValueList.map(ele=>({...ele,name:ele.productAttributeName}))
      }
    },
    /**
     * 初始化-商品数据
     */
    getBaopei() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code == 200) {
          this.productCategoryObj = res.data;
          this.productCategoryObj.goodsType=''
          try {
            const custom = res.data.custom;
            this.productCategoryObj.goodsType =
              custom && custom !== '{}'
                ? JSON.parse(custom).goodsType || ''
                : '';
            getOrderSku(res.data.attriCateId).then((res) => {
              res.data.forEach((item) => {
                item.value = '';
                let customObj = {};
                item.is_required = item.handAddStatus == 1 ? 1 : 0;
                try {
                  customObj = JSON.parse(item.custom || '{}');
                  item.placeholder = customObj.placeholder;
                } catch (e) {
                  customObj = {};
                }
              });
              this.orderSkuList = res.data;
            });
          } catch (e) {
            this.productCategoryObj.goodsType = '';
          }
          // if (res.data.navStatus == 4) {

          // }
          if (res.data.custom) {
            let custom = JSON.parse(res.data.custom);
            if(custom.isbaopeiForce){
              this.isbaopeiForce=true
            }
            this.baseBPList = [];
            this.addBPList = [];
            if (custom.baopei && custom.baopei.length) {
              custom.baopei.forEach((ele) => {
                if (ele.type == 'BASIC_COMPENSATION') {
                  this.baseBPList.push(ele);
                }
                if (ele.type == 'VALUE_ADD_COMPENSATION') {
                  this.addBPList.push(ele);
                }
              });
            }
            let item = this.baseBPList[0];
            if (item) {
              this.chooseBao(0, item);
            } else {
              this.ensure_price_add = 0;
              this.ensure_price = 0;
              this.ensure = 0;
            }
            this.computePrice();
            // this.popupShow();
          }
        }
      });
    },
    initGame() {
      generateKKConfirmOrder({
        buyType: 0, // 随便传个值
        productId: this.productId,
        quantity:this.quantity
      }).then((res) => {
        if (res.code == 200) {
          const faceObj=res.data.goodsProduct.productAttributeValueList.find(item=>{
            return item.productAttributeName=='人脸包赔'
          })
          if(faceObj&&faceObj.value=='不支持人脸包赔'){
            this.faceFlag=false
          }
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    // 总价计算
    computePrice() {
      this.totalPrice = util.add(this.gameDt.price, this.ensure_price || 0);
      this.totalPrice = util.add(this.totalPrice, this.ensure_price_add || 0);
      this.totalPrice = util.add(
        this.totalPrice,
        -this.couponSelectItem.amount || 0
      );
      if (this.totalPrice > 200000) {
        this.type = 1;
      } else {
        this.type = 2;
      }
    },
    chooseBaoAdd(item) {
      if (this.baojiaIndex == null) {
        this.$message.error('须选择基础包赔后才可加购增值包赔');
        return;
      }
      const findIndex = this.baojiaAddList.findIndex(
        (ele) => ele.id === item.id
      );
      if (findIndex !== -1) {
        this.baojiaAddList.splice(findIndex, 1);
      } else {
        this.baojiaAddList.push(item);
      }
      this.ensure_price_add = 0;

      this.baojiaAddList.forEach((ele) => {
        let ratio = ele.price;
        let price = util.times(this.gameDt.price, ratio);
        this.ensure_price_add = util.add(this.ensure_price_add, price);
      });
      this.computePrice();
    },
    // 保价选择
    chooseBao(num, item) {
      if (this.baojiaIndex == num&&!this.isbaopeiForce) {
        this.baojiaIndex = null;
        this.baojiaItem = null;
        this.ensure_price = 0;
        this.ensure_price_add = 0;
        this.ensure = 0;
        this.baojiaAddList = [];
      } else {
        this.baojiaIndex = num;
        this.baojiaItem = item;
        let ratio = item.price;
        this.ensure = parseInt(num) + 1;
        this.ensure_price = this.accMul(this.gameDt.price, ratio);
      }
      this.computePrice();
    },
    /**
     * 乘法函数，用来得到精确的乘法结果
     * 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
     * 调用：accMul(arg1,arg2)
     * 返回值：arg1乘以 arg2的精确结果
     **/
    accMul(arg1, arg2) {
      return util.times(arg1, arg2);
    },
  },
};
</script>

<style>
.confirmOrder_navStatus4_form .is-error .el-input__inner {
  border-color: #ff720c !important;
}
.confirmOrder_navStatus4_form .el-input .el-input__inner {
  border-radius: 20px;
  width: 490.204px;
  height: 38px;
}
.confirmOrder_navStatus4_form .el-form-item__error {
  color: #ff720c;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.48px;
}
.confirmOrder_navStatus4_form
  .safeguards_form_item
  .is-disabled
  .el-input__inner {
  background: #fbf9f7;
}
.confirmOrder_navStatus4_form .safeguards_form_item .el-input .el-input__inner {
  width: 232.24px;
  height: 38px;
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 13.712px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
  border-radius: 20px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
}
.confirmOrder_navStatus4_form
  .safeguards_form_item
  .el-select
  .el-input__inner {
  width: 95.984px;
}
.confirmOrder_navStatus4_form .el-radio__input.is-checked .el-radio__inner {
  border-color: #ff720c;
  background: #fff;
}
.confirmOrder_navStatus4_form
  .el-radio__input.is-checked
  .el-radio__inner::after {
  background: #ff720c;
  width: 6px;
  height: 6px;
}
.confirmOrder_navStatus4_form .el-form-item__label:before {
  background: var(--btn-background-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important;
  font-family: 'PingFang SC';
}
.confirmOrder_navStatus4_form .is-checked .el-radio__label {
  color: #1b1b1b;
}
.confirmOrder_navStatus4_form .el-radio__label {
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.32px;
}
.confirmOrder_navStatus4_form .is-required .el-form-item__label {
  text-indent: initial !important; /* 将带有 is-required 的标签的缩进恢复为初始值，即排除应用前面设置的样式 */
}

.confirmOrder_navStatus4_form .el-form-item__label {
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
  padding: 0;
  text-align: left;
  text-indent: 13.4px;
  margin-top: 7px;
}
.confirmOrder_navStatus4_form .el-form-item {
  margin-bottom: 20.567px;
}
.confirmOrder_navStatus4_form .el-form-item .el-form-item__content {
  margin-left: 150.567px !important;
}
</style>
<style lang="scss" scoped>
.basic_not_active_logo {
  position: absolute;
  top: 24px;
  right: 15.98px;
  width: 115px;
  height: 121px;
  z-index: 0;
}
.itemstart {
  // align-items: start;
  margin-top: 6px;

  color: rgba(0, 0, 0, 0.4);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  letter-spacing: 0.56px;
}
.acrive_icon {
  width: 26px;
  height: 26px;
}
.active_red {
  color: #e60f0f;
  font-size: 16px;
  margin-left: 3px;
  line-height: 16px;
}
.noactive_red {
  height: 16px;
  width: 16px;
  border: 1px solid #ccc;
  border-radius: 50%;
  margin-left: 3px;
}
.active_blue {
  color: #0082ff;
  font-size: 20px;
  line-height: 16px;
  height: 16px;
  width: 16px;
  margin-right: 3px;
  margin-left: 3px;
}
.noactive_blue {
  border: 1px solid #ccc;
  height: 15px;
  width: 15px;
  margin: 0 2px 0px 5px;
}
.color_blue {
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  min-width: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 2px;
}
.appreciate_price {
  font-family: 'PingFang SC';
  font-size: 20.57px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.color_red {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  margin-right: 2px;
}
.baopeiDialog {
  /deep/ .el-dialog__header {
    background: #fe5a1e;
    .el-dialog__title {
      color: #fff;
    }
    .el-icon-close {
      color: #fff;
    }
    .el-icon-close:hover {
      color: #fff;
    }
    text-align: center;
  }
  /deep/ .el-dialog__footer {
    text-align: center;
  }
  .baopei_box {
    font-size: 18px;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 20px 10px;
  }
  .baopei_content {
    padding: 10px 0;
    line-height: 28px;
  }
  .icon-qiandun-32,
  .icon-tanhao {
    margin-right: 6px;
    position: relative;
    top: 2px;
  }
}

.icon-icon-question {
  font-size: 18px;
  margin-left: 5px;
}

.page_comStyle {
  margin-bottom: 105px;
  // padding-top: 30px;
  // padding-bottom: 100px;
  position: relative;
}

.main_tit {
  color: #ff7a00;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
}
.table_self {
  border-radius: 24px;
  border: 1px solid #e9e9e9;
  margin-top: 20px;
}
.table_header {
  box-sizing: border-box;
  // padding: 12.85px 74.5px 10.28px 48px;
  height: 56px;
  background: #fbf9f7;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  font-weight: 400;
  font-family: 'PingFang SC';
  line-height: 30px; /* 187.5% */
  letter-spacing: 0.64px;
  border-bottom: 1px solid #e9e9e9;
  border-radius: 24px 24px 0px 0px;
  padding: 16px 0px 0px 0px;
}
.account_table_header {
  box-sizing: border-box;
  // padding: 12.85px 74.5px 10.28px 48px;
  height: 38px;
  background: #fbf9f7;
  font-size: 14px;
  color: #666666;
  font-weight: 400;
  font-family: 'PingFang SC';
  letter-spacing: 0.64px;
  border-bottom: 1px solid #e9e9e9;
  border-radius: 24px 24px 0px 0px;
  display: flex;
  align-items: center;
}
.account_table_header_text {
  text-align: center;
}
.table_body {
  box-sizing: border-box;
  padding: 0 0 28.2px;
  font-size: 14px;
  color: #222222;
  flex-wrap: wrap;
}
.account_table_body {
  box-sizing: border-box;
  font-size: 14px;
  color: #222222;
  flex-wrap: wrap;
  height: 100px;
}
.table_one {
  flex-shrink: 1;
  width: 700px;
}
.table_two {
  flex-shrink: 1;
  width: 150px;
  text-align: center;
}
.table_three {
  flex-shrink: 1;
  width: 150px;
  text-align: center;
}
.account_table_one {
  // width: 690px;
  flex: 1;
  overflow: hidden;
  align-items: center;
}
.account_table_two {
  width: 170px;
  text-align: center;
}
.goodsOrder_pic {
  width: 227px;
  height: 141.802px;
  border-radius: 16px;
  overflow: hidden;
  // margin-right: 21px;
  margin: 21px 24px 0px 40px;
  flex-shrink: 0;
}
.number_goodsOrder_pic {
  width: 58px;
  height: 58px;
  border: 1px solid #eee;
  flex-shrink: 0;
  margin-right: 10px;
  margin-left: 20px;
}
.goodsOrder_text {
  width: 388px;
  height: 147px;
  font-size: 16px;
  font-family: 'PingFang SC';
  color: #222222;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
  margin-top: 21px;
}
.number_goodsOrder_text {
  font-size: 14px;
  font-family: 'PingFang SC';
  color: #222222;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
}
.number_goodsOrder_text_one {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  margin-top: -18px;
}
.number_goodsOrder_text_two {
  font-size: 12px;
  color: #aaa;
  padding-top: 2px;
}
.goodsOrder_subTit {
  font-size: 14px;
  color: #909090;
  padding-top: 8px;
}
.baojiaPrice {
  font-weight: 500;
  color: #ff6700;
}
.compensate_wrap {
  width: 333px;
  min-height: 171px;
  box-sizing: border-box;
  padding: 23px 22px 23px 24px;
  // border: 1px solid rgba(166, 166, 166, 0.4);
  background: rgba(245, 245, 245, 0.4);
  border-radius: 24px;
  font-size: 14px;
  color: #909090;
  transition: all 0.3s;
  cursor: pointer;
  // margin-top: 34.286px;
  margin-right: 20px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border-radius: 24px;
    // margin: 1px;
    border: 1px solid rgba(166, 166, 166, 0.4);
  }
}
.compensate_wrap:nth-child(3) {
  margin-right: 0px !important;
}
.compensate_wrap:first-child {
  // margin-left: 10px;
}
.compensate_wrap.active {
  // background: #fffcf9;
  // border: none;
  // padding: 24.5px 24.71px 23.28px 29.14px;

  // border: 1px solid rgba(255, 255, 255, 0) !important;

  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  border-radius: 24px;
  position: relative;
  // text-decoration: none;
  // border: 1px solid #fff !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    margin: 1px;
    background: #fffcf9;
    position: absolute;
    border-radius: 24px;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    border: none;
  }
}
.compensate_wrap.active2 {
  // background: #fffcf9;
  // border: 1px solid #0082ff;
  border: none;
  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  // border-radius: 20px;
  position: relative;
  text-decoration: none;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    margin: 1px;
    background: #fffcf9;
    position: absolute;
    border-radius: 24px;
    border: none;
  }
}

.compensate_tit {
  font-size: 14px;
  color: #222222;
  padding-bottom: 18px;
}

.agree_box {
  padding: 0 10px;
}
.el-checkbox {
  margin-right: 10px;
}
.footer_box {
  margin-top: 43px;
  // position: absolute;
  width: 100%;
  right: 0;
  bottom: 10px;
  // padding: 20px;
  background: #fff;
  .goodsNum {
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.56px;
  }
  .payPrice {
    color: #ff7a00;
    font-family: YouSheBiaoTiHei;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: 38px;
    margin-top: 13px;
    margin-bottom: 20px;
  }
  .ddDetail {
    // width: auto;
    height: 46px;
    width: 162px;
    background: var(--btn-background-gradient) !important;
    position: relative;
    border: none;
    border-radius: 60px;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    cursor: pointer;
    margin-right: 10px;
  }
  .ddDetail span {
    position: relative;
    z-index: 2;
    background: var(--btn-background-gradient);
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
    -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.64px;
    display: block;
  }
  .ddDetail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 60px;
    background: #fff;
    z-index: 1;
    margin: 3px;
    -webkit-border-radius: 60px;
    -moz-border-radius: 60px;
    -ms-border-radius: 60px;
    -o-border-radius: 60px;
  }
  .plDt_btn {
    width: 162px;
    height: 46px;
    // background: url(../../../static/imgs/playDetail_qq_btn_bk.png);
    // background-size: 100% 100%;
    background: var(--btn-background-gradient);
    border: 1px solid #ffddbe;
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    // padding: 11px 0;
    border-radius: 60px;
    font-family: 'PingFang SC';
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
  }
  .aggree_pay {
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.56px;
    margin-top: 16px;
    .helpCenter_text {
      color: #ffb74a;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;
      text-underline-position: from-font;
    }
  }
}
.footer_box.active {
  box-shadow: 0 0 10px 0 #999;
}
.footer_box {
  .confirm_footer_show {
    display: block;
  }
}

.confirm_footer {
  font-size: 16px;
  color: #909090;
  position: absolute;
  right: 62.57px;
  bottom: 131.978px;
  width: 319.6px;
  backdrop-filter: blur(20px);
  border-radius: 20.56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  min-height: 0px;
  padding: 21.425px 24.85px 21.425px 30.852px;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  display: none;
  .title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-bottom: 12.855px;
  }
  .left_title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }
  .content {
    color: #000;
  }
  .confirm_footer_item {
    margin-bottom: 6.856px;
  }
}

.textRight {
  text-align: right;
}
.conOrder_right {
  color: rgba(0, 0, 0, 0.6);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  letter-spacing: 0.64px;
  font-weight: 400;
  line-height: 30px;
}
.payPrice {
  font-size: 14px;
  font-weight: 600;
  color: #f7423f;
}
.payPrice2 {
  font-size: 14px;
  font-weight: 600;
  color: #0082ff;
}
.bp_note_top {
  flex: 1;

  .title {
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.basic_text {
  margin-top: 24px;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  .payPrice {
    margin-left: 2px;
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
  }
  .payPrice2 {
    margin-left: 2px;

    font-family: YouSheBiaoTiHei;
    background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.basic_price {
  color: #ff720c;
  font-family: Inter;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;

  letter-spacing: -0.55px;
  // margin-right: 7.714px;
}
.bp_note {
  max-width: 253px;
}
.dingjin_pay {
  font-size: 20px;
  font-weight: 600;
  color: #f7423f;
  padding: 10px 30px 30px 20px;
}
.zeren_needKnow {
  font-size: 14px;
  color: #333;
  line-height: 26px;
}
.mid_chennuo {
  margin-top: 42px;
  font-size: 14px;
  color: transparent;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  letter-spacing: 0.28px;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.mid_chennuo_item {
  padding: 0 6px;
}
.mid_chennuo_item:nth-child(1) {
  padding-left: 0px;
}
.mid_chennuo_item img {
  width: 28px;
}
.dialog_right_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
}
.dialog_content_box {
  .title {
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.56px;
    margin: 47.13px 0px 12.85px 0px;
  }
  // p {
  //   margin: 0;
  //   line-height: 14px;
  //   color: #969696;
  //   font-family: 'PingFang SC';
  //   font-size: 14px;
  //   font-style: normal;
  //   font-weight: 400;
  // }
  .baseBp_contnet {
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    // /deep/ p {
    //   margin: 0;
    //   line-height: 14px;
    // }
  }
  .dialog_btn {
    margin: 0 auto;
    margin-top: 50.57px;

    color: #fff;
    text-align: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.72px;
    border-radius: 51.42px;
    height: 42.857px;
    border: none;
  }
}

.number_goodsList_item_box {
  .number_goodsItem_center_content {
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    letter-spacing: 0.56px;
  }
  .number_goodsItem_center_price {
    font-family: 'PingFang SC';
    font-size: 20px;
    font-weight: 700;
    color: #ff5c00;
    line-height: 24px;
  }
  .number_goodsItem_center_text {
    margin-top: 8px;
    color: #666;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.56px;
  }
  .number_goodsItem_center_title {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    color: #fff;
    background: #30c6a6;
    border-radius: 4px;
    width: 56px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }
  .number_goodsItem_center_title_platform {
    background: #488cf6;
  }
}

.tabs {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 33px;
  .tabs-item {
    width: 128px;
    height: 20px;
    font-size: 14px;
    color: #9a9a9a;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    margin-top:10px
  }
  .tabs-item-actived {
    font-weight: 600;
    color: #1f1f1f;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -8px;
      transform: translateX(-50%);
      width: 18px;
      height: 3px;
      border-radius: 5px;
      background: #ff720c;
    }
  }
}
.list_null_sorry {
  width: 100%;
  font-size: 18px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 64px 0px;
  background: #fff;
  border-radius: 24px;
  gap: 12px 0;
  color: #9a9a9a;
}
.coupon-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .coupon-item {
    margin-bottom: 20px;
    margin-right: 10px;
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
}
</style>
