.play_List_page_comStyle {
  box-sizing: border-box;
}
.single_word {
  min-height: 100px;
}
.single_word .goodsItem_pic {
  width: 0;
  display: none;
}
.single_word .goodsItem_center {
  width: 770px;
}

.goodsList_item {
  box-sizing: border-box;
  min-height: 219px;
  padding: 24px 35px 24px 24px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
  border-radius: 24px;
  margin-bottom: 12px;
}
.goodsList_item:last-child {
  border-bottom: none;
}
.number_goodsList_item {
  box-sizing: border-box;
  min-height: 110px;
  transition: all 0.3s;
  background: #fff;
cursor: pointer;
  padding: 0 30px;
}
.number_goodsList_item_box {
  padding: 20px 10px;
  border-bottom: 1px dotted #e5e5e5;
}
.number_goodsList_item:last-child {
  border-bottom: none;
}
.number_goodsItem_center_btn {
  width: 110px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  border: 1px solid #ff5c00;
  background: #ffeee4;
  color: #ff5c00;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  font-family: 'PingFang SC';
}
.number_goodsItem_center_btn:hover {
  background: linear-gradient(90deg, #ff5c00 0%, #ff9900 100%);
  border: none;
  color: #ffffff;
}
.number_goodsItem_center_title {
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  color: #fff;
  background: #30c6a6;
  border-radius: 4px;
  width: 56px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.number_goodsItem_center_title_platform {
  background: #488cf6;
}
/**
.goodsList_item:hover {
  background: #fff;
  box-shadow: 0px 5px 10px -5px rgba(179, 179, 179, 0.36);
  transform: scale(1.0177);
  border-radius: 20.56px;
  margin-top: 2px;
  border: none;
  .playListDavie {
    display: none;
  }
}
**/
.goodsItem_pic {
  width: 274px;
  height: 171px;
  position: relative;
}
.goodsItem_pic_img_box{
  width: 274px;
  height: 171px;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  font-family: PingFang Sc;
  border-radius: 12px;
display: none;
  top: 0px;
  left: 0px;
}
.goodsItem_pic_img:hover  .goodsItem_pic_img_box {
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
}
.soled_pic {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
}

.tssnum {
  position: absolute;
  z-index: 10;
  top: 0px;
  left: 0px;
  min-width: 150px;
  height: 40px;
  line-height: 29px;
  display: flex;
  align-items: center;
  .innernum {
    color: #000;
    text-align: center;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: -5px;
    margin-left: 0px;
  }
}
.tssnum1 {
  background: url(../../../static/imgs/newT1.png) no-repeat top;
  background-size: 100% 100%;

  .innernum {
    margin-left: 51px;
  }
}
.tssnum2 {
  background: url(../../../static/imgs/newT2.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 50px;
  }
}
.tssnum3 {
  background: url(../../../static/imgs/newT3.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 50px;
  }
}
.tssnum4 {
  background: url(../../../static/imgs/newT3.png) no-repeat top;
  background-size: 100% 100%;
  .innernum {
    margin-left: 51px;
  }
}
.zxtssnum {
  position: absolute;
  z-index: 10;
  top: 0px;
  left: 0px;
  min-width: 150px;
  height: 40px;
  line-height: 29px;
  display: flex;
  align-items: center;
  .zxinnernum {
    color: #000;
    text-align: center;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: -5px;
    margin-left: 0px;
  }
}
.zxtssnum1 {
  background: url(../../../static/imgs/zxnw1.png) no-repeat top;
  background-size:100% 100%;
  .zxinnernum {
    margin-left: 51px;
  }
}
.zxtssnum2 {
  width: 160px;
  background: url(../../../static/imgs/zxnw1.png) no-repeat top;
  background-size: 100% 100%;
  .zxinnernum {
    margin-left: 51px;
  }
}
.zxtssnum3 {
  background: url(../../../static/imgs/zxnw3.png) no-repeat top;
  background-size: 100% 100%;
  .zxinnernum {
    margin-left: 51px;
  }
}
.zxtssnum4 {
  background: url(../../../static/imgs/zxnw3.png) no-repeat top;
  background-size: 100% 100%;
  .zxinnernum {
    margin-left: 51px;
  }
}
.hot_pic {
  position: absolute;
  top: 5px;
  left: 5px;
  height: 20px;
  z-index: 50;
  font-size: 14px;
  color: #f56c6c;
  border: 1px solid #f56c6c;
  padding: 0 4px;
  line-height: 18px;
  background-color: #f4f0ea;
}
.goodsItem_center_is_not_pic {
  margin-left: 22px;
}
.goodsItem_center_is_pic {
  margin-left: 0px;
}
.goodsItem_center {
  flex: 1;
  font-size: 16px;
  color: #222222;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .goodsItem_center_title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    line-height: normal;
    font-weight: 500;
    text-indent: -12px;
  }
  .goodsItem_center_content {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0.56px;
    height: 60px !important;
  }
}
.number_goodsItem_center_content {
  color: #222;
  font-family: 'PingFang SC';
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  letter-spacing: 0.56px;
}
.number_goodsItem_center_price {
  font-family: 'PingFang SC';
  font-size: 20px;
  font-weight: 700;
  color: #ff5c00;
  line-height: 24px;
}
.number_goodsItem_center_text {
  margin-top: 8px;
  color: #666;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.56px;
}
.tooltip_list {
  width: 600px;
  flex-shrink: 0;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 0px 0px 20px 0px;
  opacity: 1 !important;
}
.topTips_tit {
  color: #000;
  font-family: 'PingFang SC';
  font-size: 17.14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  line-height: 0px;
}
.topTips_tit_play {
  position: relative;
  z-index: 99;
  padding: 20px 10px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08); /* 添加下阴影 */
}
.topTips_tit_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #000;
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  margin: 8px 0px;
}
.topTips_content {
  color: rgba(0, 0, 0, 0.6);
  /* 小字段落 */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  max-height: 400px;
  overflow-y: auto;
}
.searchListBox {
  border-radius: 24px;
  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
  padding: 0px;
  padding: 48px 41px 32px 40px;
  .el-input__inner {
    &::placeholder {
      color: rgba(0, 0, 0, 0.4);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.56px;
    }
  }
}
.list_infoWord {
  height: 60px;
}
.text_linThree {
  -webkit-line-clamp: 3 !important;
  line-clamp: 3 !important;
}
.content_is_pic {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
}
.goodsItem_center_address {
  margin-right: 38px;
  font-size: 14px;
  color: #9a9a9a;
  font-family: 'PingFang SC';
  font-weight: 400;
}

.goodsItem_price {
  font-size: 20px;
  font-family: 'PingFang SC';
  font-weight: 600;
  line-height: normal;
  color: #ff720c;
  display: flex;
  align-items: center;
}
.jjPrice {
  background: #fff2e6;
  font-size: 14px;
  font-family: 'PingFang SC';
  color: #ff720c;
  padding: 3px 6px;
  border-radius: 50px;
  margin-left: 10px;
}
.sorry {
  width: 100%;
  background: #fff;
  border-radius: 24px;
  font-size: 18px;
  display: flex;

  justify-content: center;
  text-align: left;
  padding: 64px 0px;
  .sorry_title {
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
    font-size: 27.42px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .sorry_text {
    color: #969696;
    /* 小字段落 */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 0%;
    margin-top: 5px;
  }
}

.sort_item {
  margin-right: 20px;
  cursor: pointer;
  transition: all 0.3s;
}
.clearSearch {
  transition: all 0.3s;
}
/*
.sort_item.active,
.sort_item:hover,
.clearSearch:hover {
  color: #ff6716;
}*/
.sortArr_pic {
  width: 10px;
  height: auto;
  margin-left: 8px;
}
.sort_container {
  box-sizing: border-box;
  width: 100%;
  letter-spacing: 0.64px;
  font-size: 16px;
  font-weight: 400;
  color: #2d2d2d;
  line-height: 30px;
  font-family: 'PingFang SC';
  margin-top: 20px;
}

.sxbox {
  border-top: 0.5px solid #ff7a00;
  margin-top: 20px;
  padding-top: 20px;
}
.playSearch_tit_top {
  flex-shrink: 0;
  min-width: 66px;
  color: #2d2d2d;
  margin-right: 20px;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
.playSearch_tit {
  flex-shrink: 0;
  width: 110px;
  color: #2d2d2d;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.8px;
}
.goodsItem_btn_search {
  padding: 0px 48px;
  height: 46px;
  line-height: 46px;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  color: #fff;
  font-weight: 500;
  letter-spacing: 0.64px;
  border-radius: 60px;
  cursor: pointer;
}

.goodsItem_pic_list_box {
  margin-bottom: 120px;
}

.pageGoBtn {
  position: absolute;
  width: 153px;
  height: 44px;
  border-radius: 24px;
  background: var(--btn-background-gradient);
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.28px;
  bottom: 24.09px;
  left: 549px;
  cursor: pointer;
}
