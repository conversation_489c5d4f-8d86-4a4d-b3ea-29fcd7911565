<template>
  <div class="canvas-demo-page">
    <div class="header">
      <h1>Canvas 学习任务管理系统</h1>
      <p>使用Canvas绘制的交互式学习任务界面</p>
    </div>
    
    <div class="canvas-wrapper">
      <TaskCanvas @play-item="handlePlayItem" />
    </div>
    
    <div class="controls">
      <el-button @click="refreshCanvas" type="primary">刷新画布</el-button>
      <el-button @click="exportCanvas" type="success">导出图片</el-button>
      <el-button @click="toggleFullscreen" type="info">全屏显示</el-button>
    </div>
    
    <div class="features">
      <h2>功能特点</h2>
      <ul>
        <li>✅ 使用Canvas绘制任务卡片</li>
        <li>✅ 鼠标悬停效果</li>
        <li>✅ 可点击的"播放"按钮</li>
        <li>✅ 锁定状态显示</li>
        <li>✅ S形连接线动画</li>
        <li>✅ 响应式交互</li>
      </ul>
    </div>
    
    <!-- 播放对话框 -->
    <el-dialog
      title="播放学习内容"
      :visible.sync="playDialogVisible"
      width="500px"
    >
      <div v-if="currentPlayItem">
        <p><strong>任务ID:</strong> {{ currentPlayItem.taskId }}</p>
        <p><strong>项目ID:</strong> {{ currentPlayItem.itemId }}</p>
        <p>正在播放学习内容...</p>
        <div class="video-placeholder">
          <i class="el-icon-video-play" style="font-size: 48px; color: #409EFF;"></i>
          <p>视频播放区域</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="playDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="playDialogVisible = false">完成学习</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TaskCanvas from './TaskCanvas.vue'

export default {
  name: 'CanvasDemo',
  components: {
    TaskCanvas
  },
  data() {
    return {
      playDialogVisible: false,
      currentPlayItem: null
    }
  },
  methods: {
    handlePlayItem(item) {
      this.currentPlayItem = item
      this.playDialogVisible = true
    },
    
    refreshCanvas() {
      // 刷新Canvas组件
      this.$nextTick(() => {
        const canvasComponent = this.$children.find(child => child.$options.name === 'TaskCanvas')
        if (canvasComponent) {
          canvasComponent.drawTasks()
        }
      })
      this.$message.success('画布已刷新')
    },
    
    exportCanvas() {
      // 导出Canvas为图片
      const canvasComponent = this.$children.find(child => child.$options.name === 'TaskCanvas')
      if (canvasComponent) {
        const canvas = canvasComponent.$refs.taskCanvas
        const link = document.createElement('a')
        link.download = 'task-canvas.png'
        link.href = canvas.toDataURL()
        link.click()
        this.$message.success('图片已导出')
      }
    },
    
    toggleFullscreen() {
      const element = document.querySelector('.canvas-wrapper')
      if (!document.fullscreenElement) {
        element.requestFullscreen().then(() => {
          this.$message.success('已进入全屏模式')
        }).catch(() => {
          this.$message.error('全屏模式失败')
        })
      } else {
        document.exitFullscreen().then(() => {
          this.$message.success('已退出全屏模式')
        })
      }
    }
  }
}
</script>

<style scoped>
.canvas-demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
  font-size: 1.2em;
  opacity: 0.9;
}

.canvas-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 40px;
}

.controls .el-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.controls .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.features {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  color: white;
}

.features h2 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.8em;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  padding: 8px 0;
  font-size: 1.1em;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.features li:last-child {
  border-bottom: none;
}

.video-placeholder {
  text-align: center;
  padding: 40px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 20px 0;
}

.video-placeholder p {
  margin-top: 10px;
  color: #666;
}

/* 全屏样式 */
.canvas-wrapper:fullscreen {
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header h1 {
    font-size: 2em;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
  
  .features {
    margin: 0 10px;
    padding: 20px;
  }
}
</style>
