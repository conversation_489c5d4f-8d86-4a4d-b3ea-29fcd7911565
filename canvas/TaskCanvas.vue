<template>
  <div class="task-canvas-container">
    <canvas 
      ref="taskCanvas" 
      :width="canvasWidth" 
      :height="canvasHeight"
      @click="handleCanvasClick"
      @mousemove="handleMouseMove"
    ></canvas>
  </div>
</template>

<script>
export default {
  name: 'TaskCanvas',
  data() {
    return {
      canvasWidth: 1200,
      canvasHeight: 800,
      ctx: null,
      tasks: [
        {
          id: 1,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: false,
          items: [
            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
          ]
        },
        {
          id: 2,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: true,
          items: [
            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
          ]
        },
        {
          id: 3,
          title: '人教子-应知要求',
          dateRange: '2025/06/01-2025/06/01',
          isLocked: false,
          items: [
            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
          ]
        }
      ],
      cardWidth: 350,
      cardHeight: 250,
      cardSpacing: 30,
      hoveredCard: null,
      clickableAreas: []
    }
  },
  mounted() {
    this.initCanvas()
    this.drawTasks()
  },
  methods: {
    initCanvas() {
      const canvas = this.$refs.taskCanvas
      this.ctx = canvas.getContext('2d')
      this.ctx.font = '14px Arial'
    },
    
    drawTasks() {
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
      this.clickableAreas = []
      
      // 绘制连接线
      this.drawConnectingLines()
      
      // 绘制任务卡片
      this.tasks.forEach((task, index) => {
        const row = Math.floor(index / 3)
        const col = index % 3
        const x = 50 + col * (this.cardWidth + this.cardSpacing)
        const y = 50 + row * (this.cardHeight + this.cardSpacing)
        
        this.drawTaskCard(task, x, y, index)
      })
    },
    
    drawConnectingLines() {
      this.ctx.strokeStyle = '#7BA7E7'
      this.ctx.lineWidth = 8
      this.ctx.lineCap = 'round'
      
      // 绘制S形连接线
      this.ctx.beginPath()
      
      // 第一行到第二行的连接
      const startX = 50 + 2 * (this.cardWidth + this.cardSpacing) + this.cardWidth
      const startY = 50 + this.cardHeight / 2
      const endX = 50
      const endY = 50 + this.cardHeight + this.cardSpacing + this.cardHeight / 2
      
      // S形曲线
      this.ctx.moveTo(startX, startY)
      this.ctx.bezierCurveTo(
        startX + 100, startY,
        startX + 100, endY,
        endX, endY
      )
      
      // 第二行内部连接
      this.ctx.moveTo(endX, endY)
      this.ctx.lineTo(endX + 2 * (this.cardWidth + this.cardSpacing), endY)
      
      // 箭头
      const arrowX = endX + 2 * (this.cardWidth + this.cardSpacing) + this.cardWidth
      const arrowY = endY
      this.ctx.lineTo(arrowX, arrowY)
      
      // 绘制箭头头部
      this.ctx.lineTo(arrowX - 15, arrowY - 10)
      this.ctx.moveTo(arrowX, arrowY)
      this.ctx.lineTo(arrowX - 15, arrowY + 10)
      
      this.ctx.stroke()
    },
    
    drawTaskCard(task, x, y, index) {
      const isHovered = this.hoveredCard === index
      
      // 卡片背景
      this.ctx.fillStyle = isHovered ? '#f8f9fa' : '#ffffff'
      this.ctx.strokeStyle = '#e0e0e0'
      this.ctx.lineWidth = 1
      this.roundRect(x, y, this.cardWidth, this.cardHeight, 8)
      this.ctx.fill()
      this.ctx.stroke()
      
      // 阴影效果
      if (isHovered) {
        this.ctx.shadowColor = 'rgba(0,0,0,0.1)'
        this.ctx.shadowBlur = 10
        this.ctx.shadowOffsetX = 2
        this.ctx.shadowOffsetY = 2
      }
      
      // 标题区域
      this.ctx.fillStyle = '#f5f5f5'
      this.roundRect(x, y, this.cardWidth, 40, 8, true, false)
      this.ctx.fill()
      
      // 锁定图标
      if (task.isLocked) {
        this.drawLockIcon(x + this.cardWidth - 30, y + 10)
      }
      
      // 标题文字
      this.ctx.fillStyle = '#333'
      this.ctx.font = 'bold 16px Arial'
      this.ctx.fillText(task.title, x + 15, y + 25)
      
      // 日期
      this.ctx.font = '12px Arial'
      this.ctx.fillStyle = '#666'
      this.ctx.fillText(`学习日期：${task.dateRange}`, x + 15, y + 60)
      
      // 表头
      this.ctx.fillStyle = '#333'
      this.ctx.font = '12px Arial'
      this.ctx.fillText('序号', x + 15, y + 85)
      this.ctx.fillText('学习目标名称', x + 50, y + 85)
      this.ctx.fillText('学习方式', x + 180, y + 85)
      this.ctx.fillText('完成度', x + 250, y + 85)
      
      // 任务列表
      task.items.forEach((item, itemIndex) => {
        const itemY = y + 105 + itemIndex * 20
        
        this.ctx.fillStyle = '#333'
        this.ctx.fillText(item.id.toString(), x + 20, itemY)
        
        // 截断长文本
        const maxNameLength = 12
        const displayName = item.name.length > maxNameLength 
          ? item.name.substring(0, maxNameLength) + '...' 
          : item.name
        this.ctx.fillText(displayName, x + 50, itemY)
        
        this.ctx.fillText(item.type, x + 185, itemY)
        
        // 进度显示
        if (item.progress === '点击播放') {
          this.ctx.fillStyle = '#1890ff'
          this.ctx.fillText(item.progress, x + 250, itemY)
          
          // 记录可点击区域
          this.clickableAreas.push({
            x: x + 250,
            y: itemY - 10,
            width: 60,
            height: 15,
            action: 'play',
            taskId: task.id,
            itemId: item.id
          })
        } else {
          this.ctx.fillStyle = '#333'
          this.ctx.fillText(item.progress, x + 250, itemY)
        }
      })
      
      // 重置阴影
      this.ctx.shadowColor = 'transparent'
      this.ctx.shadowBlur = 0
      this.ctx.shadowOffsetX = 0
      this.ctx.shadowOffsetY = 0
    },
    
    drawLockIcon(x, y) {
      this.ctx.fillStyle = '#999'
      this.ctx.strokeStyle = '#999'
      this.ctx.lineWidth = 2
      
      // 锁身
      this.roundRect(x, y + 8, 16, 12, 2)
      this.ctx.fill()
      
      // 锁环
      this.ctx.beginPath()
      this.ctx.arc(x + 8, y + 6, 4, Math.PI, 0, false)
      this.ctx.stroke()
    },
    
    roundRect(x, y, width, height, radius, topOnly = false, bottomOnly = false) {
      this.ctx.beginPath()
      
      if (topOnly) {
        this.ctx.moveTo(x + radius, y)
        this.ctx.lineTo(x + width - radius, y)
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
        this.ctx.lineTo(x + width, y + height)
        this.ctx.lineTo(x, y + height)
        this.ctx.lineTo(x, y + radius)
        this.ctx.quadraticCurveTo(x, y, x + radius, y)
      } else if (bottomOnly) {
        this.ctx.moveTo(x, y)
        this.ctx.lineTo(x + width, y)
        this.ctx.lineTo(x + width, y + height - radius)
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
        this.ctx.lineTo(x + radius, y + height)
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
        this.ctx.lineTo(x, y)
      } else {
        this.ctx.moveTo(x + radius, y)
        this.ctx.lineTo(x + width - radius, y)
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
        this.ctx.lineTo(x + width, y + height - radius)
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
        this.ctx.lineTo(x + radius, y + height)
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
        this.ctx.lineTo(x, y + radius)
        this.ctx.quadraticCurveTo(x, y, x + radius, y)
      }
      
      this.ctx.closePath()
    },
    
    handleCanvasClick(event) {
      const rect = this.$refs.taskCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      // 检查点击的可点击区域
      for (const area of this.clickableAreas) {
        if (x >= area.x && x <= area.x + area.width &&
            y >= area.y && y <= area.y + area.height) {
          this.handleAction(area)
          break
        }
      }
    },
    
    handleMouseMove(event) {
      const rect = this.$refs.taskCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      let hoveredCard = null
      
      // 检查鼠标是否在某个卡片上
      this.tasks.forEach((task, index) => {
        const row = Math.floor(index / 3)
        const col = index % 3
        const cardX = 50 + col * (this.cardWidth + this.cardSpacing)
        const cardY = 50 + row * (this.cardHeight + this.cardSpacing)
        
        if (x >= cardX && x <= cardX + this.cardWidth &&
            y >= cardY && y <= cardY + this.cardHeight) {
          hoveredCard = index
        }
      })
      
      // 检查是否在可点击区域上
      let isOverClickable = false
      for (const area of this.clickableAreas) {
        if (x >= area.x && x <= area.x + area.width &&
            y >= area.y && y <= area.y + area.height) {
          isOverClickable = true
          break
        }
      }
      
      // 设置鼠标样式
      this.$refs.taskCanvas.style.cursor = isOverClickable ? 'pointer' : 'default'
      
      if (this.hoveredCard !== hoveredCard) {
        this.hoveredCard = hoveredCard
        this.drawTasks()
      }
    },
    
    handleAction(area) {
      if (area.action === 'play') {
        this.$emit('play-item', {
          taskId: area.taskId,
          itemId: area.itemId
        })
        console.log(`播放任务 ${area.taskId} 的项目 ${area.itemId}`)
      }
    }
  }
}
</script>

<style scoped>
.task-canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
}

canvas {
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
}
</style>
