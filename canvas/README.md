# Canvas 学习任务管理系统

这是一个使用HTML5 Canvas技术实现的学习任务管理界面，完全基于Canvas绘制，提供丰富的交互功能。

## 功能特点

### 🎨 Canvas绘制
- 完全使用Canvas API绘制界面
- 自定义圆角矩形、阴影效果
- 流畅的S形连接线动画
- 高质量的图形渲染

### 🖱️ 交互功能
- 鼠标悬停高亮效果
- 可点击的"播放"按钮
- 鼠标样式自动切换
- 响应式点击区域检测

### 📋 任务管理
- 多个任务卡片展示
- 锁定状态显示
- 学习进度跟踪
- 任务项目列表

### 🎯 视觉效果
- 现代化卡片设计
- 渐变背景
- 阴影和高光效果
- 图标和状态指示器

## 文件结构

```
canvas/
├── TaskCanvas.vue      # 主要的Canvas组件
├── index.vue          # 示例页面
└── README.md          # 说明文档
```

## 组件说明

### TaskCanvas.vue
主要的Canvas绘制组件，包含以下功能：

#### Props
- 无需传入props，数据在组件内部定义

#### Events
- `play-item`: 当用户点击"播放"按钮时触发
  - 参数: `{ taskId: number, itemId: number }`

#### 主要方法
- `initCanvas()`: 初始化Canvas上下文
- `drawTasks()`: 绘制所有任务卡片
- `drawTaskCard()`: 绘制单个任务卡片
- `drawConnectingLines()`: 绘制连接线
- `drawLockIcon()`: 绘制锁定图标
- `roundRect()`: 绘制圆角矩形
- `handleCanvasClick()`: 处理点击事件
- `handleMouseMove()`: 处理鼠标移动事件

### index.vue
示例页面，展示如何使用TaskCanvas组件：

#### 功能
- 展示Canvas组件
- 提供控制按钮（刷新、导出、全屏）
- 播放对话框
- 功能特点说明

## 使用方法

### 1. 基本使用

```vue
<template>
  <div>
    <TaskCanvas @play-item="handlePlayItem" />
  </div>
</template>

<script>
import TaskCanvas from './canvas/TaskCanvas.vue'

export default {
  components: {
    TaskCanvas
  },
  methods: {
    handlePlayItem(item) {
      console.log('播放项目:', item)
      // 处理播放逻辑
    }
  }
}
</script>
```

### 2. 自定义数据

修改TaskCanvas.vue中的`tasks`数据：

```javascript
data() {
  return {
    tasks: [
      {
        id: 1,
        title: '自定义任务标题',
        dateRange: '2025/06/01-2025/06/01',
        isLocked: false,
        items: [
          { 
            id: 1, 
            name: '学习项目名称', 
            type: '视频', 
            progress: '点击播放' 
          }
          // 更多项目...
        ]
      }
      // 更多任务...
    ]
  }
}
```

### 3. 样式自定义

可以修改以下变量来调整外观：

```javascript
data() {
  return {
    cardWidth: 350,      // 卡片宽度
    cardHeight: 250,     // 卡片高度
    cardSpacing: 30,     // 卡片间距
    canvasWidth: 1200,   // 画布宽度
    canvasHeight: 800    // 画布高度
  }
}
```

## 技术实现

### Canvas绘制技术
- 使用`getContext('2d')`获取2D绘制上下文
- 实现自定义圆角矩形绘制函数
- 使用贝塞尔曲线绘制S形连接线
- 实现阴影和渐变效果

### 交互检测
- 维护可点击区域数组
- 鼠标事件坐标转换
- 碰撞检测算法
- 动态鼠标样式切换

### 性能优化
- 按需重绘机制
- 事件节流处理
- 内存管理优化

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 扩展功能

可以进一步扩展的功能：
- 拖拽排序
- 动画过渡效果
- 数据持久化
- 多主题支持
- 响应式布局
- 触摸设备支持

## 注意事项

1. Canvas是位图，在高DPI屏幕上可能需要适配
2. 文字渲染可能在不同浏览器上有细微差异
3. 大量数据时需要考虑性能优化
4. 无障碍访问需要额外处理

## 开发建议

1. 使用Canvas时要注意坐标系统
2. 复杂图形建议拆分为多个绘制函数
3. 交互区域要准确计算边界
4. 适当使用缓存提高性能
