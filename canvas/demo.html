<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 学习任务管理系统 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        #taskCanvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: white;
            cursor: default;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
        }

        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary {
            background: #409EFF;
            color: white;
        }

        .btn-success {
            background: #67C23A;
            color: white;
        }

        .btn-info {
            background: #909399;
            color: white;
        }

        .features {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            color: white;
        }

        .features h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            font-size: 1.1em;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .features li:last-child {
            border-bottom: none;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .video-placeholder {
            text-align: center;
            padding: 40px;
            background: #f5f5f5;
            border-radius: 8px;
            margin: 20px 0;
        }

        .play-icon {
            font-size: 48px;
            color: #409EFF;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Canvas 学习任务管理系统</h1>
            <p>使用Canvas绘制的交互式学习任务界面</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="taskCanvas" width="1200" height="800"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshCanvas()">刷新画布</button>
            <button class="btn btn-success" onclick="exportCanvas()">导出图片</button>
            <button class="btn btn-info" onclick="toggleFullscreen()">全屏显示</button>
        </div>
        
        <div class="features">
            <h2>功能特点</h2>
            <ul>
                <li>✅ 使用Canvas绘制任务卡片</li>
                <li>✅ 鼠标悬停效果</li>
                <li>✅ 可点击的"播放"按钮</li>
                <li>✅ 锁定状态显示</li>
                <li>✅ S形连接线动画</li>
                <li>✅ 响应式交互</li>
            </ul>
        </div>
    </div>

    <!-- 播放模态框 -->
    <div id="playModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>播放学习内容</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalBody">
                <div class="video-placeholder">
                    <div class="play-icon">▶</div>
                    <p>视频播放区域</p>
                    <p id="playInfo"></p>
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-primary" onclick="closeModal()">完成学习</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas任务管理系统
        class TaskCanvasManager {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.canvasWidth = 1200;
                this.canvasHeight = 800;
                this.cardWidth = 350;
                this.cardHeight = 250;
                this.cardSpacing = 30;
                this.hoveredCard = null;
                this.clickableAreas = [];
                
                this.tasks = [
                    {
                        id: 1,
                        title: '人教子-应知要求',
                        dateRange: '2025/06/01-2025/06/01',
                        isLocked: false,
                        items: [
                            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
                            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
                            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
                            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
                            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
                            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
                        ]
                    },
                    {
                        id: 2,
                        title: '人教子-应知要求',
                        dateRange: '2025/06/01-2025/06/01',
                        isLocked: true,
                        items: [
                            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
                            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
                            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
                            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
                            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
                            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
                        ]
                    },
                    {
                        id: 3,
                        title: '人教子-应知要求',
                        dateRange: '2025/06/01-2025/06/01',
                        isLocked: false,
                        items: [
                            { id: 1, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' },
                            { id: 2, name: '《思想政治、思想政治》', type: '视频', progress: '80%' },
                            { id: 3, name: '《理论理解要求与认识》', type: '视频', progress: '0%' },
                            { id: 4, name: '《理论要求与认识》', type: '视频', progress: '点击播放' },
                            { id: 5, name: '《理论认识理解的方法》', type: '视频', progress: '0%' },
                            { id: 6, name: '人学习目标材料要求学习', type: '视频', progress: '点击播放' }
                        ]
                    }
                ];
                
                this.init();
            }
            
            init() {
                this.ctx.font = '14px Arial';
                this.drawTasks();
                this.bindEvents();
            }
            
            bindEvents() {
                this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
            }
            
            drawTasks() {
                this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
                this.clickableAreas = [];
                
                this.drawConnectingLines();
                
                this.tasks.forEach((task, index) => {
                    const row = Math.floor(index / 3);
                    const col = index % 3;
                    const x = 50 + col * (this.cardWidth + this.cardSpacing);
                    const y = 50 + row * (this.cardHeight + this.cardSpacing);
                    
                    this.drawTaskCard(task, x, y, index);
                });
            }
            
            drawConnectingLines() {
                this.ctx.strokeStyle = '#7BA7E7';
                this.ctx.lineWidth = 8;
                this.ctx.lineCap = 'round';
                
                this.ctx.beginPath();
                
                const startX = 50 + 2 * (this.cardWidth + this.cardSpacing) + this.cardWidth;
                const startY = 50 + this.cardHeight / 2;
                const endX = 50;
                const endY = 50 + this.cardHeight + this.cardSpacing + this.cardHeight / 2;
                
                this.ctx.moveTo(startX, startY);
                this.ctx.bezierCurveTo(
                    startX + 100, startY,
                    startX + 100, endY,
                    endX, endY
                );
                
                this.ctx.moveTo(endX, endY);
                this.ctx.lineTo(endX + 2 * (this.cardWidth + this.cardSpacing), endY);
                
                const arrowX = endX + 2 * (this.cardWidth + this.cardSpacing) + this.cardWidth;
                const arrowY = endY;
                this.ctx.lineTo(arrowX, arrowY);
                
                this.ctx.lineTo(arrowX - 15, arrowY - 10);
                this.ctx.moveTo(arrowX, arrowY);
                this.ctx.lineTo(arrowX - 15, arrowY + 10);
                
                this.ctx.stroke();
            }
            
            drawTaskCard(task, x, y, index) {
                const isHovered = this.hoveredCard === index;
                
                this.ctx.fillStyle = isHovered ? '#f8f9fa' : '#ffffff';
                this.ctx.strokeStyle = '#e0e0e0';
                this.ctx.lineWidth = 1;
                this.roundRect(x, y, this.cardWidth, this.cardHeight, 8);
                this.ctx.fill();
                this.ctx.stroke();
                
                if (isHovered) {
                    this.ctx.shadowColor = 'rgba(0,0,0,0.1)';
                    this.ctx.shadowBlur = 10;
                    this.ctx.shadowOffsetX = 2;
                    this.ctx.shadowOffsetY = 2;
                }
                
                this.ctx.fillStyle = '#f5f5f5';
                this.roundRect(x, y, this.cardWidth, 40, 8, true, false);
                this.ctx.fill();
                
                if (task.isLocked) {
                    this.drawLockIcon(x + this.cardWidth - 30, y + 10);
                }
                
                this.ctx.fillStyle = '#333';
                this.ctx.font = 'bold 16px Arial';
                this.ctx.fillText(task.title, x + 15, y + 25);
                
                this.ctx.font = '12px Arial';
                this.ctx.fillStyle = '#666';
                this.ctx.fillText(`学习日期：${task.dateRange}`, x + 15, y + 60);
                
                this.ctx.fillStyle = '#333';
                this.ctx.font = '12px Arial';
                this.ctx.fillText('序号', x + 15, y + 85);
                this.ctx.fillText('学习目标名称', x + 50, y + 85);
                this.ctx.fillText('学习方式', x + 180, y + 85);
                this.ctx.fillText('完成度', x + 250, y + 85);
                
                task.items.forEach((item, itemIndex) => {
                    const itemY = y + 105 + itemIndex * 20;
                    
                    this.ctx.fillStyle = '#333';
                    this.ctx.fillText(item.id.toString(), x + 20, itemY);
                    
                    const maxNameLength = 12;
                    const displayName = item.name.length > maxNameLength 
                        ? item.name.substring(0, maxNameLength) + '...' 
                        : item.name;
                    this.ctx.fillText(displayName, x + 50, itemY);
                    
                    this.ctx.fillText(item.type, x + 185, itemY);
                    
                    if (item.progress === '点击播放') {
                        this.ctx.fillStyle = '#1890ff';
                        this.ctx.fillText(item.progress, x + 250, itemY);
                        
                        this.clickableAreas.push({
                            x: x + 250,
                            y: itemY - 10,
                            width: 60,
                            height: 15,
                            action: 'play',
                            taskId: task.id,
                            itemId: item.id
                        });
                    } else {
                        this.ctx.fillStyle = '#333';
                        this.ctx.fillText(item.progress, x + 250, itemY);
                    }
                });
                
                this.ctx.shadowColor = 'transparent';
                this.ctx.shadowBlur = 0;
                this.ctx.shadowOffsetX = 0;
                this.ctx.shadowOffsetY = 0;
            }
            
            drawLockIcon(x, y) {
                this.ctx.fillStyle = '#999';
                this.ctx.strokeStyle = '#999';
                this.ctx.lineWidth = 2;
                
                this.roundRect(x, y + 8, 16, 12, 2);
                this.ctx.fill();
                
                this.ctx.beginPath();
                this.ctx.arc(x + 8, y + 6, 4, Math.PI, 0, false);
                this.ctx.stroke();
            }
            
            roundRect(x, y, width, height, radius, topOnly = false, bottomOnly = false) {
                this.ctx.beginPath();
                
                if (topOnly) {
                    this.ctx.moveTo(x + radius, y);
                    this.ctx.lineTo(x + width - radius, y);
                    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                    this.ctx.lineTo(x + width, y + height);
                    this.ctx.lineTo(x, y + height);
                    this.ctx.lineTo(x, y + radius);
                    this.ctx.quadraticCurveTo(x, y, x + radius, y);
                } else {
                    this.ctx.moveTo(x + radius, y);
                    this.ctx.lineTo(x + width - radius, y);
                    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                    this.ctx.lineTo(x + width, y + height - radius);
                    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                    this.ctx.lineTo(x + radius, y + height);
                    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
                    this.ctx.lineTo(x, y + radius);
                    this.ctx.quadraticCurveTo(x, y, x + radius, y);
                }
                
                this.ctx.closePath();
            }
            
            handleCanvasClick(event) {
                const rect = this.canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                
                for (const area of this.clickableAreas) {
                    if (x >= area.x && x <= area.x + area.width &&
                        y >= area.y && y <= area.y + area.height) {
                        this.handleAction(area);
                        break;
                    }
                }
            }
            
            handleMouseMove(event) {
                const rect = this.canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                
                let hoveredCard = null;
                
                this.tasks.forEach((task, index) => {
                    const row = Math.floor(index / 3);
                    const col = index % 3;
                    const cardX = 50 + col * (this.cardWidth + this.cardSpacing);
                    const cardY = 50 + row * (this.cardHeight + this.cardSpacing);
                    
                    if (x >= cardX && x <= cardX + this.cardWidth &&
                        y >= cardY && y <= cardY + this.cardHeight) {
                        hoveredCard = index;
                    }
                });
                
                let isOverClickable = false;
                for (const area of this.clickableAreas) {
                    if (x >= area.x && x <= area.x + area.width &&
                        y >= area.y && y <= area.y + area.height) {
                        isOverClickable = true;
                        break;
                    }
                }
                
                this.canvas.style.cursor = isOverClickable ? 'pointer' : 'default';
                
                if (this.hoveredCard !== hoveredCard) {
                    this.hoveredCard = hoveredCard;
                    this.drawTasks();
                }
            }
            
            handleAction(area) {
                if (area.action === 'play') {
                    showPlayModal(area.taskId, area.itemId);
                }
            }
        }
        
        // 初始化Canvas管理器
        let taskManager;
        
        window.onload = function() {
            taskManager = new TaskCanvasManager('taskCanvas');
        };
        
        // 控制函数
        function refreshCanvas() {
            if (taskManager) {
                taskManager.drawTasks();
                alert('画布已刷新');
            }
        }
        
        function exportCanvas() {
            const canvas = document.getElementById('taskCanvas');
            const link = document.createElement('a');
            link.download = 'task-canvas.png';
            link.href = canvas.toDataURL();
            link.click();
            alert('图片已导出');
        }
        
        function toggleFullscreen() {
            const element = document.querySelector('.canvas-container');
            if (!document.fullscreenElement) {
                element.requestFullscreen().then(() => {
                    alert('已进入全屏模式');
                }).catch(() => {
                    alert('全屏模式失败');
                });
            } else {
                document.exitFullscreen().then(() => {
                    alert('已退出全屏模式');
                });
            }
        }
        
        function showPlayModal(taskId, itemId) {
            const modal = document.getElementById('playModal');
            const playInfo = document.getElementById('playInfo');
            playInfo.textContent = `任务ID: ${taskId}, 项目ID: ${itemId}`;
            modal.style.display = 'block';
        }
        
        function closeModal() {
            const modal = document.getElementById('playModal');
            modal.style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('playModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
