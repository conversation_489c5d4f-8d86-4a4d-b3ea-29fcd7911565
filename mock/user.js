import { param2Obj } from './utils'

const tokens = {
	admin: {
		token: 'admin-token'
	},
	editor: {
		token: 'editor-token'
	}
}

const users = {
	'admin-token': {
		roles: ['admin'],
		introduction: 'I am a super administrator',
		avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
		name: 'Super Admin'
	},
	'editor-token': {
		roles: ['editor'],
		introduction: 'I am an editor',
		avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
		name: 'Normal Editor'
	}
}

export default {
	login: res => {
		const {
			username
		} = JSON.parse(res.body)
		const data = tokens[username]

		if(data) {
			return {
				code: 0,
				data
			}
		}
		return {
			code: 60204,
			message: 'Account and password are incorrect.'
		}
	},
	getInfo: res => {
		const {
			token
		} = param2Obj(res.url)
		const info = users[token]

		if(info) {
//			return info
			return {
				code: 0,
				data: info
			}
		}
		return {
			code: 50008,
			message: '<PERSON><PERSON> failed, unable to get user details.'
		}
	},
	getUserInfo: config => {
		const {
			token
		} = param2Obj(config.url)
		if(userMap[token]) {
			return userMap[token]
		} else {
			return false
		}
	},
	logout: () => {
		return {
			code: 0,
			data: 'success'
		}
	}
}