module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module',
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ['plugin:vue/recommended', 'eslint:recommended', 'prettier'],
  rules: {
    'vue/no-parsing-error': [
      2,
      {
        'x-invalid-end-tag': 0,
      },
    ],
    'no-console': 0,
    'no-undef': 0,
    'no-unused-vars': 0,
    'no-parsing-error': 0,
    'no-debugger': 0,
    'no-unreachable': 0,
  },
};
