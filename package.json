{"name": "kkpcIm", "version": "1.0.0", "license": "MIT", "description": "A vue projrect with Element UI & axios & iconfont & permission control & lint", "author": "haonan", "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build:kkprod": "NODE_ENV_TYPE=kkprod node build/build.js", "build:ykyprod": "NODE_ENV_TYPE=ykyprod node build/build.js", "build:kktest": "NODE_ENV_TYPE=kktest node build/build.js", "build:ykytest": "NODE_ENV_TYPE=ykytest node build/build.js", "build:report": "npm_config_report=true npm run build", "lint": "eslint --ext .js,.vue src", "test": "npm run lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@xkit-yx/im-kit-ui": "^9.8.0", "ali-oss": "^6.18.1", "area-data": "^5.0.6", "axios": "0.18.0", "bignumber.js": "^9.1.2", "element-ui": "^2.15.14", "js-cookie": "2.2.0", "js-image-compressor": "^2.0.0", "js-md5": "^0.7.3", "js-sha1": "^0.6.0", "jsx-web-compiler": "^1.0.2", "lodash": "^4.17.21", "mobx": "^6.6.1", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "qrcodejs": "^1.0.0", "react": "^16.8.6", "react-dom": "^16.8.6", "swiper": "^11.0.7", "vuc-identify": "^1.1.1", "vue": "2.5.17", "vue-area-linkage": "^5.1.0", "vue-awesome-swiper": "^5.0.1", "vue-count-to": "^1.0.13", "vue-i18n": "^8.11.2", "vue-lazyload": "^1.3.5", "vue-meta": "^2.4.0", "vue-router": "3.0.1", "vue-router-sitemap": "0.0.4", "vue-seamless-scroll": "^1.1.23", "vuex": "3.0.1", "yunxin-log-debug": "^1.1.6"}, "devDependencies": {"@vue/runtime-dom": "^3.4.27", "autoprefixer": "8.5.0", "babel-core": "6.26.0", "babel-eslint": "8.2.6", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "7.1.5", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.7.0", "babel-preset-env": "1.7.0", "babel-preset-stage-2": "6.24.1", "chalk": "2.4.1", "compression-webpack-plugin": "2.0.0", "copy-webpack-plugin": "4.5.2", "css-loader": "1.0.0", "eslint": "^4.19.1", "eslint-config-prettier": "^9.1.0", "eslint-friendly-formatter": "4.0.1", "eslint-loader": "2.0.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "4.7.1", "eventsource-polyfill": "0.9.6", "file-loader": "1.1.11", "friendly-errors-webpack-plugin": "1.7.0", "html-webpack-plugin": "4.0.0-alpha", "mini-css-extract-plugin": "0.4.1", "node-notifier": "5.2.1", "node-sass": "^4.7.2", "optimize-css-assets-webpack-plugin": "5.0.0", "ora": "3.0.0", "path-to-regexp": "2.4.0", "portfinder": "1.0.16", "postcss-import": "12.0.0", "postcss-loader": "2.1.6", "postcss-url": "7.3.2", "prettier": "2.6.2", "qrcode": "^1.5.3", "rimraf": "2.6.2", "sass-loader": "7.0.3", "script-ext-html-webpack-plugin": "2.0.1", "semver": "5.5.0", "shelljs": "^0.8.5", "svg-sprite-loader": "3.8.0", "svgo": "1.0.5", "terser-webpack-plugin": "^4.2.3", "url-loader": "1.0.1", "vue-loader": "15.3.0", "vue-style-loader": "4.1.2", "vue-template-compiler": "2.5.17", "webpack": "4.16.5", "webpack-bundle-analyzer": "2.13.1", "webpack-cli": "3.1.0", "webpack-dev-server": "3.1.14", "webpack-merge": "4.1.4"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}