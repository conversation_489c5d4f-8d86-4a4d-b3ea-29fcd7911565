<svg width="170" height="50" viewBox="0 0 170 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_673_40733)">
<rect width="170" height="50" rx="24" fill="#FFF500"/>
<rect width="170" height="50" rx="24" fill="url(#paint0_radial_673_40733)"/>
<rect width="170" height="50" rx="24" fill="url(#paint1_radial_673_40733)" fill-opacity="0.9"/>
<rect width="170" height="50" rx="24" fill="url(#paint2_radial_673_40733)" fill-opacity="0.6"/>
<rect width="170" height="50" rx="24" fill="url(#paint3_radial_673_40733)" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_i_673_40733" x="0" y="0" width="170" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_673_40733"/>
</filter>
<radialGradient id="paint0_radial_673_40733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(161.307 -66.4062) rotate(147.419) scale(185.702 117.137)">
<stop offset="0.67589" stop-color="#FF7A00"/>
<stop offset="1" stop-color="#FFC700" stop-opacity="0.38"/>
</radialGradient>
<radialGradient id="paint1_radial_673_40733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(56.0227 -1.78984e-06) rotate(53.6791) scale(71.7533 133.012)">
<stop offset="0.212469" stop-color="#FF8989" stop-opacity="0.92"/>
<stop offset="0.886241" stop-color="#FFA96A" stop-opacity="0.57"/>
</radialGradient>
<radialGradient id="paint2_radial_673_40733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9.65909 50) rotate(-16.3333) scale(69.4506 98.2459)">
<stop stop-color="#F6FB22" stop-opacity="0.51"/>
<stop offset="1" stop-color="#FF9E45" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_673_40733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(164.205 15.625) rotate(163.61) scale(55.3751 128.353)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
