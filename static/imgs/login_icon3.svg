<svg width="242" height="108" viewBox="0 0 242 108" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_313_113)">
<path d="M10 50C10 25.6995 27.9233 6 50.0328 6H191.967C214.077 6 232 25.6995 232 50C232 74.3005 214.077 94 191.967 94H50.0328C27.9233 94 10 74.3005 10 50Z" fill="#FBF9F7" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_313_113)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M191.967 6.8H50.0328C28.3253 6.8 10.7279 26.1413 10.7279 50C10.7279 73.8587 28.3253 93.2 50.0328 93.2H191.967C213.675 93.2 231.272 73.8587 231.272 50C231.272 26.1413 213.675 6.8 191.967 6.8ZM50.0328 6C27.9233 6 10 25.6995 10 50C10 74.3005 27.9233 94 50.0328 94H191.967C214.077 94 232 74.3005 232 50C232 25.6995 214.077 6 191.967 6H50.0328Z" fill="#FBF9F7"/>
</g>
<rect x="27" y="20" width="62" height="61" rx="30.5" fill="#FFF500"/>
<rect x="27" y="20" width="62" height="61" rx="30.5" fill="url(#paint0_radial_313_113)"/>
<rect x="27" y="20" width="62" height="61" rx="30.5" fill="url(#paint1_radial_313_113)" fill-opacity="0.9"/>
<rect x="27" y="20" width="62" height="61" rx="30.5" fill="url(#paint2_radial_313_113)" fill-opacity="0.6"/>
<rect x="27" y="20" width="62" height="61" rx="30.5" fill="url(#paint3_radial_313_113)" fill-opacity="0.1"/>
<path d="M60.8215 38.4346C55.5512 38.4346 51.5754 40.6828 51.5754 43.663V49.6959L51.2176 49.7361C50.1366 49.8554 49.0998 50.231 48.1931 50.8318C47.2865 51.4326 46.5362 52.2412 46.0047 53.1904C45.4732 54.1395 45.1758 55.2019 45.1371 56.2892C45.0985 57.3765 45.3198 58.4573 45.7826 59.4419C46.2454 60.4264 46.9363 61.2862 47.7981 61.9499C48.6598 62.6136 49.6673 63.0619 50.7371 63.2577C51.8068 63.4535 52.9078 63.3911 53.9486 63.0756C54.9893 62.7601 55.9398 62.2007 56.721 61.4439L56.8778 61.291L57.0909 61.3433C58.3138 61.628 59.5659 61.7684 60.8215 61.7616C66.0918 61.7616 70.0676 59.5134 70.0676 56.5331V43.663C70.0676 40.6828 66.0918 38.4346 60.8215 38.4346ZM51.9774 60.9572C51.1028 60.9572 50.2478 60.6977 49.5206 60.2116C48.7934 59.7255 48.2266 59.0345 47.8919 58.2262C47.5572 57.4178 47.4696 56.5282 47.6403 55.67C47.8109 54.8118 48.2321 54.0235 48.8505 53.4048C49.4689 52.7861 50.2569 52.3647 51.1147 52.194C51.9725 52.0233 52.8616 52.1109 53.6696 52.4458C54.4776 52.7806 55.1683 53.3477 55.6542 54.0752C56.1401 54.8028 56.3994 55.6581 56.3994 56.5331C56.3994 57.7065 55.9335 58.8318 55.1042 59.6614C54.2749 60.4911 53.1502 60.9572 51.9774 60.9572ZM67.6556 56.5331C67.6556 57.8362 64.6687 59.3485 60.8215 59.3485C60.1396 59.3499 59.4585 59.3029 58.7833 59.2077L58.3089 59.1393L58.4617 58.6808C58.7877 57.7219 58.8881 56.7007 58.7552 55.6966L58.6949 55.1979L59.1934 55.2461C59.732 55.2984 60.2828 55.3225 60.8215 55.3225C62.9791 55.3669 65.1179 54.9142 67.0727 53.9993L67.6556 53.7178V56.5331ZM67.6556 50.0981C67.6556 51.4012 64.6687 52.9134 60.8215 52.9134C59.7497 52.9181 58.681 52.7966 57.6376 52.5514L57.4969 52.5112L57.4085 52.3986C56.6036 51.3417 55.5084 50.5423 54.2567 50.0981L53.9874 50.0016V47.2828L54.5703 47.5804C56.526 48.4911 58.6648 48.9397 60.8215 48.8915C62.9791 48.9359 65.1179 48.4832 67.0727 47.5683L67.6556 47.2828V50.0981ZM60.8215 46.4784C56.9743 46.4784 53.9874 44.9661 53.9874 43.663C53.9874 42.3599 56.9743 40.8477 60.8215 40.8477C64.6687 40.8477 67.6556 42.3599 67.6556 43.663C67.6556 44.9661 64.6687 46.4784 60.8215 46.4784Z" fill="white"/>
<defs>
<filter id="filter0_d_313_113" x="0" y="0" width="242" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_313_113"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_313_113" result="shape"/>
</filter>
<filter id="filter1_d_313_113" x="0" y="0" width="242" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_313_113"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_313_113" result="shape"/>
</filter>
<radialGradient id="paint0_radial_313_113" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(85.8295 -61.0156) rotate(115.069) scale(134.688 71.8597)">
<stop offset="0.67589" stop-color="#FF7A00"/>
<stop offset="1" stop-color="#FFC700" stop-opacity="0.38"/>
</radialGradient>
<radialGradient id="paint1_radial_313_113" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(47.4318 20) rotate(77.6057) scale(72.2143 58.8045)">
<stop offset="0.212469" stop-color="#FF8989" stop-opacity="0.92"/>
<stop offset="0.886241" stop-color="#FFA96A" stop-opacity="0.57"/>
</radialGradient>
<radialGradient id="paint2_radial_313_113" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(30.5227 81) rotate(-44.4302) scale(34.0382 89.1921)">
<stop stop-color="#F6FB22" stop-opacity="0.51"/>
<stop offset="1" stop-color="#FF9E45" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_313_113" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.8864 39.0625) rotate(135.466) scale(27.1803 116.35)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
