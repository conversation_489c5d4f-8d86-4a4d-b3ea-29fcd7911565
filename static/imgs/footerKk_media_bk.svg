<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
  <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05888 27.9411 0 18 0C8.05888 0 0 8.05888 0 18C0 27.9411 8.05888 36 18 36Z" fill="#FFF500"/>
  <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05888 27.9411 0 18 0C8.05888 0 0 8.05888 0 18C0 27.9411 8.05888 36 18 36Z" fill="url(#paint0_radial_313_45700)"/>
  <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05888 27.9411 0 18 0C8.05888 0 0 8.05888 0 18C0 27.9411 8.05888 36 18 36Z" fill="url(#paint1_radial_313_45700)" fill-opacity="0.9"/>
  <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05888 27.9411 0 18 0C8.05888 0 0 8.05888 0 18C0 27.9411 8.05888 36 18 36Z" fill="url(#paint2_radial_313_45700)" fill-opacity="0.6"/>
  <path d="M18 36C27.9411 36 36 27.9411 36 18C36 8.05888 27.9411 0 18 0C8.05888 0 0 8.05888 0 18C0 27.9411 8.05888 36 18 36Z" fill="url(#paint3_radial_313_45700)" fill-opacity="0.1"/>
  <defs>
    <radialGradient id="paint0_radial_313_45700" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(34.1591 -47.8125) rotate(114.713) scale(79.2592 41.8453)">
      <stop offset="0.67589" stop-color="#FF7A00"/>
      <stop offset="1" stop-color="#FFC700" stop-opacity="0.38"/>
    </radialGradient>
    <radialGradient id="paint1_radial_313_45700" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.8636 -1.28868e-06) rotate(77.7995) scale(42.5869 34.1698)">
      <stop offset="0.212469" stop-color="#FF8989" stop-opacity="0.92"/>
      <stop offset="0.886241" stop-color="#FFA96A" stop-opacity="0.57"/>
    </radialGradient>
    <radialGradient id="paint2_radial_313_45700" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(2.04546 36) rotate(-44.896) scale(19.9236 52.2167)">
      <stop stop-color="#F6FB22" stop-opacity="0.51"/>
      <stop offset="1" stop-color="#FF9E45" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="paint3_radial_313_45700" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(34.7727 11.25) rotate(135) scale(15.9099 68.1143)">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg>