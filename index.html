<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      content="看看账号网官网,游戏账号交易,网络游戏账号交易平台,游戏账号买卖,怎么买号,海口看客网络科技有限公司"
      name="keywords"
    />
    <meta
      content="看看账号网是海口看客网络科技有限公司旗下一个便宜、安全、专业的游戏账号交易平台。在看看账号网交易账号，中介担保，账号估价，平台资深玩家有问必答。"
      name="description"
    />
    <meta content="index,follow" name="robots" />
    <meta content="index,follow" name="GOOGLEBOT" />
    <meta content="看看账号网" name="Author" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <link rel="icon" href="./static/favicon.ico" />
    <title>
      看看账号网游戏账号交易平台_逆水寒手游/逆水寒/原神|王者荣耀|穿越火线手游|元梦之星|买号卖号|永劫无间|剑灵|英雄联盟|王权与自由_看看账号估价游戏平台
    </title>
    <script>
      function handleRedirect() {
        // 手动定义 linkConfig 对象，需根据实际情况修改 linkH5url 的值
        const linkConfig = {
          linkH5url: 'https://m.kkzhw.com',
        };

        // 检查是否为移动设备
        if (
          /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          )
        ) {
          // 获取当前页面的 URL
          const currentUrl = location.href;
          // 检查 URL 中是否包含 'yokoye'
          const hasYokoye = /yokoye/.test(currentUrl);
          // 解构赋值获取 host、pathname 和 search
          const { host, pathname, search } = location;
          // 去除 host 开头的 'www.'
          const sanitizedHost = host.replace(/^www\./, '');

          // 定义重定向规则数组
          const rules = [
            {
              regex: /\/playList/,
              getRedirectUrl: function () {
                const base = hasYokoye
                  ? `https://m.${sanitizedHost}`
                  : linkConfig.linkH5url;
                return `${base}/pages/accountList/accountList${search}`;
              },
            },
            {
              regex: /\/playDetail/,
              getRedirectUrl: function () {
                const base = hasYokoye
                  ? `https://m.${sanitizedHost}`
                  : linkConfig.linkH5url;
                return `${base}/pages/accountDetail/accountDetail${search}`;
              },
            },
            {
              regex: /\/gd/,
              getRedirectUrl: function () {
                const base = hasYokoye
                  ? `https://m.${sanitizedHost}`
                  : linkConfig.linkH5url;
                const newSearch = `?productSn=${pathname.split('/')[2]}`;
                return `${base}/pages/accountDetail/accountDetail${newSearch}`;
              },
            },
          ];

          // 遍历规则数组，检查是否匹配当前 URL
          for (let i = 0; i < rules.length; i++) {
            const rule = rules[i];
            if (rule.regex.test(currentUrl)) {
              window.location.href = rule.getRedirectUrl();
              return;
            }
          }

          // 如果 URL 包含 'yokoye' 且未匹配前面规则，进行重定向
          if (hasYokoye) {
            window.location.href = `https://m.${sanitizedHost}`;
            return;
          }

          // 默认重定向
          window.location.href = linkConfig.linkH5url;
        }
      }
      handleRedirect()
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?9a442d86f0100aa5236d28978f1c6208';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script
      type="text/javascript"
      src="https://cn-shanghai-aliyun-cloudauth.oss-cn-shanghai.aliyuncs.com/web_sdk_js/jsvm_all.js"
    ></script>
    <script>
      // 在调用实人认证服务端发起认证请求时需要传入该MetaInfo值
      var MetaInfo = window.getMetaInfo();
    </script>
  </body>
</html>
